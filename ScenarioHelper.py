from PIL import Image, ImageFilter
import datetime
import time
import os
import inspect
from CommonSettings import CommonSettings

scnt = 0

def testRunSub(filename):

    global scnt
    scnt = scnt + 1

    # print("テスト処理のサブ1 開始")

    print("カウント：" + filename + "-" + str(scnt))

    # print("テスト処理のサブ1 終了")


# 画面最大化
def full_screen(driver):
    driver.fullscreen_window()

# スクリーンショット取得
def save_screenshot(driver, filename):

    global scnt
    scnt = scnt + 1
    imagename = filename + "-" + str(scnt) + '.png'

    # driver.save_screenshot(filename)
    driver.save_screenshot(imagename)
    #print(filename)

# スクリーンショット取得２
def save_screenshot2(driver, filename, fullsize=False):
    driver.save_screenshot(filename)


# =================================================================================================
# スクリーンショットを画面サイズ分取得する
# =================================================================================================
def save_screenshot_IE(driver, filename, fullsize=False):

    settings = CommonSettings()
    global scnt
    #scnt = scnt + 1
    scnt = 0
    # imagename = filename + "-" + str(scnt) + '.png'
    frame = inspect.stack()[1]
    imagename = settings.drive + "/" + settings.path + "/" + filename[:3] + "/evidence/" + os.path.splitext(os.path.basename(inspect.getmodule(frame[0]).__file__))[0] + "/" + filename + "-" + str(scnt) + '.png'
    # imagename2 = filename + "-" + str(scnt) + '.png'
    # imagename = "..//evidence/" + filename + '.png'

    # filepath = '/'.join(filename.split('/')[:-1])
    filepath = '/'.join(imagename.split('/')[:-1])
    # filepath = 'C:/Python/Selenium'
    print("ファイルパス：" + filepath)
    # filepath = filename

    if fullsize:
        # ページの左上までスクロール
        driver.execute_script("window.scrollTo(0, 0);")

        #スクリーンサイズ取得（フルスクリーン前提）
        #screen_height = driver.execute_script("return window.screen.height")
        #screen_width  = driver.execute_script("return window.screen.width")
        screen_height = 768
        screen_width  = 1366

        # ページサイズ取得
        total_height = driver.execute_script("return document.body.scrollHeight")
        total_width  = driver.execute_script("return document.body.scrollWidth")

        # IE用にページサイズがスクリーンサイズ以下の場合に補正
        if total_height < screen_height:
            total_height = screen_height
        if total_width < screen_width:
            total_width = screen_width

        # 画面サイズ取得
        #view_height = driver.execute_script("return window.innerHeight")
        #view_width = driver.execute_script("return window.innerWidth")
        # IEが画面サイズとれない？のでとりあえず固定値
        #view_height = 648
        #view_width = 1419
        #view_height = total_height
        #view_width = total_width
        #view_height = driver.execute_script("return document.body.clientHeight")
        #view_width = driver.execute_script("return document.body.clientWidth")
        #view_height = driver.execute_script("return document.body.clientHeight")
        #view_width = driver.execute_script("return window.screen.width")

        view_height = screen_height
        view_width = screen_width

        print('ファイル名　:' + str(filename))
        print('高さ（合計）:' + str(total_height))
        print('幅　（合計）:' + str(total_width))
        print('高さ（画面）:' + str(view_height))
        print('幅　（画面）:' + str(view_width))

        # 画像処理用
        stitched_image = Image.new("RGB", (total_width, total_height))

        # スクロール操作用
        scroll_width = 0
        scroll_height = 0

        row_count = 0
        # 縦スクロールの処理§
        while scroll_height < total_height:
            # 横スクロール初期化
            col_count = 0
            scroll_width = 0
            driver.execute_script("window.scrollTo(%d, %d)" % (scroll_width, scroll_height)) 
            # 横スクロールの処理
            while scroll_width < total_width:
                scnt += 1
                imagename = settings.drive + "/" + settings.path + "/" + filename[:3] + "/evidence/" + os.path.splitext(os.path.basename(inspect.getmodule(frame[0]).__file__))[0] + "/" + filename + "-" + str(scnt) + '.png'
                if col_count > 0:
                    # 画面サイズ分横スクロール
                    driver.execute_script("window.scrollBy("+str(view_width)+",0)") 

                time.sleep(1)

                # 右端か下端に到達したら画像を切り取ってstitched_imageに貼り付ける
                if scroll_width + view_width >= total_width or scroll_height + view_height >= total_height:
                    new_width = view_width
                    new_height= view_height
                    if scroll_width + view_width >= total_width:
                        new_width = total_width - scroll_width
                    if scroll_height + view_height >= total_height:
                        new_height = total_height - scroll_height - 1
                    driver.save_screenshot(imagename)
                    scroll_width += new_width

                # 普通に貼り付ける
                else:
                    driver.save_screenshot(imagename)
                    scroll_width += view_width
                    col_count += 1

            scroll_height += view_height
            time.sleep(1)

        return True

    # fullsize=Falseの場合は通常のスクリーンショットを取得
    else:
        #driver.get_screenshot_as_file(filename)
        driver.save_screenshot(imagename)
# =================================================================================================