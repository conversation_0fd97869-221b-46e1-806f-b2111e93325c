from selenium.common.exceptions import NoSuchElementException
from selenium.webdriver.common.by import By
from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28020101(KodomoSiteTestCaseBase):
    """TestQAP010_28020101"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28020101(self):
        tab_index = 0
        case_data_008_QAZF100001 = self.test_data["008_QAZF100001"]
        case_data_010_QAZF100002 = self.test_data["010_QAZF100002"]
        case_data_011_QAPF100300 = self.test_data["011_QAPF100300"]
        case_data_014_QAPF103700 = self.test_data["014_QAPF103700"]
        case_data_019_QAPF103700 = self.test_data["019_QAPF103700"]
        case_data_020_QAPF103700 = self.test_data["020_QAPF103700"]
        case_data_022_QAPF103900 = self.test_data["022_QAPF103900"]
        case_data_027_QAPF103700 = self.test_data["027_QAPF103700"]
        case_data_029_QAPF103700 = self.test_data["029_QAPF103700"]
        case_data_031_QAPF103700 = self.test_data["031_QAPF103700"]

        # 1, 2, 3 ,4 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 5 メインメニュー 画面: 「子ども子育て支援」ボタン押下
        # 6 メインメニュー 画面: 「世帯情報」ボタン押下
        # 7 メインメニュー 画面: 「検索」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=True)
        tab_index += 1

        # 8 検索条件入力 画面: 入力できること
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_008_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_008_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_008_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_008_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_008_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_008_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_008_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_008_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_008_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_008_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_008_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_008_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_008_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_008_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_008_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_008_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力_画面_8")

        # 9 検索条件入力 画面: 「検索」ボタンクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 10 世帯履歴 画面: 支給認定情報を登録する世帯台帳履歴の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_010_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code=atena_code, tab_index=tab_index, col_number=1)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳_画面_10")

        # 11 世帯台帳 画面: 支給認定申請登録する児童の「№」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_011_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)

        # 12 児童台帳 画面:「入所管理」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103500", label="入所管理")
        self.screen_shot("児童台帳_画面_12")

        # 13 児童台帳 画面: 「入所申込」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103500_btnNyushoMoushikomi_button")

        # 14 基本情報を入力します: 入力できること
        # 15 入所管理（申込）画面: 希望期間開始を入力します: 入力できること
        # 申込区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoMoshikomiKbn_select",
            text=case_data_014_QAPF103700.get("shinsei_kubun_cbx", ""))
        # 状態区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoJotaiKbn_select",
            text=case_data_014_QAPF103700.get("joutai_kubun_cbx", ""))
        # 受付年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtKisoInfoUketsukeYMD_textboxInput",
            value=case_data_014_QAPF103700.get("uketsuke_ymd", ""))
        # 申請年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtKisoInfoShinseiYMD_textboxInput",
            value=case_data_014_QAPF103700.get("shinsei_ymd", ""))
        # 申請有効期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtKisoInfoShinseiYukoKikan_textboxInput",
            value=case_data_014_QAPF103700.get("shinsei_yuukou_kikan_ymd", ""))
        # 申込受付番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtKisoInfoMoshikomiUketsukeNo_textboxInput",
            value=case_data_014_QAPF103700.get("moushikomi_uketsuke_bango", ""))
        # 希望理由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtKisoInfoKiboRiyu_textboxInput",
            value=case_data_014_QAPF103700.get("kibou_riyuu", ""))
        # 希望期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtKisoInfoKiboKikanStart_textboxInput",
            value=case_data_014_QAPF103700.get("kibou_kikan_ymd_01", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtKisoInfoKiboKikanEnd_textboxInput",
            value=case_data_014_QAPF103700.get("kibou_kikan_ymd_02", ""))
        # その他保育施設利用
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoSoNotaHoikuShisetsuRiyo_select",
            text=case_data_014_QAPF103700.get("sonota_hoiku_shisetsu_riyou_cbx", ""))
        # 変更申請年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtKisoInfoHenkoShinseiYMD_textboxInput",
            value=case_data_014_QAPF103700.get("henkou_shinsei_ymd", ""))
        # 申請事由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoShinseiJiyu_select",
            text=case_data_014_QAPF103700.get("shinsei_jiyuu_cbx", ""))
        # 同時申請
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoDojiShinsei_select",
            text=case_data_014_QAPF103700.get("douji_shinsei_cbx", ""))
        # 受付時間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoUketsukeHour_select",
            text=case_data_014_QAPF103700.get("uketsuke_jikan_cbx_01", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoUketsukeMinute_select",
            text=case_data_014_QAPF103700.get("uketsuke_jikan_cbx_02", ""))
        # 入所形態
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoNyushoKeitai_select",
            text=case_data_014_QAPF103700.get("nyuusho_keitai_cbx", ""))
        # 申請受理区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoShinseiAcceptanceKbn_select",
            text=case_data_014_QAPF103700.get("shinsei_juri_kubun_cbx", ""))
        # 変更事由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoHenkoJiyu_select",
            text=case_data_014_QAPF103700.get("henkou_jiyuu_cbx", ""))
        # 変更理由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoHenkoRiyu_select",
            text=case_data_014_QAPF103700.get("henkou_riyuu_cbx", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selWrGyoseiku_KisoInfoSyokanKu_select",
            text=case_data_014_QAPF103700.get("shokan_ku_cbx", ""))
        # 受付区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selWrGyoseiku_KisoInfoUketsukeKu_select",
            text=case_data_014_QAPF103700.get("uketsuke_ku_cbx", ""))
        # 優先枠
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoYusenWaku_select",
            text=case_data_014_QAPF103700.get("yuusen_waku_cbx", ""))
        # 待機と入所になった場合
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoNyushoFukaTaio_select",
            text=case_data_014_QAPF103700.get("taiki_to_nyuusho_natta_baai_cbx", ""))
        # 別施設入所になった場合
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoSameShisetsuNyushoFukaTaio_select",
            text=case_data_014_QAPF103700.get("betsu_shisetsu_nyuusho_baai_cbx", ""))
        # 申請者氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtKisoInfoShinseiNaiyoShinseishaShimei_textboxInput",
            value=case_data_014_QAPF103700.get("shinseisha_shimei_cbx", ""))
        # 広域給付請求情報
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selKisoInfoKoikikyuFuseikyu_select",
            text=case_data_014_QAPF103700.get("kouiki_kyuufu_seikyu_jouhou_cbx", ""))
        # 特記事項
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_fldKisoInfoTokkijiko_textarea",
            value=case_data_014_QAPF103700.get("tokki_jikou_txt", ""))
        self.screen_shot("入所管理（申込)_画面_14")

        # 16 入所管理（申込）画面:「就」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103700_fldKisoInfoTokkijiko_textarea")
        self.screen_shot("入所管理（申込)_画面_16")

        # 17 入所管理（申込）画面:「希望施設」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103700", label="希望施設")
        self.screen_shot("入所管理（申込)_画面_17")

        # 18 入所管理（申込）画面:「兄弟の設定内容をコピー」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103700_btnKiboShisetsuKyodaiSetteiNaiyoCopy_button")

        # 19 入所管理（申込）画面: コピーしたい対象の「No」ボタン押下
        self.click_no_job_kyoudai_no_settei_naiyou_wo_copy(
            input_params=case_data_019_QAPF103700.get("jidou_shimei", ""),
            tab_index=tab_index)
        self.screen_shot("入所管理（申込)_画面_19")

        # 20 入所管理（申込）画面:「施設コード」を入力します
        self.click_shisetsu_koodo_wo_nyuuryoku(
            input_params=list(case_data_020_QAPF103700.get("shisetsu_kodo", {}).values()), tab_index=tab_index)
        self.screen_shot("入所管理（申込)_画面_20")

        # 21 入所管理（申込）画面:「施設を一括選択」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103700_btnShisetsuSentaku_button")

        # 22 施設検索（複数施設選択）画面: 検索条件を入力
        # 年度
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_selSentakuNendo_select",
            text=case_data_022_QAPF103900.get("nendo_cbx", ""))
        # 施設コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_txtShisetsuCD_textboxInput",
            value=case_data_022_QAPF103900.get("shisetsu_koudo", ""))
        # 事業所番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_txtJigyoshoNo_textboxInput",
            value=case_data_022_QAPF103900.get("jigyousho_bangou", ""))
        # 施設名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_txtShisetsuNM_textboxInput",
            value=case_data_022_QAPF103900.get("shisetsu_meishou_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_selShisetsuNMKensakuJoken_select",
            text=case_data_022_QAPF103900.get("shisetsu_meishou_select", ""))
        # 施設住所
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_txtShisetsuJusho_textboxInput",
            value=case_data_022_QAPF103900.get("shisetsu_jusho_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_selShisetsuJushoKensakuJoken_select",
            text=case_data_022_QAPF103900.get("shisetsu_jusho_select", ""))
        # 施設区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkShisetuKbnchk0",
            value=case_data_022_QAPF103900.get("shisetsu_kubun_nintei_kodomoen_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkShisetuKbnchk1",
            value=case_data_022_QAPF103900.get("shisetsu_kubun_yochien_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkShisetuKbnchk2",
            value=case_data_022_QAPF103900.get("shisetsu_kubun_hoikusho_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkShisetuKbnchk3",
            value=case_data_022_QAPF103900.get("shisetsu_kubun_shoukibo_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkShisetuKbnchk4",
            value=case_data_022_QAPF103900.get("shisetsu_kubun_kateiteki_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkShisetuKbnchk5",
            value=case_data_022_QAPF103900.get("shisetsu_kubun_kyotaku_houmon_gata_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkShisetuKbnchk6",
            value=case_data_022_QAPF103900.get("shisetsu_kubun_jigyousho_nai_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkShisetuKbnchk7",
            value=case_data_022_QAPF103900.get("shisetsu_kubun_ninshou_hoikusho_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkShisetuKbnchk8",
            value=case_data_022_QAPF103900.get("shisetsu_kubun_ninkagai_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkShisetuKbnchk9",
            value=case_data_022_QAPF103900.get("shisetsu_kubun_sonota_chk", ""))
        # 区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkKbnchk0",
            value=case_data_022_QAPF103900.get("kubun_kouritsu_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkKbnchk1",
            value=case_data_022_QAPF103900.get("kubun_shiritsu_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkKbnchk2",
            value=case_data_022_QAPF103900.get("kubun_hekichi_chk", ""))
        # その他
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103900_chkSoNotachk0",
            value=case_data_022_QAPF103900.get("sonota_sentakuzumi_shisetsu_wa_nozoku_chk", ""))
        # 所在区 checkbox
        self.click_shozai_ku_checkbox(input_params=case_data_022_QAPF103900.get("shozai_ku", {}).values(),
                                      tab_index=tab_index)
        self.screen_shot("施設検索（複数施設選択)_画面_22")

        # 23 施設検索（複数施設選択）画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103900_btnKensaku_button")

        # 24 施設検索（複数施設選択）画面: 対象の「No.」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZZZ000000_btnChushutsuNoButton_1_1_button")
        self.screen_shot("施設検索（複数施設選択)_画面_24")

        # 25 施設検索（複数施設選択）画面:「選択決定」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103900_btnSentakuKakuteiButton_button")

        # 26 入所管理（申込）画面:「選考基準」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103700", label="選考基準")

        # 27 入所管理（申込）画面: 各世帯員の選考基準の指数情報を入力します
        # 指数年度
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selSenkoKijyunSisuNendo_select",
            text=case_data_027_QAPF103700.get("shisuu_nendo", ""))
        # 父
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel1_1_2_select",
            text=case_data_027_QAPF103700.get("chichi_reberu_1_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel2_1_4_select",
            text=case_data_027_QAPF103700.get("chichi_reberu_2_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel3_1_6_select",
            text=case_data_027_QAPF103700.get("chichi_reberu_3_cbx", ""))
        # 母
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel1_2_2_select",
            text=case_data_027_QAPF103700.get("haha_reberu_1_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel2_2_4_select",
            text=case_data_027_QAPF103700.get("haha_reberu_2_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel3_2_6_select",
            text=case_data_027_QAPF103700.get("haha_reberu_3_cbx", ""))

        # 祖父
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel1_3_2_select",
            text=case_data_027_QAPF103700.get("sofu_reberu_1_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel2_3_4_select",
            text=case_data_027_QAPF103700.get("sofu_reberu_2_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel3_3_6_select",
            text=case_data_027_QAPF103700.get("sofu_reberu_3_cbx", ""))
        # 祖母
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel1_4_2_select",
            text=case_data_027_QAPF103700.get("sobo_reberu_1_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel2_4_4_select",
            text=case_data_027_QAPF103700.get("sobo_reberu_2_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKijyunLevel3_4_6_select",
            text=case_data_027_QAPF103700.get("sobo_reberu_3_cbx", ""))
        self.screen_shot("入所管理（申込)_画面_27")

        # 28 入所管理（申込）画面:「選考基準」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103700", label="調整指数")

        # 29 入所管理（申込）画面: 該当する調整指数にチェックを入れる
        self.click_chousei_shisuu_checkbox(input_params=case_data_029_QAPF103700.values(), tab_index=tab_index)
        self.screen_shot("入所管理（申込)_画面_29")

        # 30 入所管理（申込）画面:「児童加算」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103700", label="児童加算")

        # 31 入所管理（申込）画面: 該当する児童加算にチェックを入れる
        self.click_jidou_kasan_checkbox(input_params=case_data_031_QAPF103700.values(), tab_index=tab_index)
        self.screen_shot("入所管理（申込)_画面_31")

        # 32 入所管理（申込）画面:「同点時の優先項目」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103700", label="同点時の優先項目")

        # 33 入所管理（申込）画面: 必要な項目を選択します
        self.screen_shot("入所管理（申込)_画面_33")

        # 34 入所管理（申込）画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103700_regbtn_button")
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF103700_msg_span",
                                 msg="登録しました。")
        self.screen_shot("入所管理（申込)_画面_34")

    def click_no_job_kyoudai_no_settei_naiyou_wo_copy(self, input_params, tab_index=1):
        tr_elem = self.find_elements_by_css_selector(
            "#tab0" + str(tab_index) + "_ZZZ000000_tblQAPF1066001 > tbody > tr")
        found = False
        for elem in tr_elem:
            try:
                td_elem_no = elem.find_element(By.CSS_SELECTOR, "td.ui-valign-middle.ui_wrtable_border_left")
                text_no = td_elem_no.text

                td_elem_jidou_shimei = elem.find_element(By.CSS_SELECTOR, "td:nth-child(2)")
                jidou_shimei_div = td_elem_jidou_shimei.find_element(By.CSS_SELECTOR, "div.ui_wrlabel_gaiji")
                text_jidou_shimei = jidou_shimei_div.text.strip()

                if input_params == text_jidou_shimei:
                    self.click_by_id("tab0" + str(tab_index) + "_ZZZ000000_btnJidoNo_" + str(text_no) + "_1_button")
                    found = True
                    break
            except NoSuchElementException:
                continue

        if not found:
            self.click_by_id("tab0" + str(tab_index) + "_QAPF106600_btnClose")

    def click_shozai_ku_checkbox(self, input_params, tab_index=1):
        tr_elem = self.find_elements_by_css_selector(
            "#tab0" + str(tab_index) + "_ZZZ000000_tblQAPF103902 > tbody > tr")
        for row_index, elem in enumerate(tr_elem):
            if row_index == 0:  # remove first empty row
                continue
            try:
                td_elem_shozai_ku = elem.find_element(By.CSS_SELECTOR, "td:nth-child(2)")
                text_shozai_ku = td_elem_shozai_ku.text.strip()

                if text_shozai_ku in input_params:
                    self.form_input_by_id(
                        idstr="tab0" + str(tab_index) + "_ZZZ000000_chkKensakuJokenShokanku_" + str(
                            row_index) + "_1chk0",
                        value="1")
            except NoSuchElementException:
                continue

    def click_chousei_shisuu_checkbox(self, input_params, tab_index=1):
        index_item_param = 0
        index_chousei_shisuu = 1
        row_index = 1
        while True:
            tr_elem = self.find_elements_by_css_selector(
                "#tab0" + str(tab_index) + "_ZZZ000000_tblQAPF1037004 > tbody > tr")
            if row_index >= len(tr_elem):
                break
            elem = tr_elem[row_index]
            row_index += 1
            try:
                td_elem_kubun = elem.find_element(By.CSS_SELECTOR, "td:nth-child(2)")
                text_kubun = td_elem_kubun.text.strip()
                if text_kubun in input_params:
                    self.form_input_by_id(
                        idstr="tab0" + str(tab_index) + "_ZZZ000000_chkChoseiShisuGaito_" + str(
                            index_chousei_shisuu) + "_1chk0",
                        value="1")
                    index_item_param += 1
                if text_kubun and text_kubun != "": index_chousei_shisuu += 1
            except NoSuchElementException:
                continue
            # return when params are all set
            if (index_item_param == len(input_params)):
                return

    def click_jidou_kasan_checkbox(self, input_params, tab_index=1):
        index_item_param = 0
        index_jidou_kasan = 1
        row_index = 1
        while True:
            tr_elem = self.find_elements_by_css_selector(
                "#tab0" + str(tab_index) + "_ZZZ000000_tblQAPF1037005 > tbody > tr")
            if row_index >= len(tr_elem):
                break
            elem = tr_elem[row_index]
            row_index += 1
            try:
                td_elem_kubun = elem.find_element(By.CSS_SELECTOR, "td:nth-child(2)")
                text_kubun = td_elem_kubun.text.strip()
                if text_kubun in input_params:
                    self.form_input_by_id(
                        idstr="tab0" + str(tab_index) + "_ZZZ000000_chkJidoAddGaito_" + str(
                            index_jidou_kasan) + "_1chk0",
                        value="1")
                    index_item_param += 1
                if text_kubun and text_kubun != "": index_jidou_kasan += 1
            except NoSuchElementException:
                continue
            # return when params are all set
            if (index_item_param == len(input_params)):
                return

    def click_shisetsu_koodo_wo_nyuuryoku(self, input_params, tab_index=1):
        index_item_param = 0
        if input_params is None or len(input_params) == 0:
            return

        pages = self.find_element_by_xpath(
            '//*[@id="tab0' + str(tab_index) + '_ZZZ000000_tblQAPF1037002_spanTotalPage"]').text
        index_shisetsu_koodo = 1
        for page in range(int(pages)):
            row_index = 1
            while True:
                tr_elem = self.find_elements_by_css_selector(
                    "#tab0" + str(tab_index) + "_ZZZ000000_tblQAPF1037002 > tbody > tr")
                if row_index >= len(tr_elem):
                    break

                elem = tr_elem[row_index]

                td_elem_shisetsu_koodo = elem.find_element(By.CSS_SELECTOR, "td:nth-child(2)")
                input_shisetsu_koodo = td_elem_shisetsu_koodo.find_element(By.TAG_NAME, "input")
                text_shisetsu_koodo = input_shisetsu_koodo.get_attribute("value").strip()

                if text_shisetsu_koodo == "" and index_item_param < len(input_params):
                    self.form_input_by_id(
                        idstr="tab0" + str(tab_index) + "_ZZZ000000_lblKiboShisetsuShisetsuCD_" + str(
                            index_shisetsu_koodo) + "_2_textboxInput", value=input_params[index_item_param])
                    index_item_param += 1
                row_index += 1
                index_shisetsu_koodo += 1

            # return when params are all set
            if (index_item_param == len(input_params)):
                return
            # Click next page
            nextPageBtn = self.find_element_by_xpath(
                '//*[@id="tab0' + str(tab_index) + '_ZZZ000000_tblQAPF1037002_aftBtn_button"]')
            if nextPageBtn.is_displayed():
                nextPageBtn.click()
