import time
import unittest
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAP010_28070708(KodomoSiteTestCaseBase):
    """TestQAP010_28070708"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()
    
    # 不納欠損の登録ができることを確認する。
    def test_QAP010_28070708(self):
        """不納欠損登録"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28070708")

        self.do_login()
        # 2 不納欠損一括登録: None
        
        # 3 メインメニュー画面: 「バッチ管理」クリック
        self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)
        
        # 4 メインメニュー画面: 「即時実行」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)
        
        # 5 メインメニュー画面: 「スケジュール個別追加」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        
        # 6 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_5")
        
        # 7 スケジュール個別追加画面: 業務名：収納・滞納 サブシステム名：日次 処理名：日次消込処理 処理区分：
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuGyomuNM_select"),"収納・滞納")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuSubSystemNM_select"),"児童")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuShoriNM_select"),"日次消込処理")
        
        # 8 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 9 スケジュール個別追加画面: 「(JAABN06400) 日次消込本処理」Noボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_BtnMode32_3_4_button").click()
        
        # 10 実行指示画面: 表示
        self.screen_shot("実行指示画面_9")
        
        # 11 実行指示画面: 取扱業務：（パラメータ化）　を選択
        # Assert: 後続テストのための事前準備として処理を実施
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF002200_groupingKBN_select"),self.test_data.get("qap010_28070708_groupingKBN1",""))
        self.screen_shot("実行指示画面_10")
        
        # 12 実行指示画面: 「実行」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF002200_executebtn_button").click()
        
        # 13 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.screen_shot("実行結果管理画面_12")
        
        # 14 実行結果管理画面: 正常終了まで5分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.screen_shot("実行結果管理画面_13")
        
        # 15 実行結果管理画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 16 スケジュール個別追加画面: 表示
        self.screen_shot("スケジュール個別追加画面_15")
        
        # 17 スケジュール個別追加画面: 業務名：宛名情報管理 サブシステム名：宛名 処理名：宛名・口座バッチマスタ作成処理 処理区分：
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuGyomuNM_select"),"宛名情報管理")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuSubSystemNM_select"),"宛名")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuShoriNM_select"),"宛名・口座バッチマスタ作成処理")
        
        # 18 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 19 スケジュール個別追加画面: 「 (FAABN00400) 宛名・口座バッチマスタ作成処理 」Noボタンクリック
        # Assert: 後続テストのための事前準備として処理を実施
        self.find_element(By.ID,"tab01_ZZZ000000_BtnMode32_1_4_button").click()
        
        # 20 実行指示画面: 表示
        self.screen_shot("実行指示画面_19")
        
        # 21 実行指示画面: 業務名〇：初期値
        
        # 22 実行指示画面: 業務別基準日〇：初期値
        
        # 23 実行指示画面: 口座基準日〇：初期値
        self.screen_shot("実行指示画面_22")
        
        # 24 実行指示画面: 「実行」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF002200_executebtn_button").click()
        
        # 25 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.screen_shot("実行結果管理画面_24")
        
        # 26 実行結果管理画面: 正常終了まで10分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.screen_shot("実行結果管理画面_25")
        
        # 27 実行結果管理画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 28 スケジュール個別追加画面: 表示
        
        # 29 スケジュール個別追加画面: 業務名：収納・滞納サブシステム名：マスタ作成処理名：収納マスタ＆収納用宛名口座マスタ作成処理（統合版）処理区分：
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuGyomuNM_select"),"収納・滞納")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuSubSystemNM_select"),"児童")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuShoriNM_select"),"収納マスタ＆収納用宛名口座マスタ作成処理（統合版）")
        
        # 30 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 31 スケジュール個別追加画面: 「(JAZBN10100) 収納マスタ＆収納用宛名口座マスタ作成本処理(統合版) 」Noボタンクリック
        # Assert: 後続テストのための事前準備として処理を実施
        self.find_element(By.ID,"tab01_ZZZ000000_BtnMode32_1_4_button").click()
        
        # 32 実行指示画面: 表示
        self.screen_shot("実行指示画面_31")
        
        # 33 実行指示画面: 未納指定：全件　を選択
        # Assert: 記載のないパラメータは全て初期値を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF002200_JAZBN00400_minoushitei_select"),self.test_data.get("qap010_28070708_minoshitei",""))
        self.screen_shot("実行指示画面_32")
        
        # 34 実行指示画面: 「実行」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF002200_executebtn_button").click()
        
        # 35 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.screen_shot("実行結果管理画面_34")
        
        # 36 実行結果管理画面: 正常終了まで10分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.screen_shot("実行結果管理画面_35")
        
        # 37 実行結果管理画面: パンくず「スケジュール個別追加」クリック
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        
        # 38 スケジュール個別追加画面: 表示
        
        # 39 スケジュール個別追加画面: 業務名：収納・滞納 サブシステム名：年次 処理名：不納欠損処理 処理区分：
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuGyomuNM_select"),"収納・滞納")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuSubSystemNM_select"),"児童")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuShoriNM_select"),"不納欠損処理")
        
        # 40 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF000400_BtnKensaku_button").click()
        self.screen_shot("スケジュール個別追加画面_39")
        
        # 41 スケジュール個別追加画面: 「(JADBN00600) 不納欠損本処理」Noボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_BtnMode32_1_4_button").click()
        
        # 42 実行結果画面: 表示
        
        # 43 実行結果画面: 科目：（パラメータ化）　を選択 時効予定日（開始）：（パラメータ化）　を選択時効予定日（終了）：（パラメータ化）　を選択欠損日：（パラメータ化）　を選択
        # 科目
        self.find_element(By.ID,self.test_data.get("qap010_28070708_kamoku","")).click()
        
        # 時効予定日（開始）
        self.find_element(By.ID,"tab01_ZEAF002200_jikouYoteibiKaishi_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_ZEAF002200_jikouYoteibiKaishi_textboxInput").send_keys(self.test_data.get("qap010_28070708_jikouYoteibiKaishi",""))
        
        # 時効予定日（終了）
        self.find_element(By.ID,"tab01_ZEAF002200_jikouYoteibiShuryo_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_ZEAF002200_jikouYoteibiShuryo_textboxInput").send_keys(self.test_data.get("qap010_28070708_jikouYoteibiShuryo",""))
        
        # 欠損日
        self.find_element(By.ID,"tab01_ZEAF002200_kessonbi_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_ZEAF002200_kessonbi_textboxInput").send_keys(self.test_data.get("qap010_28070708_kessonbi",""))
        
        self.screen_shot("実行結果画面_42")
        
        # 44 実行指示画面: 「実行」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF002200_executebtn_button").click()
        
        # 45 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.screen_shot("実行結果管理画面_44")
        
        # 46 実行結果管理画面: 正常終了まで10分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.screen_shot("実行結果管理画面_45")
        
        # 47 メインメニュー画面: 表示
        time.sleep(1)
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.screen_shot("メインメニュー画面_46")
        
        # 48 メインメニュー画面: 「バッチ管理」クリック
        # 「バッチ管理」は既に開いているのでクリック不要
        # self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        # time.sleep(1)
        
        # 49 メインメニュー画面: 「結果管理」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'結果管理')]/span").click()
        time.sleep(1)
        
        # 50 メインメニュー画面: 「納品物管理」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'結果管理')]/ul/li[contains(span,'納品物確認')]/span")
        self.actionChains.double_click(searchBtn).perform()
        
        # 51 納品物管理画面: 表示
        
        # 52 納品物管理画面: 業務名：収納・滞納 サブシステム名：年次 処理名：不納欠損処理 処理区分：
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF002700_SelKensakuGyomuNM_select"),"収納・滞納")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF002700_SelKensakuSubSystemNM_select"),"児童")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF002700_SelKensakuSyoriNM_select"),"不納欠損処理")
        
        # 53 納品物管理画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF002700_BtnKensaku_button").click()
        self.screen_shot("納品物管理画面_52")
        
        # 54 納品物管理画面: 「JADP801930_不納欠損対象集計表(料用).pdf 」の「ダウンロード」ボタンクリック
        # 55 ファイルダウンロード確認ダイアログ: 表示
        # self.screen_shot("ファイルダウンロード確認ダイアログ_55")
        # 56 ファイルダウンロード確認ダイアログ: 「JADP801930_不納欠損対象集計表(料用).pdf」のNoボタンクリック
        # 57 ファイルダウンロード確認ダイアログ: 「ファイルを開く」ボタンクリック
        # 58 PDF「JADP801930_不納欠損対象集計表(料用).pdf」: 表示
        # self.screen_shot("PDF「JADP801930_不納欠損対象集計表(料用).pdf」_58")
        # self.pdf_download("JADP801930_不納欠損対象集計表(料用).pdf", "PDF「JADP801930_不納欠損対象集計表(料用).pdf」_57")
        
        # 59 PDF「JADP801930_不納欠損対象集計表(料用).pdf」: 閉じるボタンクリック
        # 	self.click_button_by_label("閉じる")
        
        # 60 納品物管理画面: 「不納欠損対象集計表(料用).CSV」の「ダウンロード」ボタンクリック
        # 61 ファイルダウンロード確認ダイアログ: 表示
        # self.screen_shot("ファイルダウンロード確認ダイアログ_61")
        # 62 ファイルダウンロード確認ダイアログ: 「不納欠損対象集計表(料用).CSV」のNoボタンクリック
        # 63 ファイルダウンロード確認ダイアログ: 「ファイルを開く」ボタンクリック
        # 64 CSV「不納欠損対象集計表(料用).CSV」: 表示
        # self.screen_shot("CSV「不納欠損対象集計表(料用).CSV」_64")
        self.pdf_download("不納欠損対象集計表(料用).CSV", "CSV「不納欠損対象集計表(料用).CSV」_57")
        
        # 65 CSV「不納欠損対象集計表(料用).CSV」: 閉じるボタンクリック
        self.click_button_by_label("閉じる")
        
        # 66 納品物管理画面: 「不納欠損更新結果リスト.CSV」の「ダウンロード」ボタンクリック
        # 67 ファイルダウンロード確認ダイアログ: 表示
        # self.screen_shot("ファイルダウンロード確認ダイアログ_67")
        # 68 ファイルダウンロード確認ダイアログ: 「不納欠損更新結果リスト.CSV」のNoボタンクリック
        # 69 ファイルダウンロード確認ダイアログ: 「ファイルを開く」ボタンクリック
        # 70 CSV「不納欠損更新結果リスト.CSV」: 表示
        # self.screen_shot("CSV「不納欠損更新結果リスト.CSV」_70")
        self.pdf_download("不納欠損更新結果リスト.CSV", "CSV「不納欠損更新結果リスト.CSV」_63")
        
        # 71 CSV「不納欠損更新結果リスト.CSV」: 閉じるボタンクリック
        self.click_button_by_label("閉じる")
        
        # 72 メインメニュー画面: 表示
        time.sleep(1)
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.screen_shot("メインメニュー画面_65")
        
        # 73 メインメニュー画面: 「バッチ管理」クリック
        # 「バッチ管理」は既に開いているのでクリック不要
        # self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        # time.sleep(1)
        
        # 74 メインメニュー画面: 「即時実行」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)
        
        # 75 メインメニュー画面: 「スケジュール個別追加」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchBtn).perform()
        
        # 76 スケジュール個別追加画面: 表示
        
        # 77 スケジュール個別追加画面: 業務名：収納・滞納 サブシステム名：年次 処理名：不納欠損処理 処理区分：
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuGyomuNM_select"),"収納・滞納")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuSubSystemNM_select"),"児童")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuShoriNM_select"),"不納欠損処理")
        
        # 78 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF000400_BtnKensaku_button").click()
        self.screen_shot("スケジュール個別追加画面_71")
        
        # 79 スケジュール個別追加画面: 「(JADBN01000) 不納欠損後処理」Noボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_BtnMode32_2_4_button").click()
        self.screen_shot("スケジュール個別追加画面_72")
        
        # 80 実行指示画面: 「実行」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF002200_executebtn_button").click()
        
        # 81 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.screen_shot("実行結果管理画面_74")
        
        # 82 実行結果管理画面: 正常終了まで10分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.screen_shot("実行結果管理画面_75")
        
        # 83 不納欠損個別登録: None
        
        # 84 メインメニュー画面: 「バッチ管理」クリック
        # self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        # time.sleep(1)
        
        # 85 メインメニュー画面: 「即時実行」クリック
        # self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        # time.sleep(1)
        
        # 86 メインメニュー画面: 「スケジュール個別追加」ダブルクリック
        # searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        # self.actionChains.double_click(searchBtn).perform()
        
        # 87 スケジュール個別追加画面: 表示
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002300_navi']/li[1]/a").click()
        self.screen_shot("スケジュール個別追加画面_80")
        
        # 88 スケジュール個別追加画面: 業務名：収納・滞納 サブシステム名：日次 処理名：日次消込処理 処理区分：
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuGyomuNM_select"),"収納・滞納")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuSubSystemNM_select"),"児童")
        
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF000400_SelKensakuShoriNM_select"),"日次消込処理")
        
        # 89 スケジュール個別追加画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF000400_BtnKensaku_button").click()
        
        # 90 スケジュール個別追加画面: 「(JAABN06400) 日次消込本処理」Noボタンクリック
        self.find_element(By.ID,"tab01_ZZZ000000_BtnMode32_3_4_button").click()
        
        # 91 実行指示画面: 表示
        self.screen_shot("実行指示画面_84")
        
        # 92 実行指示画面: 取扱業務：（パラメータ化）　を選択
        # Assert: 後続テストのための事前準備として処理を実施
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_ZEAF002200_groupingKBN_select"),self.test_data.get("qap010_28070708_groupingKBN2",""))
        self.screen_shot("実行指示画面_85")
        
        # 93 実行指示画面: 「実行」ボタンクリック
        self.find_element(By.ID,"tab01_ZEAF002200_executebtn_button").click()
        
        # 94 実行結果管理画面: 表示
        # Assert: 「実行しました。」のメッセージのチェック
        self.screen_shot("実行結果管理画面_87")
        
        # 95 実行結果管理画面: 正常終了まで5分置きに「実行」ボタンクリック
        self.wait_job_finished_kodomo(3600, 20)
        self.screen_shot("実行結果管理画面_88")
        
        # 96 メインメニュー画面: 表示
        time.sleep(1)
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        self.screen_shot("メインメニュー画面_89")
        
        # 97 メインメニュー画面: 「収納」クリック
        self.find_element_by_xpath("//*[@id='rootmenu_0']/li[contains(span,'収納')]/span").click()
        time.sleep(1)
        
        # 98 メインメニュー画面: 「個人別収納状況」クリック
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'個人別収納状況')]/span").click()
        time.sleep(1)
        
        # 99 メインメニュー画面: 「個人別収納状況」ダブルクリック
        searchBtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'収納')]/ul/li[contains(span,'個人別収納状況')]/ul/li[contains(span,'個人別収納状況')]/span")
        self.actionChains.double_click(searchBtn).perform()
        
        # 100 （個人・法人）検索条件入力画面: 表示
        self.screen_shot("（個人・法人）検索条件入力画面_93")
        
        # 101 （個人・法人）検索条件入力画面: 住民コード：（パラメータ化）
        self.find_element(By.ID,"tab01_JAAF400100_txtJuminCD_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_JAAF400100_txtJuminCD_textboxInput").send_keys(self.test_data.get("qap010_28070708_txtJuminCD",""))
        
        # 102 （個人・法人）検索条件入力画面: 「検索」ボタンクリック
        self.find_element(By.ID,"tab01_JAAF400100_WrCmnBtn05_button").click()
        
        # 103 （個人・法人）管理情報照会画面: 表示
        self.screen_shot("（個人・法人）管理情報照会画面_96")
        
        # 104 （個人・法人）管理情報照会画面: 「処分一覧」ボタンクリック
        self.find_element(By.ID,"tab01_JAAF400300_btnShobunIchiran_button").click()
        
        # 105 処分一覧画面: 表示
        self.screen_shot("処分一覧画面_98")
        
        # 106 処分一覧画面: 処分種類：不納欠損
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JADF000100_selShobunShurui_select"),"不納欠損")
        
        # 107 処分一覧画面: 「新規」ボタンクリック
        self.find_element(By.ID,"tab01_JADF000100_btnShinkiToroku_button").click()
        
        # 108 不納欠損管理画面: 表示
        self.screen_shot("不納欠損管理画面_101")
        
        # 109 不納欠損管理画面：不納欠損タブ: 不納欠損日：（パラメータ化）　を入力
        self.find_element(By.ID,"tab01_JADF800600_lblFunoKessonShoribi_textboxInput").send_keys("")
        self.find_element(By.ID,"tab01_JADF800600_lblFunoKessonShoribi_textboxInput").send_keys(self.test_data.get("qap010_28070708_lblFunoKessonShoribi",""))
        
        # 110 不納欠損管理画面：不納欠損タブ: 不納欠損理由：時効消滅　を選択
        self.select_Option(self.driver,self.find_element(By.ID,"tab01_JADF800600_selFuNoKessonRiyu_select"),"時効消滅")
        
        # 111 不納欠損管理画面：不納欠損タブ: 備考：テスト登録　を選択
        self.find_element(By.ID,"tab01_JADF800600_fldBiko_textarea").send_keys("")
        self.find_element(By.ID,"tab01_JADF800600_fldBiko_textarea").send_keys("テスト登録")
        
        # 112 不納欠損管理画面：不納欠損タブ: 「期別明細」タブクリック
        self.find_element(By.ID,"tab01_JADF800600_answer_li").click()
        
        # 113 不納欠損管理画面：期別明細タブ: 表示
        self.screen_shot("不納欠損管理画面：期別明細タブ_106")
        
        # 114 不納欠損管理画面：期別明細タブ: 「期別選択」ボタンクリック
        self.find_element(By.ID,"tab01_JADF800600_btnKibetsuChoice_button").click()
        
        # 115 期別選択画面: 表示
        self.screen_shot("期別選択画面_108")
        
        # 116 期別選択画面: 「全選択」ボタンクリック
        self.find_element(By.ID,"tab01_JADF000200_btnKibetsuALLSentaku_button").click()
        
        # 117 期別選択画面: 「決定」ボタンクリック
        self.find_element(By.ID,"tab01_JADF000200_btnKettei_button").click()
        
        # 118 不納欠損管理画面：期別明細タブ: 表示
        self.screen_shot("不納欠損管理画面：期別明細タブ_111")
        
        # 119 不納欠損管理画面：期別明細タブ: 「経過登録」ボタンクリック
        self.find_element(By.ID,"tab01_JADF800600_btnKeikaToroku_button").click()
        
        # 120 共通経過登録確認ダイアログ: 表示
        self.screen_shot("共通経過登録確認ダイアログ_113")
        
        # 121 共通経過登録確認ダイアログ: 「はい」ボタンクリック
        self.find_element(By.ID,"tempId__1").click()
        
        # 122 共通経過入力画面: 表示
        self.screen_shot("共通経過入力画面_115")
        
        # 123 共通経過入力画面: 「決定」ボタンクリック
        self.find_element(By.ID,"tab01_JADF200500_btnKettei_button").click()
        
        # 124 不納欠損管理画面: 表示
        self.screen_shot("不納欠損管理画面_117")
        
        # 125 不納欠損管理画面: 「登録」ボタンクリック
        self.find_element(By.ID,"tab01_JADF800600_regbtn_button").click()
        
        # 126 不納欠損管理画面: 表示
        self.screen_shot("不納欠損管理画面_119")
        
        # 127 不納欠損管理画面: 「はい」ボタンクリック
        self.find_element(By.ID,"tempId__3").click()
        
        
        # 128 不納欠損管理画面: 表示
        # Assert: 「登録しました。」のメッセージのチェック
        self.screen_shot("不納欠損管理画面_121")
        
