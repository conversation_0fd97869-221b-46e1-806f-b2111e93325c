from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28000104(KodomoSiteTestCaseBase):
    """TestQAP010_28000104"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28000104(self):
        case_data_098_QAZF100001 = self.test_data["098_QAZF100001"]
        case_data_100_QAZF100002 = self.test_data["100_QAZF100002"]
        case_data_103_FADF100300 = self.test_data["103_FADF100300"]
        case_data_101_FADF100300 = self.test_data["101_FADF100300"]
        case_data_106_FADF100400 = self.test_data["106_FADF100400"]
        case_data_111_FAZF000410 = self.test_data["111_FAZF000410"]
        case_data_112_FAZF000210 = self.test_data["112_FAZF000210"]
        case_data_113_FADF100300 = self.test_data["113_FADF100300"]
        case_data_116_FADF100400 = self.test_data["116_FADF100400"]
        tab_index = 0

        self.do_login_new_tab()

        # 95 メインメニュー 画面: 「子ども子育て支援」ボタン押下
        # 96 メインメニュー 画面: 「世帯情報」ボタン押下
        # 97 メインメニュー 画面: 「検索」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=True)
        tab_index += 1

        # 98 検索条件入力 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_098_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_098_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_098_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_098_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_098_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_098_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_098_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_098_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_098_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_098_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_098_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_098_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_098_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_098_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_098_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_098_QAZF100001.get("denwa_bangou_ichi", ""))

        self.screen_shot("検索条件入力 画面_98")

        # 99 検索条件入力 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 100 世帯履歴 画面: 対象の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_100_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code, tab_index)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳 画面_100")

        # 101 世帯台帳 画面: 対象の「№」ボタン押下
        self.click_kodomo_by_atena_code(case_data_101_FADF100300.get("kodomo_atena_code", ""))
        self.screen_shot("児童台帳 画面_101")

        # 102 児童台帳 画面: 「口座情報」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103500_btnKozaInfo_button")

        # 103 口座照会 画面: 「用途」プルダウンを選択する
        # 用途
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100300_selYoto_select",
            text=case_data_103_FADF100300.get("yoto_1", ""))
        self.screen_shot("口座照会 画面_103")

        # 104 口座照会 画面: 「選択」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_FADF100300_btnSentaku_button")

        # 105 口座照会 画面: 「追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_FADF100400_btnAddChg_button")

        # 106 口座照会 画面: 必要な情報を入力する
        # 異動日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtIDobi_textboxInput",
            value=case_data_106_FADF100400.get("idobi", ""))
        # 公開/非公開
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selKokaiHikokai_select",
            text=case_data_106_FADF100400.get("kokai_hikokai", ""))
        # 金融機関種別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selKinyukikanSB_select",
            text=case_data_106_FADF100400.get("kinyu_kikan_shubetsu", ""))
        # 送金方法
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selSoukinKbn_select",
            text=case_data_106_FADF100400.get("soukin_hoho", ""))
        # 金融機関名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtGinkoCD_textboxInput",
            value=case_data_106_FADF100400.get("kinyu_kikan_mei_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtShitenCD_textboxInput",
            value=case_data_106_FADF100400.get("kinyu_kikan_mei_2", ""))
        # 預金科目
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selYokinKamoku_select",
            text=case_data_106_FADF100400.get("yokin_kamoku", ""))
        # 口座番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaNo_textboxInput",
            value=case_data_106_FADF100400.get("koza_bango", ""))
        # 口座名義人カナ
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMeigiKanaShimei_textboxInput",
            value=case_data_106_FADF100400.get("koza_meiginin_kana", ""))
        # 預金者電話番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtTEL_textboxInput",
            value=case_data_106_FADF100400.get("yokinsha_denwa_bango", ""))
        # 口座申込日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMoshikomibi_textboxInput",
            value=case_data_106_FADF100400.get("koza_moshikomibi", ""))
        # 口座名義人漢字
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMeigiKanjiShimeiNM_textboxInput",
            value=case_data_106_FADF100400.get("koza_meiginin_kanji", ""))
        # 有効期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtYukoKikanFrom_textboxInput",
            value=case_data_106_FADF100400.get("yuko_kikan_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtYukoKikanTo_textboxInput",
            value=case_data_106_FADF100400.get("yuko_kikan_to", ""))
        # 口座停止期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaTeishiKikanFrom_textboxInput",
            value=case_data_106_FADF100400.get("koza_teishi_kikan_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaTeishiKikanTo_textboxInput",
            value=case_data_106_FADF100400.get("koza_teishi_kikan_to", ""))
        # 納付区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selZennoKbn_select",
            text=case_data_106_FADF100400.get("nofu_kubun", ""))

        self.screen_shot("口座管理 画面_106")

        # 107 口座照会 画面: 「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_FADF100400_regbtn_button")

        # 108 口座照会 画面: 「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_FADF100400_msg_span", msg="登録しました。")
        self.screen_shot("口座管理 画面_108")

        # 109 メインメニュー 画面: メインメニューから「宛名情報管理」クリック
        # 110 メインメニュー 画面: 「口座情報」ダブルクリック
        self._goto_menu_by_label(menu_level_1="宛名情報管理", menu_level_2="口座情報")
        tab_index += 1

        # 111 検索条件入力 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名・名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtKanaShimeiNM_textboxInput",
            value=case_data_111_FAZF000410.get("kana_shimei_meisho_inp", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_selAimaiKana_select",
            text=case_data_111_FAZF000410.get("kana_shimei_meisho_sel", ""))
        # 漢字氏名・名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtKanjiShimeiNM_textboxInput",
            value=case_data_111_FAZF000410.get("kanji_shimei_meisho_inp", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_selAimaiKanji_select",
            text=case_data_111_FAZF000410.get("kanji_shimei_meisho_sel", ""))
        # カナ氏名・名称２
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtKanaShimeiNM2_textboxInput",
            value=case_data_111_FAZF000410.get("kana_shimei_meisho_inp_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_selAimaiKana2_select",
            text=case_data_111_FAZF000410.get("kana_shimei_meisho_sel_2", ""))
        # 漢字氏名・名称２
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtKanjiShimeiNM2_textboxInput",
            value=case_data_111_FAZF000410.get("kanji_shimei_meisho_inp_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_selAimaiKanji2_select",
            text=case_data_111_FAZF000410.get("kanji_shimei_meisho_sel_2", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtDate01Left_textboxInput",
            value=case_data_111_FAZF000410.get("seinengappi_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_selAimaiBirth1_select",
            text=case_data_111_FAZF000410.get("seinengappi_sel", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtDate02Left_textboxInput",
            value=case_data_111_FAZF000410.get("seinengappi_to", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_selSeibetsu_select",
            text=case_data_111_FAZF000410.get("seibetsu", ""))
        # 行政区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_selGyoseiku_select",
            text=case_data_111_FAZF000410.get("gyoseiku", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_YubinbangouLeftTxt_textboxInput",
            value=case_data_111_FAZF000410.get("yubin_bango_left", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_YubinbangouRightTxt_textboxInput",
            value=case_data_111_FAZF000410.get("yubin_bango_right", ""))
        # 住所・所在地
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtJusho_textboxInput",
            value=case_data_111_FAZF000410.get("jusho_shozai_inp", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_selAimaiJusho_select",
            text=case_data_111_FAZF000410.get("jusho_shozai_sel", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtKatagaki_textboxInput",
            value=case_data_111_FAZF000410.get("katagaki", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtJuminCD_textboxInput",
            value=case_data_111_FAZF000410.get("jumin_kodo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtSetaiCD_textboxInput",
            value=case_data_111_FAZF000410.get("setai_kodo", ""))
        # 特徴指定番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtTokuchoShiteiNo_textboxInput",
            value=case_data_111_FAZF000410.get("tokucho_shitei_bango", ""))
        # 通知番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_txtTsuchiNo_textboxInput",
            value=case_data_111_FAZF000410.get("tsuchi_bango", ""))
        # 検索区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FAZF000410_selKensakuKbn_select",
            text=case_data_111_FAZF000410.get("kensaku_kubun", ""))

        self.screen_shot("検索条件入力 画面_111")

        # 112 検索条件入力 画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_FAZF000410_WrCmnBtn05_button")

        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_FAZF000210_pages1"):
            atena_code = case_data_112_FAZF000210.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code, tab_index)

        # 113 口座照会 画面: 「用途」プルダウンを選択する
        # 用途
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100300_selYoto_select",
            text=case_data_113_FADF100300.get("yoto_2", ""))
        self.screen_shot("口座照会 画面_113")

        # 114 口座照会 画面: 「選択」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_FADF100300_btnSentaku_button")

        # 115 口座照会 画面: 「追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_FADF100400_btnAddChg_button")

        # 116 口座照会 画面: 必要な情報を入力する
        # 異動日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtIDobi_textboxInput",
            value=case_data_116_FADF100400.get("idobi", ""))
        # 公開/非公開
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selKokaiHikokai_select",
            text=case_data_116_FADF100400.get("kokai_hikokai", ""))
        # 金融機関種別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selKinyukikanSB_select",
            text=case_data_116_FADF100400.get("kinyu_kikan_shubetsu", ""))
        # 送金方法
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selSoukinKbn_select",
            text=case_data_116_FADF100400.get("soukin_hoho", ""))
        # 金融機関名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtGinkoCD_textboxInput",
            value=case_data_116_FADF100400.get("kinyu_kikan_mei_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtShitenCD_textboxInput",
            value=case_data_116_FADF100400.get("kinyu_kikan_mei_2", ""))
        # 預金科目
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selYokinKamoku_select",
            text=case_data_116_FADF100400.get("yokin_kamoku", ""))
        # 口座番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaNo_textboxInput",
            value=case_data_116_FADF100400.get("koza_bango", ""))
        # 口座名義人カナ
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMeigiKanaShimei_textboxInput",
            value=case_data_116_FADF100400.get("koza_meiginin_kana", ""))
        # 預金者電話番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtTEL_textboxInput",
            value=case_data_116_FADF100400.get("yokinsha_denwa_bango", ""))
        # 口座申込日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMoshikomibi_textboxInput",
            value=case_data_116_FADF100400.get("koza_moshikomibi", ""))
        # 口座名義人漢字
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMeigiKanjiShimeiNM_textboxInput",
            value=case_data_116_FADF100400.get("koza_meiginin_kanji", ""))
        # 有効期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtYukoKikanFrom_textboxInput",
            value=case_data_116_FADF100400.get("yuko_kikan_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtYukoKikanTo_textboxInput",
            value=case_data_116_FADF100400.get("yuko_kikan_to", ""))
        # 口座停止期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaTeishiKikanFrom_textboxInput",
            value=case_data_116_FADF100400.get("koza_teishi_kikan_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaTeishiKikanTo_textboxInput",
            value=case_data_116_FADF100400.get("koza_teishi_kikan_to", ""))
        # 納付区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selZennoKbn_select",
            text=case_data_116_FADF100400.get("nofu_kubun", ""))
        self.screen_shot("口座管理 画面_116")

        # 117 口座照会 画面: 「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_FADF100400_regbtn_button")

        # 118 口座照会 画面: 「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_FADF100400_msg_span", msg="登録しました。")
        self.screen_shot("口座管理 画面_118")
