from selenium.webdriver.common.by import By
from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28000109(KodomoSiteTestCaseBase):
    """TestQAP010_28000109"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28000109(self):
        case_data_175_QAZF100001 = self.test_data["175_QAZF100001"]
        case_data_178_QAZF100001 = self.test_data["178_QAZF100001"]
        tab_index = 0

        self.do_login_new_tab()

        # 171 メインメニュー 画面: 「子ども子育て支援」ボタン押下
        # 172 メインメニュー 画面: 「強制修正」ボタン押下
        # 173 メインメニュー 画面: 「住民番号変更」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="強制修正", menu_level_3="住民番号変更",
                                 is_new_tab=True)
        tab_index += 1
        self.screen_shot("住民番号変更 画面_173")

        # 174 住民番号変更 画面: 住民番号変更前欄の「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF003000_btnJuminNoHenkoMaeKojinKensaku_button")

        # 175 検索条件入力 画面: 対象の住民コードを入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_175_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_175_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_175_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_175_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_175_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_175_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_175_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_175_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_175_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_175_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_175_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_175_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_175_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_175_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_175_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_175_QAZF100001.get("denwa_bangou_ichi", ""))

        self.screen_shot("検索条件入力 画面_175")

        # 176 検索条件入力 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")
        self.screen_shot("検索条件入力 画面_176")

        # 177 住民番号変更 画面: 住民番号変更後欄の「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF003000_btnJuminNoHenkoGoKojinKensaku_button")

        # 178 検索条件入力 画面: 対象の住民コードを入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_178_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_178_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_178_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_178_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_178_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_178_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_178_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_178_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_178_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_178_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_178_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_178_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_178_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_178_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_178_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_178_QAZF100001.get("denwa_bangou_ichi", ""))

        self.screen_shot("検索条件入力 画面_178")

        # 179 検索条件入力 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")
        self.screen_shot("住民番号変更 画面_179")

        # 180 住民番号変更 画面: 「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF003000_regbtn_button")

        # 181 世帯構成員更新 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAZF003000_msg_span", msg="登録しました。")
        self.screen_shot("世帯構成員更新 画面_181")
