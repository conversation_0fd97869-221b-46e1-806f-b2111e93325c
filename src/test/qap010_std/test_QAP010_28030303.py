from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28030303(KodomoSiteTestCaseBase):
    """TestQAP010_28030303"""

    def setUp(self):
        super().setUp()

    # 利用者負担額決定にかかわる各種帳票が出力できることを確認する。
    def test_QAP010_28030303(self):
        """各種帳票作成"""
        case_data_165_QAZF100001 = self.test_data["165_QAZF100001"]
        case_data_167_QAZF100002 = self.test_data["167_QAZF100002"]
        case_data_168_QAPF100300 = self.test_data["168_QAPF100300"]
        case_data_172_QAPF900200 = self.test_data["172_QAPF900200"]
        case_data_183_ZEAF000400 = self.test_data["183_ZEAF000400"]
        case_data_185_ZEAF000400 = self.test_data["185_ZEAF000400"]
        case_data_186_ZEAF002200 = self.test_data["186_ZEAF002200"]
        case_data_196_ZEAF000400 = self.test_data["196_ZEAF000400"]
        case_data_197_ZEAF002200 = self.test_data["197_ZEAF002200"]
        case_data_209_QAZF100001 = self.test_data["209_QAZF100001"]
        case_data_211_QAZF100002 = self.test_data["211_QAZF100002"]
        case_data_212_QAPF100300 = self.test_data["212_QAPF100300"]
        case_data_215_QAPF106500 = self.test_data["215_QAPF106500"]
        case_data_217_QAPF900500 = self.test_data["217_QAPF900500"]
        case_data_227_ZEAF000400 = self.test_data["227_ZEAF000400"]
        case_data_229_ZEAF000400 = self.test_data["229_ZEAF000400"]
        case_data_230_ZEAF002200 = self.test_data["230_ZEAF002200"]
        case_data_241_ZEAF000400 = self.test_data["241_ZEAF000400"]
        case_data_242_ZEAF002200 = self.test_data["242_ZEAF002200"]
        case_data_255_QAZF100001 = self.test_data["255_QAZF100001"]
        case_data_257_QAZF100002 = self.test_data["257_QAZF100002"]
        case_data_258_QAPF100300 = self.test_data["258_QAPF100300"]
        case_data_261_QAPF107800 = self.test_data["261_QAPF107800"]
        case_data_271_ZEAF000400 = self.test_data["271_ZEAF000400"]
        case_data_273_ZEAF000400 = self.test_data["273_ZEAF000400"]
        case_data_274_ZEAF002200 = self.test_data["274_ZEAF002200"]
        case_data_286_QAZF100001 = self.test_data["286_QAZF100001"]
        case_data_288_QAZF100002 = self.test_data["288_QAZF100002"]
        case_data_289_QAPF100300 = self.test_data["289_QAPF100300"]
        case_data_295_QAPF900200 = self.test_data["295_QAPF900200"]
        case_data_304_ZEAF000400 = self.test_data["304_ZEAF000400"]
        case_data_306_ZEAF000400 = self.test_data["306_ZEAF000400"]
        case_data_307_ZEAF002200 = self.test_data["307_ZEAF002200"]
        case_data_317_ZEAF000400 = self.test_data["317_ZEAF000400"]
        case_data_318_ZEAF002200 = self.test_data["318_ZEAF002200"]
        tab_index = 0

        self.do_login_new_tab()

        # 162 メインメニュー画面: メインメニューから「子ども子育て支援」クリック
        # 163 メインメニュー画面: 「世帯情報」クリック
        # 164 メインメニュー画面: 「検索」ダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=True)
        tab_index += 1

        # 165 検索条件入力画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
                value=case_data_165_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
                text=case_data_165_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
                value=case_data_165_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
                text=case_data_165_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
                value=case_data_165_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
                value=case_data_165_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
                text=case_data_165_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
                text=case_data_165_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
                value=case_data_165_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
                value=case_data_165_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
                value=case_data_165_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
                value=case_data_165_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
                value=case_data_165_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
                text=case_data_165_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
                value=case_data_165_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
                value=case_data_165_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力画面_165")

        # 166 検索条件入力画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 167 世帯履歴画面: 対象の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_167_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code = atena_code, tab_index = tab_index)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳画面_167")

        # 168 世帯台帳画面: 対象児童の「No.」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_168_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)

        # 169 児童台帳画面:「入所管理」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103500", label="入所管理")
        self.screen_shot("児童台帳画面_入所管理_169")

        # 170 入所管理（申込）画面:「入所管理」ボタン押下
        self.click_button_by_label("入所管理")

        # 171 入所管理（申込）画面: 「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103700_printbtn")
        self.screen_shot("印刷指示画面画面_171")

        # 172 印刷指示画面:「利用者負担額決定(変更)通知書」のチェックボックスにチェックを入れる
        # 173 印刷指示画面:「利用者負担額決定通知書」タブ押下
        # 174 印刷指示画面: 「発行年月日」「対象年月」「基準日」を入力します
        exec_params = [
            {
                "report_name": case_data_172_QAPF900200.get("chouhyou_mei", ""),
                "params_1": [
                    {"title": "発行場所", "value": case_data_172_QAPF900200.get("hakkou_basho", "")},
                    {"title": "トレイ", "value": case_data_172_QAPF900200.get("torei", "")},
                    {"title": "部数", "value": case_data_172_QAPF900200.get("busuu", "")}
                ],
                "params_2": [
                    {"title": "発行年月日", "value": case_data_172_QAPF900200.get("hakko_nengetsu_bi", "")},
                    {"title": "対象年月", "value": case_data_172_QAPF900200.get("taishou_nengetsu", "")},
                    {"title": "対象児童", "value": case_data_172_QAPF900200.get("taishou_jidou", "")},
                    {"title": "基準日", "value": case_data_172_QAPF900200.get("kijun_bi", "")},
                    {"title": "出力区分", "value": case_data_172_QAPF900200.get("shutsuryoku_kubun", "")}
                ]
            }
        ]
        checked_reports_count = self.select_kodomo_report(report_param_list=exec_params, tab_index=tab_index,
                                                          screen_shot_name="印刷指示画面画面_174",
                                                          screen_id="QAPF900200")

        # 175 印刷指示画面: 「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF900200_printBtn_button")

        # 176 印刷指示画面:「印刷してよろしいですか？」に対し「はい」ボタン押下
        self.alert_accept()
        self.screen_shot("印刷指示画面_176")

        # 177 ファイルダウンロード画面: 出力対象で選択した帳票の「No1」ボタン押下
        # 178 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        self.print_kodomo_reports(limit_wait_count=20, time_span_sec=4, tab_index=tab_index,
                                  screen_shot_name="ファイルダウンロード画面_178", count_report=checked_reports_count)

        # 179 印刷指示画面画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 180 メインメニュー画面: メインメニューから「バッチ管理」クリック
        # 181 メインメニュー画面:「即時実行」クリック
        # 182 メインメニュー画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 183 スケジュール個別追加画面: 業務名: <子ども子育て支援>: サブシステム名：<入所>: 処理名：<保育料決定通知書_一覧出力処理>
        # 184 スケジュール個別追加画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_183_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_183_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_183_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加画面_184")

        # 185 スケジュール個別追加画面: 「保育料決定通知書 抽出」の「No」ボタン押下：
        self.click_batch_job_button_by_label(job_label=case_data_185_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 186 実行指示画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_186_ZEAF002200.get("shokan_ku", "")},
            {"title": "公私区分", "type": "select",
             "value": case_data_186_ZEAF002200.get("koushi_kubun", "")},
            {"title": "再発行区分", "type": "select",
             "value": case_data_186_ZEAF002200.get("saihakko_kubun", "")},
            {"title": "支所", "type": "select",
             "value": case_data_186_ZEAF002200.get("shisho", "")},
            {"title": "施設コード", "type": "text", "value": case_data_186_ZEAF002200.get("shisetsu_code", "")},
            {"title": "施設種類", "type": "select", "value": case_data_186_ZEAF002200.get("shisetsu_shurui", "")},
            {"title": "実施区分", "type": "select",
             "value": case_data_186_ZEAF002200.get("jisshi_kubun", "")},
            {"title": "出力区分", "type": "select",
             "value": case_data_186_ZEAF002200.get("shutsuryoku_kubun", "")},
            {"title": "所在", "type": "select",
             "value": case_data_186_ZEAF002200.get("shozai", "")},
            {"title": "申込区分", "type": "select", "value": case_data_186_ZEAF002200.get("moushikomi_kubun", "")},
            {"title": "対象児童", "type": "select",
             "value": case_data_186_ZEAF002200.get("taishou_jidou", "")},
            {"title": "対象年月", "type": "text",
             "value": case_data_186_ZEAF002200.get("taishou_nengetsu", "")},
            {"title": "入所形態", "type": "select",
             "value": case_data_186_ZEAF002200.get("nyusho_keitai", "")},
            {"title": "発行年月日", "type": "text",
             "value": case_data_186_ZEAF002200.get("hakko_nengetsu_bi", "")},
            {"title": "並び順", "type": "select",
             "value": case_data_186_ZEAF002200.get("narabi_jun", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_186_ZEAF002200.get("yuubin_kuunai_tokubetsu_umu", "")},
            {"title": "基準日", "type": "text",
             "value": case_data_186_ZEAF002200.get("kijun_bi", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示画面_186")

        # 187 実行指示画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 188 実行管理画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理画面_188")

        # 189 実行管理画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 190 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理画面_190")

        # 191 納品物管理画面: 納品物の「ダウンロード」ボタン押下
        # 192 ファイルダウンロード画面:「No1」ボタン押下
        # 193 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 194 納品物管理画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 195 納品物管理画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加画面_195")

        # 196 スケジュール個別追加画面:「保育料決定通知書　出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_196_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 197 実行指示画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_197_ZEAF002200.get("shokan_ku", "")},
            {"title": "支所", "type": "select", "value": case_data_197_ZEAF002200.get("shisho", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_197_ZEAF002200.get("hakko_nengetsu_bi", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示画面_197")

        # 198 実行指示画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 199 実行管理画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理画面_199")

        # 200 実行管理画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 201 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理画面_201")

        # 202 納品物管理画面: 納品物の「ダウンロード」ボタン押下
        # 203 ファイルダウンロード画面:「No1」ボタン押下
        # 204 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 205 納品物管理画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 206 メインメニュー画面: メインメニューから「子ども子育て支援」クリック
        # 207 メインメニュー画面:「世帯情報」クリック
        # 208 メインメニュー画面:「検索」ダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=False)
        tab_index += 1

        # 209 検索条件入力画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
                value=case_data_209_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
                text=case_data_209_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
                value=case_data_209_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
                text=case_data_209_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
                value=case_data_209_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
                value=case_data_209_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
                text=case_data_209_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
                text=case_data_209_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
                value=case_data_209_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
                value=case_data_209_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
                value=case_data_209_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
                value=case_data_209_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
                value=case_data_209_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
                text=case_data_209_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
                value=case_data_209_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
                value=case_data_209_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力画面_209")

        # 210 検索条件入力画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")
        self.screen_shot("世帯履歴画面_210")

        # 211 世帯履歴画面: 対象の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_211_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code = atena_code, tab_index = tab_index)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳画面_211")

        # 212 世帯台帳画面: 対象児童の「No.」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_212_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)

        # 213 児童台帳画面:「入所管理」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103500", label="入所管理")
        self.screen_shot("児童台帳画面_入所管理_213")

        # 214 児童台帳画面: 「減免・調整申請」ボタン押下
        self.click_button_by_label("減免・調整申請")

        # 215 保育料減免・調整管理（階層その他）画面: 印刷対象の申請情報履歴を選択
        self.click_button_by_label(case_data_215_QAPF106500.get("shinsei_jouhou_rireki_no", ""))
        self.screen_shot("保育料減免・調整管理（階層その他）_215")

        # 216 保育料減免・調整管理（階層その他）画面:「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF106500_printbtn_button")

        # 217 印刷指示画面画面:「利用者負担額減免通知書」のチェックボックスにチェックを入れる
        # 218 印刷指示画面画面:「発行年月日」を入力します
        exec_params = [
            {
                "report_name": case_data_217_QAPF900500.get("chouhyou_mei", ""),
                "params_1": [
                    {"title": "発行場所", "value": case_data_217_QAPF900500.get("hakkou_basho", "")},
                    {"title": "トレイ", "value": case_data_217_QAPF900500.get("torei", "")},
                    {"title": "部数", "value": case_data_217_QAPF900500.get("busuu", "")}
                ],
                "params_2": [
                    {"title": "発行年月日", "value": case_data_217_QAPF900500.get("hakko_nengetsu_bi", "")}
                ],
            }
        ]
        checked_reports_count = self.select_kodomo_report(report_param_list=exec_params, tab_index=tab_index,
                                                          screen_shot_name="印刷指示画面画面_218",
                                                          screen_id="QAPF900500")

        # 219 印刷指示画面画面:「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF900500_printBtn_button")

        # 220 印刷指示画面画面:「印刷してよろしいですか」に「はい」ボタン押下
        self.alert_accept()
        self.screen_shot("ファイルダウンロード画面_220")

        # 221 ファイルダウンロード画面: 出力対象で選択した帳票の「No1」ボタン押下
        # 222 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        self.print_kodomo_reports(limit_wait_count=20, time_span_sec=4, tab_index=tab_index,
                                  screen_shot_name="ファイルダウンロード画面_222", count_report=checked_reports_count)

        # 223 印刷指示画面画面:「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 224 メインメニュー画面: メインメニューから「バッチ管理」クリック
        # 225 メインメニュー画面:「即時実行」クリック
        # 226 メインメニュー画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 227 スケジュール個別追加画面: 業務名: <子ども子育て支援>: サブシステム名：<入所>: 処理名：<保育所徴収金減免決定（却下）通知書出力処理>
        # 228 スケジュール個別追加画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_227_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_227_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_227_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加画面_228")

        # 229 スケジュール個別追加画面: 「保育所徴収金減免決定（却下）通知書出力」の「No」ボタン押下：
        self.click_batch_job_button_by_label(job_label=case_data_229_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)

        # 230 実行指示画面: パラメータを入力
        # 231 実行指示画面:「出力区分：承認」を選択
        params = [
            {"title": "対象年月", "type": "text", "value": case_data_230_ZEAF002200.get("taishou_nengetsu", "")},
            {"title": "出力区分", "type": "select",
             "value": case_data_230_ZEAF002200.get("shutsuryoku_kubun", "")},
            {"title": "再発行区分", "type": "select",
             "value": case_data_230_ZEAF002200.get("saihakko_kubun", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_230_ZEAF002200.get("yuubin_kuunai_tokubetsu", "")},
            {"title": "発行年月日", "type": "text",
             "value": case_data_230_ZEAF002200.get("hakko_nengetsu_bi", "")},
            {"title": "並び順", "type": "select", "value": case_data_230_ZEAF002200.get("narabi_jun", "")},
            {"title": "所管区", "type": "select", "value": case_data_230_ZEAF002200.get("shokan_ku", "")},
            {"title": "施設種類", "type": "select", "value": case_data_230_ZEAF002200.get("shisetsu_shurui", "")},
            {"title": "入所形態", "type": "select", "value": case_data_230_ZEAF002200.get("nyusho_keitai", "")},
            {"title": "実施区分", "type": "select", "value": case_data_230_ZEAF002200.get("jisshi_kubun", "")},
            {"title": "公私区分", "type": "select", "value": case_data_230_ZEAF002200.get("koushi_kubun", "")},
            {"title": "事業所番号", "type": "text", "value": case_data_230_ZEAF002200.get("jigyousho_bangou", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示画面_231")

        # 232 実行指示画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 233 実行管理画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理画面_233")

        # 234 実行管理画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 235 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理画面_235")

        # 236 納品物管理画面: 納品物の「ダウンロード」ボタン押下
        # 237 ファイルダウンロード画面:「No1」ボタン押下
        # 238 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 239 納品物管理画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 240 納品物管理画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加画面_240")

        # 241 スケジュール個別追加画面:「契約情報一括更新処理（契約新規）」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_241_ZEAF000400.get("batch_job_004", ""),
                                             tab_index=tab_index)

        # 242 実行指示画面: パラメータを入力
        # 243 実行指示画面:「出力区分：不承諾」を選択
        params = [
            {"title": "対象年月", "type": "text", "value": case_data_242_ZEAF002200.get("taishou_nengetsu", "")},
            {"title": "出力区分", "type": "select",
             "value": case_data_242_ZEAF002200.get("shutsuryoku_kubun", "")},
            {"title": "再発行区分", "type": "select",
             "value": case_data_242_ZEAF002200.get("saihakko_kubun", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_242_ZEAF002200.get("yuubin_kuunai_tokubetsu", "")},
            {"title": "発行年月日", "type": "text",
             "value": case_data_242_ZEAF002200.get("hakko_nengetsu_bi", "")},
            {"title": "並び順", "type": "select", "value": case_data_242_ZEAF002200.get("narabi_jun", "")},
            {"title": "所管区", "type": "select", "value": case_data_242_ZEAF002200.get("shokan_ku", "")},
            {"title": "施設種類", "type": "select", "value": case_data_242_ZEAF002200.get("shisetsu_shurui", "")},
            {"title": "入所形態", "type": "select", "value": case_data_242_ZEAF002200.get("nyusho_keitai", "")},
            {"title": "実施区分", "type": "select", "value": case_data_242_ZEAF002200.get("jisshi_kubun", "")},
            {"title": "公私区分", "type": "select", "value": case_data_242_ZEAF002200.get("koushi_kubun", "")},
            {"title": "事業所番号", "type": "text", "value": case_data_242_ZEAF002200.get("jigyousho_bangou", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示画面_243")

        # 244 実行指示画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 245 実行管理画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理画面_245")

        # 246 実行管理画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 247 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理画面_247")

        # 248 納品物管理画面: 納品物の「ダウンロード」ボタン押下
        # 249 ファイルダウンロード画面:「No1」ボタン押下
        # 250 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 251 納品物管理画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 252 メインメニュー画面: メインメニューから「子ども子育て支援」クリック
        # 253 メインメニュー画面: 「世帯情報」クリック
        # 254 メインメニュー画面: 「検索」ダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3='検索',
                                 is_new_tab=False)
        tab_index += 1

        # 255 検索条件入力画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
                value=case_data_255_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
                text=case_data_255_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
                value=case_data_255_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
                text=case_data_255_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
                value=case_data_255_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
                value=case_data_255_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
                text=case_data_255_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
                text=case_data_255_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
                value=case_data_255_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
                value=case_data_255_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
                value=case_data_255_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
                value=case_data_255_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
                value=case_data_255_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
                text=case_data_255_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
                value=case_data_255_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
                value=case_data_255_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力画面_255")

        # 256 検索条件入力画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 257 世帯履歴画面: 対象の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_257_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code = atena_code, tab_index = tab_index)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳画面_257")

        # 258 世帯台帳画面: 対象児童の「No.」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_258_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)
        self.screen_shot("世帯台帳画面_258")

        # 259 児童台帳画面:「賦課」ボタン押下
        self.click_button_by_label("賦課")
        self.screen_shot("児童賦課情報画面_259")

        # 260 児童賦課情報画面:「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 261 印刷指示画面:「保育料(利用料)変更通知書」のチェックボックスにチェックを入れる
        # 262 印刷指示画面:「発行年月日」「処理対象年月」「印刷区分」「変更理由」を入力します
        exec_params = [
            {
                "report_name": case_data_261_QAPF107800.get("chouhyou_mei", ""),
                "params_1": [
                    {"title": "発行場所", "value": case_data_261_QAPF107800.get("hakkou_basho", "")},
                    {"title": "トレイ", "value": case_data_261_QAPF107800.get("torei", "")},
                    {"title": "部数", "value": case_data_261_QAPF107800.get("busuu", "")}
                ],
                "params_2": [
                    {"title": "発行年月日", "value": case_data_261_QAPF107800.get("hakko_nengetsu_bi", "")},
                    {"title": "処理対象年月", "value": case_data_261_QAPF107800.get("shori_taishou_nengetsu", "")},
                    {"title": "印刷区分", "value": case_data_261_QAPF107800.get("insatsu_kubun", "")},
                    {"title": "変更理由", "value": case_data_261_QAPF107800.get("henkou_riyuu", "")}
                ],
            },
        ]
        checked_reports_count = self.select_kodomo_report(report_param_list=exec_params, tab_index=tab_index,
                                                          screen_shot_name="印刷指示画面画面_262",
                                                          screen_id="QAPF107800")

        # 263 印刷指示画面画面:「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF107800_printBtn_button")

        # 264 印刷指示画面画面:「印刷してよろしいですか」に「はい」ボタン押下
        self.alert_accept()
        self.screen_shot("ファイルダウンロード画面_264")

        # 265 ファイルダウンロード画面: 出力対象で選択した帳票の「No1」ボタン押下
        # 266 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        self.print_kodomo_reports(limit_wait_count=20, time_span_sec=4, tab_index=tab_index,
                                  screen_shot_name="ファイルダウンロード画面_266", count_report=checked_reports_count)

        # 267 印刷指示画面画面:「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 268 メインメニュー画面: メインメニューから「バッチ管理」クリック
        # 269 メインメニュー画面:「即時実行」クリック
        # 270 メインメニュー画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 271 スケジュール個別追加画面: 業務名: <子ども子育て支援>: サブシステム名：<賦課>: 処理名：<月次賦課処理>
        # 272 スケジュール個別追加画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_271_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_271_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_271_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加画面_272")

        # 273 スケジュール個別追加画面: 「保育料変更者一覧・保育料変更通知書作成処理」の「No」ボタン押下：
        self.click_batch_job_button_by_label(job_label=case_data_273_ZEAF000400.get("batch_job_005", ""),
                                             tab_index=tab_index)

        # 274 実行指示画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_274_ZEAF002200.get("shokan_ku", "")},
            {"title": "支所", "type": "select",
             "value": case_data_274_ZEAF002200.get("shisho", "")},
            {"title": "発行年月日", "type": "text",
             "value": case_data_274_ZEAF002200.get("hakko_nengetsu_bi", "")},
            {"title": "対象年月", "type": "text",
             "value": case_data_274_ZEAF002200.get("taishou_nengetsu", "")},
            {"title": "施設コード", "type": "text",
             "value": case_data_274_ZEAF002200.get("shisetsu_code", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_274_ZEAF002200.get("yuubin_kuunai_tokubetsu_umu", "")},
            {"title": "並び順", "type": "select", "value": case_data_274_ZEAF002200.get("narabi_jun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示画面_274")

        # 275 実行指示画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 276 実行管理画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理画面_276")

        # 277 実行管理画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 278 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理画面_278")

        # 279 納品物管理画面: 納品物の「ダウンロード」ボタン押下
        # 280 ファイルダウンロード画面:「No1」ボタン押下
        # 281 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 282 納品物管理画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 283 メインメニュー画面: メインメニューから「子ども子育て支援」クリック
        # 284 メインメニュー画面: 「世帯情報」クリック
        # 285 メインメニュー画面: 「検索」ダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=False)
        tab_index += 1

        # 286 検索条件入力画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
                value=case_data_286_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
                text=case_data_286_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
                value=case_data_286_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
                text=case_data_286_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
                value=case_data_286_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
                value=case_data_286_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
                text=case_data_286_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
                text=case_data_286_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
                value=case_data_286_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
                value=case_data_286_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
                value=case_data_286_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
                value=case_data_286_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
                value=case_data_286_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
                text=case_data_286_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
                value=case_data_286_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
                value=case_data_286_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件入力画面_286")

        # 287 検索条件入力画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 288 世帯履歴画面: 対象の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_288_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code = atena_code, tab_index = tab_index)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳画面_288")

        # 289 世帯台帳画面: 対象児童の「No.」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_289_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)

        # 290 児童台帳画面:「入所管理」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103500", label="入所管理")
        self.screen_shot("児童台帳画面_入所管理_290")

        # 291 入所管理（申込）画面:「入所管理」ボタン押下
        self.click_button_by_label("入所管理")
        self.screen_shot("入所管理（申込）画面_291")

        # 292 入所管理（申込）画面: 「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103700_printbtn")

        # 293 印刷指示画面:「保育料無償のお知らせ」のチェックボックスにチェックを入れる
        # 294 印刷指示画面:「保育料無償のお知らせ」タブ押下
        # 295 印刷指示画面:「発行年月日」「対象年月」「対象児童」「基準日」「出力区分」を入力します
        exec_params = [
            {
                "report_name": case_data_295_QAPF900200.get("chouhyou_mei", ""),
                "params_1": [
                    {"title": "発行場所", "value": case_data_295_QAPF900200.get("hakkou_basho", "")},
                    {"title": "トレイ", "value": case_data_295_QAPF900200.get("torei", "")},
                    {"title": "部数", "value": case_data_295_QAPF900200.get("busuu", "")}
                ],
                "params_2": [
                    {"title": "発行年月日", "value": case_data_295_QAPF900200.get("hakko_nengetsu_bi", "")},
                    {"title": "対象年月", "value": case_data_295_QAPF900200.get("taishou_nengetsu", "")},
                    {"title": "対象児童", "value": case_data_295_QAPF900200.get("taishou_jidou", "")},
                    {"title": "基準日", "value": case_data_295_QAPF900200.get("kijun_bi", "")},
                    {"title": "出力区分", "value": case_data_295_QAPF900200.get("shutsuryoku_kubun", "")}
                ]
            }
        ]
        checked_reports_count = self.select_kodomo_report(report_param_list=exec_params, tab_index=tab_index,
                                                          screen_shot_name="印刷指示画面画面_295",
                                                          screen_id="QAPF900200")

        # 296 印刷指示画面: 「印刷」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF900200_printBtn_button")

        # 297 印刷指示画面:「印刷してよろしいですか？」に対し「はい」ボタン押下
        self.alert_accept()
        self.screen_shot("印刷指示画面_297")

        # 298 ファイルダウンロード画面: 出力対象で選択した帳票の「No1」ボタン押下
        # 299 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        self.print_kodomo_reports(limit_wait_count=20, time_span_sec=4, tab_index=tab_index,
                                  screen_shot_name="ファイルダウンロード画面_299", count_report=checked_reports_count)

        # 300 印刷指示画面画面: 「閉じる」ボタン押下
        self.click_button_by_label("閉じる")

        # 301 メインメニュー画面: メインメニューから「バッチ管理」クリック
        # 302 メインメニュー画面:「即時実行」クリック
        # 303 メインメニュー画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 304 スケジュール個別追加画面: 業務名: <子ども子育て支援>: サブシステム名：<入所>: 処理名：<保育料無償のお知らせ出力>
        # 305 スケジュール個別追加画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_304_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_304_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_304_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加画面_305")

        # 306 スケジュール個別追加画面: 「保育料無償化対象者一覧出力」の「No」ボタン押下：
        self.click_batch_job_button_by_label(job_label=case_data_306_ZEAF000400.get("batch_job_006", ""),
                                             tab_index=tab_index)

        # 307 実行指示画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_307_ZEAF002200.get("shokan_ku", "")},
            {"title": "支所", "type": "select",
             "value": case_data_307_ZEAF002200.get("shisho", "")},
            {"title": "再発行区分", "type": "select",
             "value": case_data_307_ZEAF002200.get("saihakko_kubun", "")},
            {"title": "施設コード", "type": "text", "value": case_data_307_ZEAF002200.get("shisetsu_code", "")},
            {"title": "出力区分", "type": "select",
             "value": case_data_307_ZEAF002200.get("shutsuryoku_kubun", "")},
            {"title": "対象児童", "type": "select",
             "value": case_data_307_ZEAF002200.get("taishou_jidou", "")},
            {"title": "対象年月", "type": "text",
             "value": case_data_307_ZEAF002200.get("taishou_nengetsu", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_307_ZEAF002200.get("hakko_nengetsu_bi", "")},
            {"title": "並び順", "type": "select",
             "value": case_data_307_ZEAF002200.get("narabi_jun", "")},
            {"title": "児童宛名コード", "type": "text",
             "value": case_data_307_ZEAF002200.get("jidou_atena_code", "")},

        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示画面_307")

        # 308 実行指示画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 309 実行管理画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理画面_309")

        # 310 実行管理画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 311 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理画面_311")

        # 312 納品物管理画面: 納品物の「ダウンロード」ボタン押下
        # 313 ファイルダウンロード画面:「No1」ボタン押下
        # 314 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 315 納品物管理画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 316 納品物管理画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加画面_316")

        # 317 スケジュール個別追加画面:「保育料無償のお知らせ　出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_317_ZEAF000400.get("batch_job_007", ""),
                                             tab_index=tab_index)

        # 318 実行指示画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_318_ZEAF002200.get("shokan_ku", "")},
            {"title": "所管区", "type": "select", "value": case_data_318_ZEAF002200.get("shokan_ku", "")},
            {"title": "支所", "type": "select", "value": case_data_318_ZEAF002200.get("shisetsu_shurui", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_318_ZEAF002200.get("shisetsu_code", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示画面_318")

        # 319 実行指示画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 320 実行管理画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理画面_320")

        # 321 実行管理画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 322 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理画面_322")

        # 323 納品物管理画面: 納品物の「ダウンロード」ボタン押下
        # 324 ファイルダウンロード画面:「No1」ボタン押下
        # 325 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 326 納品物管理画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
