from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070702(KodomoSiteTestCaseBase):
    """TestQAP010_28070702"""

    def setUp(self):
        super().setUp()

    # 還付・充当を登録できることを確認する。
    def test_QAP010_28070702(self):
        """還付・充当登録"""

        case_data_076_ZEAF000400 = self.test_data["076_ZEAF000400"]
        case_data_077_ZEAF000400 = self.test_data["077_ZEAF000400"]
        case_data_078_ZEAF002200 = self.test_data["078_ZEAF002200"]
        case_data_083_ZEAF000400 = self.test_data["083_ZEAF000400"]
        case_data_084_ZEAF000400 = self.test_data["084_ZEAF000400"]
        case_data_085_ZEAF002200 = self.test_data["085_ZEAF002200"]
        case_data_090_ZEAF000400 = self.test_data["090_ZEAF000400"]
        case_data_091_ZEAF000400 = self.test_data["091_ZEAF000400"]
        case_data_092_ZEAF002200 = self.test_data["092_ZEAF002200"]
        tab_index = 0

        self.do_login_new_tab()

        # 72 メインメニュー画面: メインメニューから「バッチ管理」クリック
        # 73 メインメニュー画面: 「即時実行」クリック
        # 74 メインメニュー画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1

        # 75 スケジュール個別追加 画面：宛名情報管理 サブシステム名：宛名 処理名：宛名・口座バッチマスタ作成処理
        # 76 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_076_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_076_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_076_ZEAF000400.get("shori_mei", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_76")

        # 77 スケジュール個別追加 画面: 「宛名・口座バッチマスタ作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_077_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 78 実行指示 画面: パラメータを入力
        params = [
            {"title": "業務名１", "type": "select", "value": case_data_078_ZEAF002200.get("gyomu_mei_1", "")},
            {"title": "業務別基準日1", "type": "text",
             "value": case_data_078_ZEAF002200.get("gyomu_betsu_kijunbi_1", "")},
            {"title": "口座基準日1", "type": "text", "value": case_data_078_ZEAF002200.get("koza_kijunbi_1", "")},
            {"title": "業務名2", "type": "select", "value": case_data_078_ZEAF002200.get("gyomu_mei_2", "")},
            {"title": "業務別基準日2", "type": "text",
             "value": case_data_078_ZEAF002200.get("gyomu_betsu_kijunbi_2", "")},
            {"title": "口座基準日2", "type": "text", "value": case_data_078_ZEAF002200.get("koza_kijunbi_2", "")},
            {"title": "業務名3", "type": "select", "value": case_data_078_ZEAF002200.get("gyomu_mei_3", "")},
            {"title": "業務別基準日3", "type": "text",
             "value": case_data_078_ZEAF002200.get("gyomu_betsu_kijunbi_3", "")},
            {"title": "口座基準日3", "type": "text", "value": case_data_078_ZEAF002200.get("koza_kijunbi_3", "")},
            {"title": "業務名4", "type": "select", "value": case_data_078_ZEAF002200.get("gyomu_mei_4", "")},
            {"title": "業務別基準日4", "type": "text",
             "value": case_data_078_ZEAF002200.get("gyomu_betsu_kijunbi_4", "")},
            {"title": "口座基準日4", "type": "text", "value": case_data_078_ZEAF002200.get("koza_kijunbi_4", "")},
            {"title": "業務名5", "type": "select", "value": case_data_078_ZEAF002200.get("gyomu_mei_5", "")},
            {"title": "業務別基準日5", "type": "text",
             "value": case_data_078_ZEAF002200.get("gyomu_betsu_kijunbi_5", "")},
            {"title": "口座基準日5", "type": "text", "value": case_data_078_ZEAF002200.get("koza_kijunbi_5", "")},
            {"title": "業務名6", "type": "select", "value": case_data_078_ZEAF002200.get("gyomu_mei_6", "")},
            {"title": "業務別基準日6", "type": "text",
             "value": case_data_078_ZEAF002200.get("gyomu_betsu_kijunbi_6", "")},
            {"title": "口座基準日6", "type": "text", "value": case_data_078_ZEAF002200.get("koza_kijunbi_6", "")},
            {"title": "業務名7", "type": "select", "value": case_data_078_ZEAF002200.get("gyomu_mei_7", "")},
            {"title": "業務別基準日7", "type": "text",
             "value": case_data_078_ZEAF002200.get("gyomu_betsu_kijunbi_7", "")},
            {"title": "口座基準日7", "type": "text", "value": case_data_078_ZEAF002200.get("koza_kijunbi_7", "")},
            {"title": "業務名8", "type": "select", "value": case_data_078_ZEAF002200.get("gyomu_mei_8", "")},
            {"title": "業務別基準日8", "type": "text",
             "value": case_data_078_ZEAF002200.get("gyomu_betsu_kijunbi_8", "")},
            {"title": "口座基準日8", "type": "text", "value": case_data_078_ZEAF002200.get("koza_kijunbi_8", "")},
            {"title": "業務名9", "type": "select", "value": case_data_078_ZEAF002200.get("gyomu_mei_9", "")},
            {"title": "業務別基準日9", "type": "text",
             "value": case_data_078_ZEAF002200.get("gyomu_betsu_kijunbi_9", "")},
            {"title": "口座基準日9", "type": "text", "value": case_data_078_ZEAF002200.get("koza_kijunbi_9", "")},
            {"title": "業務名10", "type": "select", "value": case_data_078_ZEAF002200.get("gyomu_mei_10", "")},
            {"title": "業務別基準日10", "type": "text",
             "value": case_data_078_ZEAF002200.get("gyomu_betsu_kijunbi_10", "")},
            {"title": "口座基準日10", "type": "text", "value": case_data_078_ZEAF002200.get("koza_kijunbi_10", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)

        self.screen_shot("実行指示 画面_78")

        # # 79 実行指示 画面:「実行」ボタン押下
        # self.exec_batch_job_kodomo(tab_index=tab_index)
        #
        # # 80 実行管理 画面:「検索」ボタン押下
        # self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        # self.screen_shot("実行管理 画面_80")

        # 81 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002200", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_81")

        # 82 スケジュール個別追加 画面：収納・滞納 サブシステム名：マスタ作成 処理名：収納マスタ＆収納用宛名口座マスタ作成処理（統合版）
        # 83 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_083_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_083_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_083_ZEAF000400.get("shori_mei", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_83")

        # 84 スケジュール個別追加 画面: 「収納マスタ＆収納用宛名口座マスタ作成本処理(統合版)」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_084_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 85 実行指示 画面: パラメータを入力
        params = [
            {"title": "未納指定", "type": "select", "value": case_data_085_ZEAF002200.get("mino_shitei", "")},
            {"title": "科目", "type": "checkbox", "value": case_data_085_ZEAF002200.get("kamoku_chk", "").values()},
            {"title": "調定発生年度開始", "type": "select",
             "value": case_data_085_ZEAF002200.get("chotei_hassei_nendo_kaishi", "")},
            {"title": "調定発生年度終了", "type": "select",
             "value": case_data_085_ZEAF002200.get("chotei_hassei_nendo_shuryo", "")},
            {"title": "課税根拠年度開始", "type": "select",
             "value": case_data_085_ZEAF002200.get("kazei_konkyo_nendo_kaishi", "")},
            {"title": "課税根拠年度終了", "type": "select",
             "value": case_data_085_ZEAF002200.get("kazei_konkyo_nendo_shuryo", "")},
            {"title": "期別開始", "type": "text", "value": case_data_085_ZEAF002200.get("kibetsu_kaishi", "")},
            {"title": "期別終了", "type": "text", "value": case_data_085_ZEAF002200.get("kibetsu_shuryo", "")},
            {"title": "納期限開始", "type": "text", "value": case_data_085_ZEAF002200.get("nokigen_kaishi", "")},
            {"title": "納期限終了", "type": "text", "value": case_data_085_ZEAF002200.get("nokigen_shuryo", "")},
            {"title": "除外納付区分", "type": "text", "value": case_data_085_ZEAF002200.get("jogai_nofu_kubun", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)

        self.screen_shot("実行指示 画面_85")

        # 86 実行指示 画面:「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 87 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_87")

        # 88 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_88")

        # 89 スケジュール個別追加 画面：収納・滞納 サブシステム名：随時 処理名：還付一括登録処理
        # 90 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_090_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_090_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_090_ZEAF000400.get("shori_mei", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_90")

        # 91 スケジュール個別追加 画面: 「還付一括登録処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_091_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)

        # 92 実行指示 画面: パラメータを入力
        params = [
            {"title": "科目", "type": "checkbox", "value": case_data_092_ZEAF002200.get("kamoku_chk").values()},
            {"title": "還付通知日", "type": "text", "value": case_data_092_ZEAF002200.get("kanpu_tsuchibi", "")},
            {"title": "支出決定日", "type": "text", "value": case_data_092_ZEAF002200.get("shishutsu_ketteibi", "")},
            {"title": "還付理由", "type": "select", "value": case_data_092_ZEAF002200.get("kanpu_riyu", "")},
            {"title": "更正理由", "type": "select", "value": case_data_092_ZEAF002200.get("kosei_riyu", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)

        self.screen_shot("実行指示 画面_92")

        # 93 実行指示 画面:「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 94 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_94")
