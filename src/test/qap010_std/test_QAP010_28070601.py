from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070601(KodomoSiteTestCaseBase):
    """TestQAP010_28070601"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070601(self):

        case_data_006_ZEAF000400 = self.test_data["006_ZEAF000400"]
        case_data_008_ZEAF000400 = self.test_data["008_ZEAF000400"]
        case_data_009_ZEAF002200 = self.test_data["009_ZEAF002200"]
        case_data_013_ZEAF000400 = self.test_data["013_ZEAF000400"]
        case_data_015_ZEAF000400 = self.test_data["015_ZEAF000400"]
        case_data_016_ZEAF002200 = self.test_data["016_ZEAF002200"]
        case_data_020_ZEAF000400 = self.test_data["020_ZEAF000400"]
        case_data_022_ZEAF000400 = self.test_data["022_ZEAF000400"]
        case_data_023_ZEAF002200 = self.test_data["023_ZEAF002200"]
        case_data_028_ZEAF002700 = self.test_data["028_ZEAF002700"]
        case_data_032_ZEAF002700 = self.test_data["032_ZEAF002700"]
        case_data_036_ZEAF002700 = self.test_data["036_ZEAF002700"]
        case_data_041_ZEAF000400 = self.test_data["041_ZEAF000400"]
        case_data_047_JAAF400100 = self.test_data["047_JAAF400100"]
        case_data_048_JAAF400200 = self.test_data["048_JAAF400200"]
        case_data_049_JAAF403400 = self.test_data["049_JAAF403400"]
        case_data_053_JACF300300 = self.test_data["053_JACF300300"]
        case_data_059_JAAF400100 = self.test_data["059_JAAF400100"]
        case_data_060_JAAF400200 = self.test_data["060_JAAF400200"]
        case_data_061_JAAF403400 = self.test_data["061_JAAF403400"]
        tab_index = 0
        
        # 1, 2 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 3 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 4 メインメニュー 画面:「即時実行」クリック
        # 5 メインメニュー 画面:「スケジュール個別追加」をダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1

        # 6 スケジュール個別追加 画面: <業務名>：宛名情報管理 <サブシステム名>：宛名 <処理名>: 宛名・口座バッチマスタ作成処理
        # 7 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_006_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_006_ZEAF000400.get("sabushisutemu_mei"),
                                        shoriNM=case_data_006_ZEAF000400.get("shori_mei"), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_7")

        # 8 スケジュール個別追加 画面:「宛名・口座バッチマスタ作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_008_ZEAF000400.get("batch_job_001"), tab_index=tab_index)

        # 9 実行指示 画面: パラメータを入力
        params = [
            {"title": "業務名１", "type": "select", "value": case_data_009_ZEAF002200.get("gyomu_mei_1", "")},
            {"title": "業務別基準日1", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyomu_betsu_kijunbi_1", "")},
            {"title": "口座基準日1", "type": "text", "value": case_data_009_ZEAF002200.get("koza_kijunbi_1", "")},
            {"title": "業務名2", "type": "select", "value": case_data_009_ZEAF002200.get("gyomu_mei_2", "")},
            {"title": "業務別基準日2", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyomu_betsu_kijunbi_2", "")},
            {"title": "口座基準日2", "type": "text", "value": case_data_009_ZEAF002200.get("koza_kijunbi_2", "")},
            {"title": "業務名3", "type": "select", "value": case_data_009_ZEAF002200.get("gyomu_mei_3", "")},
            {"title": "業務別基準日3", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyomu_betsu_kijunbi_3", "")},
            {"title": "口座基準日3", "type": "text", "value": case_data_009_ZEAF002200.get("koza_kijunbi_3", "")},
            {"title": "業務名4", "type": "select", "value": case_data_009_ZEAF002200.get("gyomu_mei_4", "")},
            {"title": "業務別基準日4", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyomu_betsu_kijunbi_4", "")},
            {"title": "口座基準日4", "type": "text", "value": case_data_009_ZEAF002200.get("koza_kijunbi_4", "")},
            {"title": "業務名5", "type": "select", "value": case_data_009_ZEAF002200.get("gyomu_mei_5", "")},
            {"title": "業務別基準日5", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyomu_betsu_kijunbi_5", "")},
            {"title": "口座基準日5", "type": "text", "value": case_data_009_ZEAF002200.get("koza_kijunbi_5", "")},
            {"title": "業務名6", "type": "select", "value": case_data_009_ZEAF002200.get("gyomu_mei_6", "")},
            {"title": "業務別基準日6", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyomu_betsu_kijunbi_6", "")},
            {"title": "口座基準日6", "type": "text", "value": case_data_009_ZEAF002200.get("koza_kijunbi_6", "")},
            {"title": "業務名7", "type": "select", "value": case_data_009_ZEAF002200.get("gyomu_mei_7", "")},
            {"title": "業務別基準日7", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyomu_betsu_kijunbi_7", "")},
            {"title": "口座基準日7", "type": "text", "value": case_data_009_ZEAF002200.get("koza_kijunbi_7", "")},
            {"title": "業務名8", "type": "select", "value": case_data_009_ZEAF002200.get("gyomu_mei_8", "")},
            {"title": "業務別基準日8", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyomu_betsu_kijunbi_8", "")},
            {"title": "口座基準日8", "type": "text", "value": case_data_009_ZEAF002200.get("koza_kijunbi_8", "")},
            {"title": "業務名9", "type": "select", "value": case_data_009_ZEAF002200.get("gyomu_mei_9", "")},
            {"title": "業務別基準日9", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyomu_betsu_kijunbi_9", "")},
            {"title": "口座基準日9", "type": "text", "value": case_data_009_ZEAF002200.get("koza_kijunbi_9", "")},
            {"title": "業務名10", "type": "select", "value": case_data_009_ZEAF002200.get("gyomu_mei_10", "")},
            {"title": "業務別基準日10", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyomu_betsu_kijunbi_10", "")},
            {"title": "口座基準日10", "type": "text", "value": case_data_009_ZEAF002200.get("koza_kijunbi_10", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_9")

        # 10 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 11 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=200,time_span_sec=20)
        self.screen_shot("実行管理 画面_11")

        # 12 実行管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_12")

        # 13 スケジュール個別追加 画面: <業務名>：収納・滞納 <サブシステム名>：マスタ作成 <処理名>: 収納マスタ＆収納用宛名口座マスタ作成処理（統合版）
        # 14 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_013_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_013_ZEAF000400.get("sabushisutemu_mei"),
                                        shoriNM=case_data_013_ZEAF000400.get("shori_mei"), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_14")

        # 15 スケジュール個別追加 画面:「収納マスタ＆収納用宛名口座マスタ作成本処理(統合版)」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_015_ZEAF000400.get("batch_job_002"), tab_index=tab_index)

        # 16 実行指示 画面: パラメータを入力
        params = [
            {"title": "未納指定", "type": "select", "value": case_data_016_ZEAF002200.get("mino_shitei", "")},
            {"title": "科目", "type": "checkbox", "value": case_data_016_ZEAF002200.get("kamoku_chk").values()},
            {"title": "調定発生年度開始", "type": "select",
             "value": case_data_016_ZEAF002200.get("chotei_hassei_nendo_kaishi", "")},
            {"title": "調定発生年度終了", "type": "select",
             "value": case_data_016_ZEAF002200.get("chotei_hassei_nendo_shuryo", "")},
            {"title": "課税根拠年度開始", "type": "select",
             "value": case_data_016_ZEAF002200.get("kazei_konkyo_nendo_kaishi", "")},
            {"title": "課税根拠年度終了", "type": "select",
             "value": case_data_016_ZEAF002200.get("kazei_konkyo_nendo_shuryo", "")},
            {"title": "期別開始", "type": "text", "value": case_data_016_ZEAF002200.get("kibetsu_kaishi", "")},
            {"title": "期別終了", "type": "text", "value": case_data_016_ZEAF002200.get("kibetsu_shuryo", "")},
            {"title": "納期限開始", "type": "text", "value": case_data_016_ZEAF002200.get("nokigen_kaishi", "")},
            {"title": "納期限終了", "type": "text", "value": case_data_016_ZEAF002200.get("nokigen_shuryo", "")},
            {"title": "除外納付区分", "type": "text", "value": case_data_016_ZEAF002200.get("jogai_nofu_kubun", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_16")

        # 17 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 18 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=180, time_span_sec=20)
        self.screen_shot("実行管理 画面_18")

        # 19 実行管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_19")

        # 20 スケジュール個別追加 画面: <業務名>：収納・滞納 <サブシステム名>：月次 <処理名>: 督促状作成処理
        # 21 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_020_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_020_ZEAF000400.get("sabushisutemu_mei"),
                                        shoriNM=case_data_020_ZEAF000400.get("shori_mei"), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_21")

        # 22 スケジュール個別追加 画面:「督促状作成本処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_022_ZEAF000400.get("batch_job_003"), tab_index=tab_index)

        # 23 実行指示 画面: パラメータを入力
        params = [
            {"title": "科目", "type": "checkbox", "value": case_data_023_ZEAF002200.get("kamoku_chk").values()},
            {"title": "調定発生年度(開始)", "type": "select",
             "value": case_data_023_ZEAF002200.get("tyotei_hassei_nendo_start", "")},
            {"title": "調定発生年度(終了)", "type": "select",
             "value": case_data_023_ZEAF002200.get("tyotei_hassei_nendo_end", "")},
            {"title": "納期限(開始)", "type": "text", "value": case_data_023_ZEAF002200.get("noukigen_start", "")},
            {"title": "納期限(終了)", "type": "text", "value": case_data_023_ZEAF002200.get("noukigen_end", "")},
            {"title": "発送日", "type": "text", "value": case_data_023_ZEAF002200.get("hassoubi", "")},
            {"title": "収納現在日", "type": "text", "value": case_data_023_ZEAF002200.get("shuunou_genzaibi", "")},
            {"title": "支払期限", "type": "text", "value": case_data_023_ZEAF002200.get("shiharai_kigen", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_23")

        # 24 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 25 実行指示 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=180, time_span_sec=20)
        self.screen_shot("実行管理 画面_25")

        # 26 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 27 実行管理 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_27")

        # 28 納品物管理 画面: 納品物「JAAP400100_督促状.pdf」の「ダウンロード」ボタン押下
        # 29 ファイルダウンロード 画面:「No1」ボタン押下
        # 30 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 31 ファイル 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_028_ZEAF002700.get("report_name",""))

        # 32 納品物管理 画面: 納品物「JACP100700_督促状発付一覧表.pdf」の「ダウンロード」ボタン押下
        # 33 ファイルダウンロード 画面:「No1」ボタン押下
        # 34 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 35 ファイル 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_032_ZEAF002700.get("report_name",""))

        # 36 納品物管理 画面: 納品物「JACP100900_督促状発付不能一覧表.CSV」の「ダウンロード」ボタン押下
        # 37 ファイルダウンロード 画面:「No1」ボタン押下
        # 38 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 39 ファイル 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_036_ZEAF002700.get("report_name",""))

        # 40 実行管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_40")

        # 41 スケジュール個別追加 画面:「督促状作成後処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_041_ZEAF000400.get("batch_job_004"), tab_index=tab_index)
        self.screen_shot("実行指示 画面_41")

        # 42 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 43 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=180)
        self.screen_shot("実行管理 画面_43")

        # 44 メインメニュー 画面: メインメニューから「収納」クリック
        # 45 メインメニュー 画面:「個人別収納状況」クリック
        # 46 メインメニュー 画面:「通番明細」ダブルクリック
        self._goto_menu_by_label(menu_level_1="収納", menu_level_2="個人別収納状況",
                                 menu_level_3="通番明細")
        tab_index += 1

        # 47（個人・法人）検索条件入力 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名・名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanaShimeiNM_textboxInput",
            value=case_data_047_JAAF400100.get("kana_shimei_meisho_inp", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKana_select",
            text=case_data_047_JAAF400100.get("kana_shimei_meisho_sel", ""))
        # 漢字氏名・名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanjiShimeiNM_textboxInput",
            value=case_data_047_JAAF400100.get("kanji_shimei_meisho_inp", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKanji_select",
            text=case_data_047_JAAF400100.get("kanji_shimei_meisho_sel", ""))
        # カナ氏名・名称２
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanaShimeiNM2_textboxInput",
            value=case_data_047_JAAF400100.get("kana_shimei_meisho_inp_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKana2_select",
            text=case_data_047_JAAF400100.get("kana_shimei_meisho_sel_2", ""))
        # 漢字氏名・名称２
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanjiShimeiNM2_textboxInput",
            value=case_data_047_JAAF400100.get("kanji_shimei_meisho_inp_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKanji2_select",
            text=case_data_047_JAAF400100.get("kanji_shimei_meisho_sel_2", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtDate01Left_textboxInput",
            value=case_data_047_JAAF400100.get("seinengappi_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiBirth1_select",
            text=case_data_047_JAAF400100.get("seinengappi_sel", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtDate02Left_textboxInput",
            value=case_data_047_JAAF400100.get("seinengappi_to", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selSeibetsu_select",
            text=case_data_047_JAAF400100.get("seibetsu", ""))
        # 行政区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selGyoseiku_select",
            text=case_data_047_JAAF400100.get("gyoseiku", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_tYubinbangouLeftTxt_textboxInput",
            value=case_data_047_JAAF400100.get("yubin_bango_left", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_tYubinbangouRightTxt_textboxInput",
            value=case_data_047_JAAF400100.get("yubin_bango_right", ""))
        # 住所・所在地
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtJusho_textboxInput",
            value=case_data_047_JAAF400100.get("jusho_shozai_inp", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiJusho_select",
            text=case_data_047_JAAF400100.get("jusho_shozai_sel", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKatagaki_textboxInput",
            value=case_data_047_JAAF400100.get("katagaki", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtJuminCD_textboxInput",
            value=case_data_047_JAAF400100.get("jumin_kodo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtSetaiCD_textboxInput",
            value=case_data_047_JAAF400100.get("setai_kodo", ""))
        # 特徴指定番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtTokuchoShiteiNo_textboxInput",
            value=case_data_047_JAAF400100.get("tokucho_shitei_bango", ""))
        # 通知番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtTsuchiNo_textboxInput",
            value=case_data_047_JAAF400100.get("tsuchi_bango", ""))
        # 検索区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selKensakuKbn_select",
            text=case_data_047_JAAF400100.get("kensaku_kubun", ""))
        self.screen_shot("（個人・法人）検索条件入力 画面_47")

        # 48 検索条件入力 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JAAF400100_WrCmnBtn05_button")
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_JAAF400200_pages1"):
            atena_code = case_data_048_JAAF400200.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code, tab_index=tab_index, col_number=1)

        # 49 通番明細 画面: 対象の「№」ボタン押下
        self.select_no_kobetsu_keshikomirow_by_tsuuchisho_bangou(tsuuchisho_bangou=case_data_049_JAAF403400.get("tsuuchisho_bangou"), tab_index=tab_index)
        self.screen_shot("収納状況照会 画面_49")

        # 50 収納状況照会 画面:「詳細表示」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JAAF403000_btnShosaIDisp_button")
        self.screen_shot("収納状況照会（詳細表示）画面_50")

        # 51 収納状況照会（詳細表示）画面:「発布履歴」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JAAF401500_btnHassouRereki_button")
        self.screen_shot("科目別発付履歴一覧 画面_51")

        # 52 科目別発付履歴一覧 画面:「明細追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JACF300200_btnShinki_button")

        # 53 科目別発付履歴更新 画面: 文書名に「督促状」を選択し、発布情報を入力する。
        # 住民情報
        # 文書名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JACF300300_selHenreiTorikeshiHassobutsuKbn_select",
            text=case_data_053_JACF300300.get("bunsho_mei"))
        # 枝番
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JACF300300_txtEdaban_textboxInput",
            value=case_data_053_JACF300300.get("eda_bango"))
        # 発付日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JACF300300_txtTokusokujoHappubi_textboxInput",
            value=case_data_053_JACF300300.get("hassoubi"))
        self.screen_shot("科目別発付履歴更新 画面_53")

        # 54 科目別発付履歴更新 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JACF300300_regbtn_button")

        # 55 科目別発付履歴更新 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_JACF300300_msg_span",
                                 msg="登録しました。")
        self.screen_shot("科目別発付履歴更新 画面_55")

        # 56（個人・法人）検索条件入力 画面: メインメニューから「収納」クリック
        # 57 メインメニュー 画面:「個人別収納状況」クリック
        # 58 メインメニュー 画面:「通番明細」ダブルクリック
        self._goto_menu_by_label(menu_level_1="収納", menu_level_2="個人別収納状況",
                                 menu_level_3="通番明細")
        tab_index += 1

        # 59（個人・法人）検索条件入力 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名・名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanaShimeiNM_textboxInput",
            value=case_data_059_JAAF400100.get("kana_shimei_meisho_inp", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKana_select",
            text=case_data_059_JAAF400100.get("kana_shimei_meisho_sel", ""))
        # 漢字氏名・名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanjiShimeiNM_textboxInput",
            value=case_data_059_JAAF400100.get("kanji_shimei_meisho_inp", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKanji_select",
            text=case_data_059_JAAF400100.get("kanji_shimei_meisho_sel", ""))
        # カナ氏名・名称２
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanaShimeiNM2_textboxInput",
            value=case_data_059_JAAF400100.get("kana_shimei_meisho_inp_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKana2_select",
            text=case_data_059_JAAF400100.get("kana_shimei_meisho_sel_2", ""))
        # 漢字氏名・名称２
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanjiShimeiNM2_textboxInput",
            value=case_data_059_JAAF400100.get("kanji_shimei_meisho_inp_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKanji2_select",
            text=case_data_059_JAAF400100.get("kanji_shimei_meisho_sel_2", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtDate01Left_textboxInput",
            value=case_data_059_JAAF400100.get("seinengappi_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiBirth1_select",
            text=case_data_059_JAAF400100.get("seinengappi_sel", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtDate02Left_textboxInput",
            value=case_data_059_JAAF400100.get("seinengappi_to", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selSeibetsu_select",
            text=case_data_059_JAAF400100.get("seibetsu", ""))
        # 行政区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selGyoseiku_select",
            text=case_data_059_JAAF400100.get("gyoseiku", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_tYubinbangouLeftTxt_textboxInput",
            value=case_data_059_JAAF400100.get("yubin_bango_left", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_tYubinbangouRightTxt_textboxInput",
            value=case_data_059_JAAF400100.get("yubin_bango_right", ""))
        # 住所・所在地
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtJusho_textboxInput",
            value=case_data_059_JAAF400100.get("jusho_shozai_inp", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiJusho_select",
            text=case_data_059_JAAF400100.get("jusho_shozai_sel", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKatagaki_textboxInput",
            value=case_data_059_JAAF400100.get("katagaki", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtJuminCD_textboxInput",
            value=case_data_059_JAAF400100.get("jumin_kodo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtSetaiCD_textboxInput",
            value=case_data_059_JAAF400100.get("setai_kodo", ""))
        # 特徴指定番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtTokuchoShiteiNo_textboxInput",
            value=case_data_059_JAAF400100.get("tokucho_shitei_bango", ""))
        # 通知番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtTsuchiNo_textboxInput",
            value=case_data_059_JAAF400100.get("tsuchi_bango", ""))
        # 検索区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selKensakuKbn_select",
            text=case_data_059_JAAF400100.get("kensaku_kubun", ""))
        self.screen_shot("（個人・法人）検索条件入力 画面_59")

        # 60 検索条件入力 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JAAF400100_WrCmnBtn05_button")
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_JAAF400200_pages1"):
            atena_code = case_data_060_JAAF400200.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code, tab_index=tab_index, col_number=1)

        # 61 通番明細 画面: 対象の「№」ボタン押下
        self.select_no_kobetsu_keshikomirow_by_tsuuchisho_bangou(tsuuchisho_bangou=case_data_061_JAAF403400.get("tsuuchisho_bangou"), tab_index=tab_index)
        self.screen_shot("収納状況照会 画面_61")

        # 62 収納状況照会 画面:「詳細表示」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JAAF403000_btnShosaIDisp_button")
        self.screen_shot("収納状況照会（詳細表示）画面_62")

        # 63 収納状況照会（詳細表示）画面:「発布履歴」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JAAF401500_btnHassouRereki_button")
        self.screen_shot("科目別発付履歴一覧 画面_63")

        # 64 科目別発付履歴一覧 画面:削除対象の「№」ボタン押下
        self.click_button_by_label("1")

        # 65 科目別発付履歴更新 画面:「修正」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JACF300300_btnEditChg_button")
        self.screen_shot("科目別発付履歴更新 画面_65")

        # 66 科目別発付履歴更新 画面:「削除」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JACF300300_deletebtn_button")

        # 67 科目別発付履歴更新 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_JACF300200_msg_span",
                                 msg="削除しました。")
        self.screen_shot("科目別発付履歴一覧 画面_67")
