from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070103(KodomoSiteTestCaseBase):
    """TestQAP010_28070103"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070103(self):
        tab_index = 0
        case_data_077_QAPF105800 = self.test_data["077_QAPF105800"]
        case_data_078_QAPF100100 = self.test_data["078_QAPF100100"]
        case_data_082_QAPF103700 = self.test_data["082_QAPF103700"]
        case_data_091_QAZF100001 = self.test_data["091_QAZF100001"]
        case_data_093_QAZF100002 = self.test_data["093_QAZF100002"]
        case_data_095_QAPF100300 = self.test_data["095_QAPF100300"]
        case_data_098_QAPF103600 = self.test_data["098_QAPF103600"]
        case_data_104_QAPF103600 = self.test_data["104_QAPF103600"]
        case_data_108_QAPF103800 = self.test_data["108_QAPF103800"]
        case_data_115_ZEAF000400 = self.test_data["115_ZEAF000400"]
        case_data_117_ZEAF000400 = self.test_data["117_ZEAF000400"]
        case_data_118_ZEAF002200 = self.test_data["118_ZEAF002200"]
        case_data_123_ZEAF002700 = self.test_data["123_ZEAF002700"]
        case_data_128_ZEAF000400 = self.test_data["128_ZEAF000400"]
        case_data_129_ZEAF002200 = self.test_data["129_ZEAF002200"]
        case_data_135_ZEAF000400 = self.test_data["135_ZEAF000400"]
        case_data_137_ZEAF000400 = self.test_data["137_ZEAF000400"]
        case_data_138_ZEAF002200 = self.test_data["138_ZEAF002200"]
        case_data_143_ZEAF002700 = self.test_data["143_ZEAF002700"]
        case_data_147_ZEAF002700 = self.test_data["147_ZEAF002700"]
        case_data_152_ZEAF000400 = self.test_data["152_ZEAF000400"]
        case_data_153_ZEAF002200 = self.test_data["153_ZEAF002200"]
        case_data_158_ZEAF002700 = self.test_data["158_ZEAF002700"]
        case_data_162_ZEAF002700 = self.test_data["162_ZEAF002700"]
        case_data_167_ZEAF000400 = self.test_data["167_ZEAF000400"]
        case_data_168_ZEAF002200 = self.test_data["168_ZEAF002200"]
        case_data_173_ZEAF002700 = self.test_data["173_ZEAF002700"]
        case_data_177_ZEAF002700 = self.test_data["177_ZEAF002700"]

        # 73 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 74 メインメニュー 画面: メインメニューから「子ども子育て支援」をクリックする
        # 75 メインメニュー 画面: 「入所管理」をクリック
        # 76 メインメニュー 画面: 「児童検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="入所管理", menu_level_3="児童検索",
                                 is_new_tab=True)
        tab_index += 1

        # 77 検索条件入力 画面: 検索条件を入力
        # 検索条件1:
        # 保護者カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuHogoshaKanaShimei_textboxInput",
            value=case_data_077_QAPF105800.get("hogosha_kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuHogoshaKanaShimeiOption_select",
            text=case_data_077_QAPF105800.get("hogosha_kana_shimei_cbx", ""))
        # 保護者氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuHogoshaKanjiShimei_textboxInput",
            value=case_data_077_QAPF105800.get("hogosha_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuHogoshaKanjiShimeiOption_select",
            text=case_data_077_QAPF105800.get("hogosha_shimei_cbx", ""))
        # 児童カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuJidoKanaShimei_textboxInput",
            value=case_data_077_QAPF105800.get("jidou_kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuJidoKanaShimeiOption_select",
            text=case_data_077_QAPF105800.get("jidou_kana_shimei_cbx", ""))
        # 児童氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuJidoKanjiShimei_textboxInput",
            value=case_data_077_QAPF105800.get("jidou_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuJidoKanjiShimeiOption_select",
            text=case_data_077_QAPF105800.get("jidou_shimei_cbx", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuSeinengappiStart_textboxInput",
            value=case_data_077_QAPF105800.get("seinengappi_ymd_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selAimaiBirth1_select",
            text=case_data_077_QAPF105800.get("seinengappi_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuSeinengappiEnd_textboxInput",
            value=case_data_077_QAPF105800.get("seinengappi_ymd_to", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuSeibetsu_select",
            text=case_data_077_QAPF105800.get("seibetsu_cbx", ""))
        # 宛名コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuAtenaCD_textboxInput",
            value=case_data_077_QAPF105800.get("atena_code", ""))
        # 状態区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuJotaiKbn_select",
            text=case_data_077_QAPF105800.get("joutai_kubun_cbx", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selWrGyoseiku_JidoKensakuShokanKu_select",
            text=case_data_077_QAPF105800.get("shokanku_cbx", ""))
        # 入所番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuNyushoNo_textboxInput",
            value=case_data_077_QAPF105800.get("nyuusho_bangou", ""))
        # 入所申込番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuMoushikomiNo_textboxInput",
            value=case_data_077_QAPF105800.get("nyuusho_moushikomi_bangou", ""))

        # 検索条件2:
        # 施設コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtShisetsuKensakuJidoKensakuShisetsuCD_textboxInput",
            value=case_data_077_QAPF105800.get("shisetsu_code", ""))
        # 施設カナ名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtShisetsuKensakuJidoKensakuShisetsuKanaNM_textboxInput",
            value=case_data_077_QAPF105800.get("shisetsu_kana_meishou", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selShisetsuKensakuJidoKensakuShisetsuKanaNMOption_select",
            text=case_data_077_QAPF105800.get("shisetsu_kana_meishou_cbx", ""))
        # 施設種類
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selShisetsuKensakuJidoKensakuShisetsuShurui_select",
            text=case_data_077_QAPF105800.get("shisetsu_shurui_cbx", ""))
        # 所在区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selWrGyoseiku_ShisetsuKensakuJidoKensakuShozaiKu_select",
            text=case_data_077_QAPF105800.get("shozai_ku_cbx", ""))

        # 検索条件3:
        # 認定者番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtNinteiINFOKensakuNinteiBango_textboxInput",
            value=case_data_077_QAPF105800.get("ninteisha_bangou", ""))
        # 認定区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuNinteiKbn_select",
            text=case_data_077_QAPF105800.get("nintei_kubun_cbx", ""))
        # 必要性事由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuHitsuyoseiJiyu_select",
            text=case_data_077_QAPF105800.get("hitsuyousei_jiyuu_cbx", ""))
        # 保育必要量
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuHoikuHitsuyoryo_select",
            text=case_data_077_QAPF105800.get("hoiku_hitsuyou_ryou_cbx", ""))
        # 優先利用
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuYusenRiyo_select",
            text=case_data_077_QAPF105800.get("yuusen_riyou_cbx", ""))
        # 決定年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuKetteiDay_select",
            text=case_data_077_QAPF105800.get("kettei_nengappi_cbx", ""))
        # 負担区分決定年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuFutanKbnKetteiDay_select",
            text=case_data_077_QAPF105800.get("futan_kubun_kettei_nengappi_cbx", ""))

        # 検索条件4:
        # 年度
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNendoKensakuNendo_select",
            text=case_data_077_QAPF105800.get("nendo_cbx", ""))
        # クラス年齢
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNendoKensakuClassAge_select",
            text=case_data_077_QAPF105800.get("class_nerai_cbx", ""))
        # 入所番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtNendoKensakuNyushoNo_textboxInput",
            value=case_data_077_QAPF105800.get("kana_shimei_text", ""))

        self.screen_shot("検索条件入力 画面_77")

        # 78 検索条件入力 画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF105800_WrCmnBtn05_button")

        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAPF100100_pages1"):
            atena_code = case_data_078_QAPF100100.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code=atena_code, tab_index=tab_index, col_number=7)

        # 79 児童台帳 画面: 「入所管理」タブをクリック
        self.change_tab_by_label(tab_id=tab_index, label="入所管理", screen_id="QAPF103500")
        self.screen_shot("児童台帳_入所管理 画面_79")

        # 80 児童台帳 画面: 「入所管理」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103500_btnNyushoKanri_button")

        # 81 入所管理（申込） 画面: 「修正（取下げ）」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103700_btnShuseiTorisage_button")
        self.screen_shot("入所管理（申込） 画面_81")

        # 82 入所管理（申込） 画面: 更新区分に「取下げ登録・変更」を選択肢
        # 83 入所管理（申込） 画面: 理由に「その他」を選択
        # 84 入所管理（申込） 画面: 備考に「テスト入力」と入力
        # 取下年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtTorisageYMD_textboxInput",
            value=case_data_082_QAPF103700.get("torishimo_nengappi_ymd", ""))
        # 更新区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selTorisageKoshinKbn_select",
            text=case_data_082_QAPF103700.get("koushin_kubun_cbx", ""))
        # 理由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_selTorisageRiyu_select",
            text=case_data_082_QAPF103700.get("riyuu_cbx", ""))
        # 備考
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103700_txtTorisageBiko_textarea",
            value=case_data_082_QAPF103700.get("bikou", ""))
        self.screen_shot("入所管理（申込） 画面_85")

        # 85 入所管理（申込） 画面: 「入力チェック」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103700_checkbtn_button")
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF103700_msg_span",
                                 msg="入力チェックが完了しました。")

        # 86 入所管理（申込） 画面: 「登録」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103700_regbtn_button")

        # 87 入所管理（申込） 画面: 「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF103700_msg_span", msg="更新しました。")
        self.screen_shot("入所管理（申込） 画面_87")

        # 88 メインメニュー 画面: メインメニューから「子ども子育て支援」をクリックする
        # 89 メインメニュー 画面: 「世帯情報」をクリック
        # 90 メインメニュー 画面: 「検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=False)
        tab_index += 1

        # 91 検索条件入力 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_091_QAZF100001.get("kana_shimei_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "QAZF100001_selAimaiKana_select",
            text=case_data_091_QAZF100001.get("kana_shimei_select", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_091_QAZF100001.get("kanji_shimei_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_091_QAZF100001.get("kanji_shimei_select", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_091_QAZF100001.get("seinengappi_1_ymd", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "tab0_QAZF100001_selAimaiBirth1_select",
            text=case_data_091_QAZF100001.get("seinengappi_select", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_091_QAZF100001.get("seinengappi_2_ymd", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_091_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_091_QAZF100001.get("yuubin_bangou_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_091_QAZF100001.get("yuubin_bangou_2", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_091_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_091_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_091_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_091_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_091_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_091_QAZF100001.get("denwa_bangou_ichi", ""))

        self.screen_shot("検索条件入力 画面_91")

        # 92 検索条件入力 画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 93 世帯履歴 画面: 「No1」ボタン押下
        # 世帯履歴 画面: 支給認定情報を登録する世帯台帳履歴の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_093_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code=atena_code, tab_index=tab_index, col_number=1)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳 画面_93")

        # 94 世帯台帳 画面: 「支給認定履歴」タブをクリック
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100300", label="支給認定履歴")
        self.screen_shot("世帯台帳_支給認定履歴 画面_94")

        # 95 世帯台帳 画面: 「No1」ボタン押下
        self.click_shikyuu_nintei_rireki_by_atena_code(kodomo_atena_code=case_data_095_QAPF100300.get("atena_code", ""),
                                                       tab_index=tab_index)

        # 96 支給認定情報 画面: 「修正」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103600_btnEditChg_button")

        # 97 支給認定情報 画面: 「支給認定結果」タブをクリック
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103600", label="支給認定結果")

        # 98 支給認定情報 画面: 時間区分(保育必要量)を変更します。
        # 認定・却下決定日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_txtNinteiKekkaHitsuyoseiNinteiKetteiDay_textboxInput",
            value=case_data_098_QAPF103600.get("nintei_kyakka_ketteibi_ymd", ""))
        # 認定結果
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_selNinteiKekkaNinteiKekka_select",
            text=case_data_098_QAPF103600.get("nintei_kekka_cbx", ""))
        # 支給認定区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_selNinteiKekkaShikyuNinteiKbn_select",
            text=case_data_098_QAPF103600.get("shikyuu_nintei_kubun_cbx", ""))
        # 認定期間_from_to
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_txtNinteiKekkaNinteiKikanStart_textboxInput",
            value=case_data_098_QAPF103600.get("nintei_kikan_from_ymd", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_txtNinteiKekkaNinteiKikanEnd_textboxInput",
            value=case_data_098_QAPF103600.get("nintei_kikan_to_ymd", ""))
        # 保育の必要性(事由)
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_selNinteiKekkaHoikuHitsuyoseiJiyu_select",
            text=case_data_098_QAPF103600.get("hoiku_no_hitsuyousei_jiyuu_cbx", ""))
        # 保育の必要性(続柄)
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_selNinteiKekkaHoikuHitsuyoseiZokugara_select",
            text=case_data_098_QAPF103600.get("hoiku_no_hitsuyousei_zokugara_cbx", ""))
        # 負担区分決定日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_QAPF103600_txtNinteiKekkaFutanKbnKetteiDay_textboxInput",
            value=case_data_098_QAPF103600.get("futan_kubun_ketteibi_cbx", ""))
        # 認定しない理由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_txtNinteiKekkaNinteiSinaiRiyu_select",
            text=case_data_098_QAPF103600.get("nintei_shinai_riyuu_cbx", ""))
        # 取消理由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_selNinteiKekkaTorikeshiRiyu_select",
            text=case_data_098_QAPF103600.get("torikeshi_riyuu_cbx", ""))
        # 取消年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_txtNinteiKekkaTorikeshiYMD_textboxInput",
            value=case_data_098_QAPF103600.get("torikeshi_nengappi_cbx", ""))
        # 時間区分(保育必要量)
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_selNinteiKekkaTimeKbnHoikuHitsuyoryo_select",
            text=case_data_098_QAPF103600.get("jikan_kubun_hoiku_hitsuyouryou_cbx", ""))
        # 優先利用事由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_selNinteiKekkaYusenRiyoJiyu_select",
            text=case_data_098_QAPF103600.get("yuusen_riyuu_cbx", ""))
        # 備考
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103600_txtNinteiKekkaBiko_textboxInput",
            value=case_data_098_QAPF103600.get("bikou_cbx", ""))

        self.screen_shot("支給認定情報 画面_98")

        # 99 支給認定情報 画面: 「入力チェック」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103600_checkbtn_button")
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF103600_msg_span",
                                 msg="入力チェックが完了しました。")

        # 100 支給認定情報 画面: 「登録」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103600_regbtn_button")

        # 101 支給認定情報 画面: 「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF103600_msg_span",
                                 msg="更新しました。")
        self.screen_shot("支給認定情報 画面_101")

        # 102 支給認定情報 画面: パンくず「世帯台帳」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="QAPF103600", label="世帯台帳")

        # 103 世帯台帳 画面: 「児童一覧」タブをクリック
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100300", label="児童一覧")
        self.screen_shot("世帯台帳_児童一覧 画面_103")

        # 104 世帯台帳 画面: 「No1」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_104_QAPF103600.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)

        # 105 児童台帳 画面: 「入所管理」タブをクリック
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103500", label="入所管理")
        self.screen_shot("児童台帳_入所管理 画面_105")

        # 106 児童台帳 画面: 「契約情報」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103500_btnKeiyakuINFO_button")

        # 107 契約情報 画面: 「修正」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103800_btnEditChg_button")

        # 108 契約情報 画面: 保育必要量を変更します。
        # 契約施設
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_txtKeiyakuShisetsuCD_textboxInput",
            value=case_data_108_QAPF103800.get("keiyaku_shisetsu", ""))

        # 届出年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_txtTodokedeYMD_textboxInput",
            value=case_data_108_QAPF103800.get("todokede_nengappi_ymd", ""))

        # 契約年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_txtKeiyakuYMD_textboxInput",
            value=case_data_108_QAPF103800.get("keiyaku_nengappi_ymd", ""))

        # 認定区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_selKeiyakuNinteiKbn_select",
            text=case_data_108_QAPF103800.get("nintei_kubun_cbx", ""))

        # 保育必要量
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_selKeiyakuHoikuHitsuyoryo_select",
            text=case_data_108_QAPF103800.get("hoiku_hitsuyouryou_cbx", ""))

        # 契約期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_txtRiyoKikanStart_textboxInput",
            value=case_data_108_QAPF103800.get("keiyaku_kikan_from_ymd", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_txtRiyoKikanEnd_textboxInput",
            value=case_data_108_QAPF103800.get("keiyaku_kikan_to_ymd", ""))

        # 利用曜日 checkbox
        # 月
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_chkRiyoYoubichk0",
            value=case_data_108_QAPF103800.get("riyou_youbi_getsu_chk", ""))
        # 火
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_chkRiyoYoubichk1",
            value=case_data_108_QAPF103800.get("riyou_youbi_ka_chk", ""))
        # 水
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_chkRiyoYoubichk2",
            value=case_data_108_QAPF103800.get("riyou_youbi_sui_chk", ""))
        # 木
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_chkRiyoYoubichk3",
            value=case_data_108_QAPF103800.get("riyou_youbi_moku_chk", ""))
        # 金
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_chkRiyoYoubichk4",
            value=case_data_108_QAPF103800.get("riyou_youbi_kin_chk", ""))
        # 土
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_chkRiyoYoubichk5",
            value=case_data_108_QAPF103800.get("riyou_youbi_do_chk", ""))
        # 日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_chkRiyoYoubichk6",
            value=case_data_108_QAPF103800.get("riyou_youbi_nichi_cbx", ""))

        # 利用時間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_selRiyoTimeStartHour_select",
            text=case_data_108_QAPF103800.get("riyou_jikan_start_hour_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_selRiyoTimeStartMinute_select",
            text=case_data_108_QAPF103800.get("riyou_jikan_start_minute_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_selRiyoTimeEndHour_select",
            text=case_data_108_QAPF103800.get("riyou_jikan_end_hour_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_selRiyoTimeEndMinute_select",
            text=case_data_108_QAPF103800.get("riyou_jikan_end_minute_cbx", ""))

        # 確認入力済
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_chkKakuninNyuryokuZumichk0",
            value=case_data_108_QAPF103800.get("kakunin_nyuuryoku_zumi_chk", ""))

        # 延長保育利用
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_chkEnchoHoikuRiyoUmuchk0",
            value=case_data_108_QAPF103800.get("enchou_hoiku_riyou_chk", ""))

        # 特記事項
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF103800_fldTokkijiko_textarea",
            value=case_data_108_QAPF103800.get("tokkijikou", ""))
        self.screen_shot("契約情報 画面_108")

        # 109 契約情報 画面: 「入力チェック」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103800_checkbtn_button")
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF103800_msg_span",
                                 msg="入力チェックが完了しました。")

        # 110 契約情報 画面: 「登録」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103800_regbtn_button")

        # 111 契約情報 画面: 「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF103800_msg_span",
                                 msg="更新しました。")
        self.screen_shot("契約情報 画面_111")

        # 112 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 113 メインメニュー 画面: 「即時実行」クリック
        # 114 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=False)
        tab_index += 1

        # 115 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：賦課 <処理名>：月次賦課処理
        # 選択"
        # 116 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_115_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_115_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_115_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_116")

        # 117 スケジュール個別追加 画面: 「整合性チェック」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_117_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 118 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_118_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select", "value": case_data_118_ZEAF002200.get("shisho_cbx", "")},
            {"title": "開始年月", "type": "text", "value": case_data_118_ZEAF002200.get("kaishi_nengetsu_ymd", "")},
            {"title": "終了年月", "type": "text", "value": case_data_118_ZEAF002200.get("shuuryou_nengetsu_ymd", "")},
            {"title": "チェック項目設定", "type": "select",
             "value": case_data_118_ZEAF002200.get("check_item_settei_cbx", "")},
            {"title": "並び順", "type": "select",
             "value": case_data_118_ZEAF002200.get("narabijun_cbx", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_118")

        # 119 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 120 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_120")

        # 121 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 122 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("結果確認 画面_122")

        # 123 結果確認 画面: 納品物「整合性チェック_エラー.csv」の「ダウンロード」ボタン押下
        # 124 納品物管理 画面: 「No1」ボタン押下
        # 125 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 126 ファイルダウンロード 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime,
                                   tab_index=tab_index, report_name=case_data_123_ZEAF002700.get("report_name", ""))

        # 127 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_127")

        # 128 スケジュール個別追加 画面: 「月次賦課計算処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_128_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 129 実行指示 画面: パラメータを入力
        params = [
            {"title": "対象年月", "type": "text", "value": case_data_129_ZEAF002200.get("taishou_nengetsu_ymd", "")},
            {"title": "対象年度", "type": "text", "value": case_data_129_ZEAF002200.get("taishou_nendo_y", "")},
            {"title": "基準年月", "type": "text", "value": case_data_129_ZEAF002200.get("kijun_nengetsu_ymd", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_129")

        # 130 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 131 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_131")

        # 132 実行管理 画面: メインメニューから「バッチ管理」クリック
        # 133 メインメニュー 画面: 「即時実行」クリック
        # 134 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=False)
        tab_index += 1

        # 135 スケジュール個別追加 画面: "<業務名>：子ども子育て支援 <サブシステム名>：賦課 <処理名>：月次賦課処理
        # 選択" 
        # 136 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_135_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_135_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_135_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_136")

        # 137 スケジュール個別追加 画面: 「調定者一覧表作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_137_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)

        # 138 実行指示 画面: パラメータを入力
        params = [
            {"title": "データ種別", "type": "select", "value": case_data_138_ZEAF002200.get("data_shubetsu_cbx", "")},
            {"title": "科目", "type": "select", "value": case_data_138_ZEAF002200.get("kamoku_cbx", "")},
            {"title": "所管区", "type": "select", "value": case_data_138_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select", "value": case_data_138_ZEAF002200.get("shisho_cbx", "")},
            {"title": "収納方法", "type": "select",
             "value": case_data_138_ZEAF002200.get("shuunou_houhou_cbx", "")},
            {"title": "出力対象", "type": "select",
             "value": case_data_138_ZEAF002200.get("shutsuryoku_taishou_cbx", "")},
            {"title": "対象年月開始", "type": "text",
             "value": case_data_138_ZEAF002200.get("taishou_nengetsu_kaishi_ymd", "")},
            {"title": "対象年月終了", "type": "text",
             "value": case_data_138_ZEAF002200.get("taishou_nengetsu_shuuryou_ymd", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行管理 画面_138")

        # 139 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 140 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_140")

        # 141 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 142 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理_画面_142")

        # 143 納品物管理 画面: 納品物「QAPP109700_施設別調定者一覧表.pdf」の「ダウンロード」ボタン押下 
        # 144 ファイルダウンロード 画面: 「No1」ボタン押下
        # 145 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 146 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime,
                                   tab_index=tab_index, report_name=case_data_143_ZEAF002700.get("report_name", ""))

        # 147 納品物管理 画面: 納品物「施設別調定者一覧表.csv」の「ダウンロード」ボタン押下 
        # 148 ファイルダウンロード 画面: 「No2」ボタン押下
        # 149 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 150 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_147_ZEAF002700.get("report_name", ""))

        # 151 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_151")

        # 152 スケジュール個別追加 画面: 「調定額集計表（個票）作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_152_ZEAF000400.get("batch_job_004", ""),
                                             tab_index=tab_index)

        # 153 実行指示 画面: パラメータを入力
        params = [
            {"title": "データ種別", "type": "select", "value": case_data_153_ZEAF002200.get("data_shubetsu_cbx", "")},
            {"title": "科目", "type": "select", "value": case_data_153_ZEAF002200.get("kamoku_cbx", "")},
            {"title": "所管区", "type": "select", "value": case_data_153_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select", "value": case_data_153_ZEAF002200.get("shisho_cbx", "")},
            {"title": "施設種類", "type": "select",
             "value": case_data_153_ZEAF002200.get("shisetsu_shurui_cbx", "")},
            {"title": "出力対象", "type": "select",
             "value": case_data_153_ZEAF002200.get("shutsuryoku_taishou_cbx", "")},
            {"title": "対象年度", "type": "text",
             "value": case_data_153_ZEAF002200.get("taishou_nendo_ymd", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_153")

        # 154 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 155 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_155")

        # 156 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 157 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理_画面_157")

        # 158 納品物管理 画面: 納品物「QAPP109800_調定額集計表（個票）.pdf」の「ダウンロード」ボタン押下 
        # 159 ファイルダウンロード 画面: 「No1」ボタン押下
        # 160 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 161 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime,
                                   tab_index=tab_index, report_name=case_data_158_ZEAF002700.get("report_name", ""))

        # 162 納品物管理 画面: 納品物「調定額集計表（個票）.csv」の「ダウンロード」ボタン押下 
        # 163 ファイルダウンロード 画面: 「No2」ボタン押下
        # 164 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 165 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_162_ZEAF002700.get("report_name", ""))

        # 166 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")

        self.screen_shot("スケジュール個別追加_画面_166")

        # 167 スケジュール個別追加 画面: 「調定額集計表（総括）」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_167_ZEAF000400.get("batch_job_005", ""),
                                             tab_index=tab_index)
        # 168 実行指示 画面: パラメータを入力
        params = [
            {"title": "データ種別", "type": "select", "value": case_data_168_ZEAF002200.get("deeta_shubetsu_cbx", "")},
            {"title": "科目", "type": "select", "value": case_data_168_ZEAF002200.get("kamoku_cbx", "")},
            {"title": "所管区", "type": "select", "value": case_data_168_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select", "value": case_data_168_ZEAF002200.get("shisho_cbx", "")},
            {"title": "収納方法", "type": "select", "value": case_data_168_ZEAF002200.get("shisetsu_shurui_cbx", "")},
            {"title": "出力対象", "type": "select",
             "value": case_data_168_ZEAF002200.get("shutsuryoku_taishou_cbx", "")},
            {"title": "対象年度", "type": "text",
             "value": case_data_168_ZEAF002200.get("taishou_nendo_ymd", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_168")

        # 169 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 170 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_170")

        # 171 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 172 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理_画面_172")

        # 173 納品物管理 画面: 納品物「QAPP1D0707_調定額集計表（総括）.pdf」の「ダウンロード」ボタン押下
        # 174 ファイルダウンロード 画面: 「No1」ボタン押下
        # 175 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 176 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime,
                                   tab_index=tab_index, report_name=case_data_173_ZEAF002700.get("report_name", ""))

        # 177 納品物管理 画面: 納品物「調定額集計表（総括）.csv」の「ダウンロード」ボタン押下 
        # 178 ファイルダウンロード 画面: 「No2」ボタン押下
        # 179 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 180 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_177_ZEAF002700.get("report_name", ""))
