from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070401(KodomoSiteTestCaseBase):
    """Test_QAP010_28070401"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070401(self):
        tab_index = 0
        case_data_006_ZEAF000400 = self.test_data["006_ZEAF000400"]
        case_data_008_ZEAF000400 = self.test_data["008_ZEAF000400"]
        case_data_009_ZEAF002200 = self.test_data["009_ZEAF002200"]
        case_data_013_ZEAF000400 = self.test_data["013_ZEAF000400"]
        case_data_015_ZEAF000400 = self.test_data["015_ZEAF000400"]
        case_data_016_ZEAF002200 = self.test_data["016_ZEAF002200"]
        case_data_020_ZEAF000400 = self.test_data["020_ZEAF000400"]
        case_data_022_ZEAF000400 = self.test_data["022_ZEAF000400"]
        case_data_023_ZEAF002200 = self.test_data["023_ZEAF002200"]

        # 1, 2 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 3 メインメニュー 画面:メインメニューから「バッチ管理」クリック
        # 4 メインメニュー 画面:「即時実行」クリック
        # 5 メインメニュー 画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=True)
        tab_index += 1
        self.wait_page_loaded(wait_timeout=10)

        # 6 スケジュール個別追加 画面: <業務名>：宛名情報管理 <サブシステム名>：宛名 <処理名>：宛名・口座バッチマスタ作成処理
        # 7 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_006_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_006_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_006_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_7")

        # 8 スケジュール個別追加 画面:「宛名・口座バッチマスタ作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_008_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 9 実行指示 画面:パラメータを入力
        params = [
            {"title": "業務名１", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_1", "")},
            {"title": "業務別基準日1", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijun_bi_1", "")},
            {"title": "口座基準日1", "type": "text", "value": case_data_009_ZEAF002200.get("kouza_kijun_bi_1", "")},
            {"title": "業務名2", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_2", "")},
            {"title": "業務別基準日2", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijun_bi_2", "")},
            {"title": "口座基準日2", "type": "text", "value": case_data_009_ZEAF002200.get("kouza_kijun_bi_2", "")},
            {"title": "業務名3", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_3", "")},
            {"title": "業務別基準日3", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijun_bi_3", "")},
            {"title": "口座基準日3", "type": "text", "value": case_data_009_ZEAF002200.get("kouza_kijun_bi_3", "")},
            {"title": "業務名4", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_4", "")},
            {"title": "業務別基準日4", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijun_bi_4", "")},
            {"title": "口座基準日4", "type": "text", "value": case_data_009_ZEAF002200.get("kouza_kijun_bi_4", "")},
            {"title": "業務名5", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_5", "")},
            {"title": "業務別基準日5", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijun_bi_5", "")},
            {"title": "口座基準日5", "type": "text", "value": case_data_009_ZEAF002200.get("kouza_kijun_bi_5", "")},
            {"title": "業務名6", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_6", "")},
            {"title": "業務別基準日6", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijun_bi_6", "")},
            {"title": "口座基準日6", "type": "text", "value": case_data_009_ZEAF002200.get("kouza_kijun_bi_6", "")},
            {"title": "業務名7", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_7", "")},
            {"title": "業務別基準日7", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijun_bi_7", "")},
            {"title": "口座基準日7", "type": "text", "value": case_data_009_ZEAF002200.get("kouza_kijun_bi_7", "")},
            {"title": "業務名8", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_8", "")},
            {"title": "業務別基準日8", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijun_bi_8", "")},
            {"title": "口座基準日8", "type": "text", "value": case_data_009_ZEAF002200.get("kouza_kijun_bi_8", "")},
            {"title": "業務名9", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_9", "")},
            {"title": "業務別基準日9", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijun_bi_9", "")},
            {"title": "口座基準日9", "type": "text", "value": case_data_009_ZEAF002200.get("kouza_kijun_bi_9", "")},
            {"title": "業務名10", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_10", "")},
            {"title": "業務別基準日10", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijun_bi_10", "")},
            {"title": "口座基準日10", "type": "text", "value": case_data_009_ZEAF002200.get("kouza_kijun_bi_10", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_9")

        # 10 実行指示 画面:「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 11 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_11")

        # 12 実行管理 画面:パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(label="スケジュール個別追加", tab_index=tab_index, screen_id="ZEAF002300")
        self.screen_shot("スケジュール個別追加 画面_12")

        # 13 スケジュール個別追加 画面: <業務名>：収納・滞納 <サブシステム名>：マスタ作成 <処理名>：収納マスタ＆収納用宛名口座マスタ作成処理（統合版）
        # 14 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_013_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_013_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_013_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_14")

        # 15 スケジュール個別追加 画面:「収納マスタ＆収納用宛名口座マスタ作成本処理（統合版）」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_015_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 16 実行指示 画面:パラメータを入力
        params = [
            {"title": "未納指定", "type": "select", "value": case_data_016_ZEAF002200.get("minou_shitei", "")},
            {"title": "科目", "type": "checkbox", "value": case_data_016_ZEAF002200.get("kamoku_chk").values()},
            {"title": "調定発生年度開始", "type": "select",
             "value": case_data_016_ZEAF002200.get("choutei_hassei_nendo_start", "")},
            {"title": "調定発生年度終了", "type": "select",
             "value": case_data_016_ZEAF002200.get("choutei_hassei_nendo_end", "")},
            {"title": "課税根拠年度開始", "type": "select",
             "value": case_data_016_ZEAF002200.get("kazei_konkyo_nendo_start", "")},
            {"title": "課税根拠年度終了", "type": "select",
             "value": case_data_016_ZEAF002200.get("kazei_konkyo_nendo_end", "")},
            {"title": "期別開始", "type": "text", "value": case_data_016_ZEAF002200.get("kibetsu_start", "")},
            {"title": "期別終了", "type": "text", "value": case_data_016_ZEAF002200.get("kibetsu_end", "")},
            {"title": "納期限開始", "type": "text", "value": case_data_016_ZEAF002200.get("noukigen_start", "")},
            {"title": "納期限終了", "type": "text", "value": case_data_016_ZEAF002200.get("noukigen_end", "")},
            {"title": "除外納付区分", "type": "text", "value": case_data_016_ZEAF002200.get("jyokainou_fukubun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_16")

        # 17 実行指示 画面:「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 18 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_18")

        # 19 実行管理 画面:パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(label="スケジュール個別追加", tab_index=tab_index, screen_id="ZEAF002300")
        self.screen_shot("スケジュール個別追加 画面_19")

        # 20 スケジュール個別追加 画面: <業務名>：収納・滞納 <サブシステム名>：月次 <処理名>：口座振替請求処理
        # 21 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_020_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_020_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_020_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_21")

        # 22 スケジュール個別追加 画面:「口座振替請求本処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_022_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)

        # 23 実行指示 画面:パラメータを入力
        params = [
            {"title": "科目", "type": "checkbox", "value": case_data_023_ZEAF002200.get("kamoku_chk").values()},
            {"title": "課税根拠年度（開始)", "type": "select",
             "value": case_data_023_ZEAF002200.get("kazei_konkyo_nendo_start", "")},
            {"title": "課税根拠年度（終了)", "type": "select",
             "value": case_data_023_ZEAF002200.get("kazei_konkyo_nendo_end", "")},
            {"title": "調定発生年度（開始)", "type": "select",
             "value": case_data_023_ZEAF002200.get("choutei_hassei_nendo_start", "")},
            {"title": "調定発生年度（終了)", "type": "select",
             "value": case_data_023_ZEAF002200.get("choutei_hassei_nendo_end", "")},
            {"title": "期別（開始)", "type": "text", "value": case_data_023_ZEAF002200.get("kibetsu_start", "")},
            {"title": "期別（終了)", "type": "text", "value": case_data_023_ZEAF002200.get("kibetsu_end", "")},
            {"title": "納期限（開始)", "type": "text", "value": case_data_023_ZEAF002200.get("noukigen_start", "")},
            {"title": "納期限（終了)", "type": "text", "value": case_data_023_ZEAF002200.get("noukigen_end", "")},
            {"title": "引落日", "type": "text", "value": case_data_023_ZEAF002200.get("hikiotoshi", "")},
            {"title": "口振除外条件", "type": "select", "value": case_data_023_ZEAF002200.get("nofusi_xyokubun", "")},
            {"title": "帳票連絡先編集条件", "type": "select",
             "value": case_data_023_ZEAF002200.get("renrakusaki_kubun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_23")

        # 24 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 25 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_25")

        # 26 実行管理 画面:正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 27 結果確認画面:「納品物確認」ボタン押下
        self.click_button_by_label("納品物確認")
        self.screen_shot("納品物管理 画面_27")

        # 28 納品物管理 画面: 納品物「口座振替ファイル.txt」の「ダウンロード」ボタン押下
        # 29 ファイルダウンロード 画面:「No1」ボタン押下
        # 30 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 31 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index, report_name="口座振替ファイル.txt")

        # 32 納品物管理 画面: 納品物「口座振替請求一覧表.pdf」の「ダウンロード」ボタン押下
        # 33 ファイルダウンロード 画面:「No1」ボタン押下
        # 34 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 35 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name="口座振替請求一覧表.pdf")

        # 36 納品物管理 画面: 納品物「口座振替請求集計表.pdf」の「ダウンロード」ボタン押下
        # 37 ファイルダウンロード 画面:「No1」ボタン押下
        # 38 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 39 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name="口座振替請求集計表.pdf")
