from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070104(KodomoSiteTestCaseBase):
    """TestQAP010_28070104"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070104(self):
        tab_index = 0
        case_data_185_ZEAF000400 = self.test_data["185_ZEAF000400"]
        case_data_187_ZEAF000400 = self.test_data["187_ZEAF000400"]
        case_data_188_ZEAF002200 = self.test_data["188_ZEAF002200"]
        case_data_192_ZEAF000400 = self.test_data["192_ZEAF000400"]
        case_data_194_ZEAF000400 = self.test_data["194_ZEAF000400"]
        case_data_195_ZEAF002200 = self.test_data["195_ZEAF002200"]

        # 181 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 182 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 183 メインメニュー 画面: 「即時実行」クリック
        # 184 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1

        # 185 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：賦課 <処理名>：宛名・月次賦課処理
        # 選択"
        # 186 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_185_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_185_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_185_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_186")

        # 187 スケジュール個別追加 画面: 「月次調定データ作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_187_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 188 実行指示 画面: パラメータを入力
        params = [
            {"title": "対象年月", "type": "text", "value": case_data_188_ZEAF002200.get("taishou_nengetsu_ymd", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_188")

        # 189 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 190 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_190")

        # 191 実行管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_191")

        # 192 スケジュール個別追加 画面: <業務名>：収納・滞納 <サブシステム名>：月次 <処理名>：例月異動処理
        # 選択"
        # 193 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_192_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_192_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_192_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_193")

        # 194 スケジュール個別追加 画面: 「例月異動本処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_194_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 195 実行指示 画面: パラメータを入力
        # 科目
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZEAF002200_JAABN00500_GyomuCodechk0",
            value=case_data_195_ZEAF002200.get("kamoku_kouho_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZEAF002200_JAABN00500_GyomuCodechk1",
            value=case_data_195_ZEAF002200.get("kamoku_enchou_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZEAF002200_JAABN00500_GyomuCodechk2",
            value=case_data_195_ZEAF002200.get("kamoku_kyuushoku_chk", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZEAF002200_JAABN00500_GyomuCodechk3",
            value=case_data_195_ZEAF002200.get("kamoku_shiho_chk", ""))
        # 前回納付書発行用情報
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZEAF002200_JAABN00500_Deletekubun_select",
            text=case_data_195_ZEAF002200.get("zenkai_noufusho_hakkouyou_jouhou_cbx", ""))
        self.screen_shot("実行指示 画面_195")

        # 196 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 197 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_197")
