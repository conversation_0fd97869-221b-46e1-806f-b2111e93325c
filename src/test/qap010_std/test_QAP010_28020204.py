import time
import unittest
from datetime import datetime, date, timedelta
from base.kodomo_case import KodomoSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select
from selenium.webdriver.support.select import Select

class TestQAP010_28020204(KodomoSiteTestCaseBase):
    """TestQAP010_28020204"""

    def setUp(self):
        settings = self.common_test_data
        self.test_data = settings.get("test_qap010_28020204")
        atena_list = settings.get("test_qap010_28020204")
        # self.exec_sqlfile("QAP010_28020204_更新スクリプト.sql", params=atena_list)
        super().setUp()
    
    # 選考にかかわる各種帳票が作成できることを確認する。
    def test_QAP010_28020204(self):
        """通知書作表"""
        
        driver = None

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー 画面_1" , True)
        
        # 2 メインメニュー画面: 「バッチ管理」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        time.sleep(1)
        
        # 3 メインメニュー画面: 「即時実行」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)
        
        # 4 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        searchbtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchbtn).perform()
        time.sleep(1)
        
        # 5 スケジュール個別追加画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加画面_5" , True)
        
        # 6 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：内定通知書_一覧出力処理
        self.form_input_by_id(idstr="tab01_ZEAF000400_SelKensakuGyomuNM_select", text="子ども子育て支援")
        self.form_input_by_id(idstr="tab01_ZEAF000400_SelKensakuSubSystemNM_select", text="入所")
        self.form_input_by_id(idstr="tab01_ZEAF000400_SelKensakuShoriNM_select", text="内定通知書_一覧出力処理")
        
        # 7 スケジュール個別追加画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 8 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")
        
        # 9 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_9" , True)
        
        # 10 実行指示画面: 以下を入力対象年月：
        # Assert: パラメータ化
        self.form_input_by_id(idstr="tab01_ZEAF002200_Paratest61_textboxInput", value=self.test_data.get("case_qap001_taisyou_nenngetu_1"))
        self.form_input_by_id(idstr="tab01_ZEAF002200_Paratest12_select", text="有")

        # 11 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 12 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理画面_12" , True)
        
        # 13 実行結果管理画面: 「No.1」ボタン押下
        self.click_button_by_label("1")

        # 14 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_14" , True)

        # 15 結果確認画面: 「納品物確認」ボタン押下
        # 16 納品物管理画面: 表示
        # 17 納品物管理画面: 「ダウンロード」ボタンを押下
        # 18 ファイルダウンロード画面: 表示
        # 19 ファイルダウンロード画面: 「No.1」ボタン押下
        # 20 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 21 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_21")
        # 22 PDF: ×ボタン押下で閉じる
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：選考処理名：入所処理名：内定通知書_一覧出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2","子ども子育て支援",  "入所", "内定通知書_一覧出力処理")
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP100700_保育所入所内定者一覧.pdf", "ファイルダウンロード画面_18")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 23 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002400_navi']/li[1]/a").click()
        time.sleep(1)
        
        # 24 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        
        # 25 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_25" , True)
        
        # 26 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 27 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理画面_27" , True)
        
        # 28 実行結果管理画面: 「No.1」ボタン押下
        self.click_button_by_label("1")

        # 29 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_29" , True)

        # 30 結果確認画面: 「納品物確認」ボタン押下
        # 31 納品物管理画面: 表示
        # 32 納品物管理画面: 「ダウンロード」ボタンを押下
        # 33 ファイルダウンロード画面: 表示
        # 34 ファイルダウンロード画面: 「No.1」ボタン押下
        # 35 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 36 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # self.screen_shot("PDF_36")
        # 37 PDF: ×ボタン押下で閉じる
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：入所処理名：内定通知書_一覧出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2","子ども子育て支援", "入所", "内定通知書_一覧出力処理")
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP109600_施設等利用調整結果通知書（内定）.pdf", "ファイルダウンロード画面_33")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # タブ削除
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        
        # 38 メインメニュー画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー画面_38" , True)
        
        # 39 メインメニュー画面: 「バッチ管理」ボタン押下
        # self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        # time.sleep(1)
        
        # 40 メインメニュー画面: 「即時実行」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)
        
        # 41 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        searchbtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchbtn).perform()
        time.sleep(1)
        
        # 42 スケジュール個別追加画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加画面_42" , True)
        
        # 43 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：入所不承諾通知書 出力
        self.form_input_by_id(idstr="tab01_ZEAF000400_SelKensakuGyomuNM_select", text="子ども子育て支援")
        self.form_input_by_id(idstr="tab01_ZEAF000400_SelKensakuSubSystemNM_select", text="入所")
        self.form_input_by_id(idstr="tab01_ZEAF000400_SelKensakuShoriNM_select", text="入所不承諾通知書 出力")
        
        # 44 スケジュール個別追加画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 45 スケジュール個別追加画面: 「No.1」ボタン押下
        self.click_button_by_label("1")

        # 46 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_46" , True)
        
        # 47 実行指示画面: 以下を入力対象年月：
        # Assert: パラメータ化
        self.form_input_by_id(idstr="tab01_ZEAF002200_Paratest61_textboxInput", value=self.test_data.get("case_qap001_taisyou_nenngetu_2"))
        self.form_input_by_id(idstr="tab01_ZEAF002200_Paratest12_select", text="有")

        # 48 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 49 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理画面_49" , True)
        
        # 50 実行結果管理画面: 「No.1」ボタン押下
        self.click_button_by_label("1")

        # 51 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_51" , True)

        # 52 結果確認画面: 「納品物確認」ボタン押下
        # 53 納品物管理画面: 表示
        # 54 納品物管理画面: 「ダウンロード」ボタンを押下
        # 55 ファイルダウンロード画面: 表示
        # 56 ファイルダウンロード画面: 「No.1」ボタン押下
        # 57 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 58 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # 59 PDF: ×ボタン押下で閉じる
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：入所処理名：入所不承諾通知書 出力
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2","子ども子育て支援", "入所", "入所不承諾通知書 出力")
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP101100_入所不承諾者一覧.pdf", "ファイルダウンロード画面_55")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        # 60 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002400_navi']/li[1]/a").click()
        time.sleep(1)
        
        # 61 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        
        # 62 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_62" , True)
        
        # 63 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 64 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理画面_64" , True)
        
        # 65 実行結果管理画面: 「No.1」ボタン押下
        self.click_button_by_label("1")

        # 66 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_66" , True)

        # 67 結果確認画面: 「納品物確認」ボタン押下
        # 68 納品物管理画面: 表示
        # 69 納品物管理画面: 「ダウンロード」ボタンを押下
        # 70 ファイルダウンロード画面: 表示
        # 71 ファイルダウンロード画面: 「No.1」ボタン押下
        # 72 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 73 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # 74 ×ボタン押下で閉じる
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：入所処理名：入所不承諾通知書 出力
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2","子ども子育て支援", "入所", "入所不承諾通知書 出力")
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP101000_入所不承諾通知書.pdf", "ファイルダウンロード画面_70")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()

        # タブ削除
        self.find_element_by_xpath("//*[@id='01']/span[2]").click()
        
        # 75 メインメニュー画面: 表示
        self.save_screenshot_migrate(driver, "メインメニュー画面_75" , True)
        
        # 76 メインメニュー画面: 「バッチ管理」ボタン押下
        # self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/span").click()
        # time.sleep(1)
        
        # 77 メインメニュー画面: 「即時実行」ボタン押下
        self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/span").click()
        time.sleep(1)
        
        # 78 メインメニュー画面: 「スケジュール個別追加」ボタン押下
        searchbtn = self.find_element_by_xpath("//ul[@id='rootmenu_0']/li[contains(span,'バッチ管理')]/ul/li[contains(span,'即時実行')]/ul/li[contains(span,'スケジュール個別追加')]/span")
        self.actionChains.double_click(searchbtn).perform()
        time.sleep(1)
        
        # 79 スケジュール個別追加画面: 表示
        self.save_screenshot_migrate(driver, "スケジュール個別追加画面_79" , True)
        
        # 80 スケジュール個別追加画面: 業務名：子ども子育て支援サブシステム名：入所処理名：入所保留通知書出力処理
        self.form_input_by_id(idstr="tab01_ZEAF000400_SelKensakuGyomuNM_select", text="子ども子育て支援")
        self.form_input_by_id(idstr="tab01_ZEAF000400_SelKensakuSubSystemNM_select", text="入所")
        self.form_input_by_id(idstr="tab01_ZEAF000400_SelKensakuShoriNM_select", text="入所保留通知書出力処理")
        
        # 81 スケジュール個別追加画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        
        # 82 スケジュール個別追加画面: 「No.X」ボタン押下
        self.click_button_by_label("1")
        
        # 83 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_83" , True)
        
        # 84 実行指示画面: 以下を入力対象年月：
        # Assert: パラメータ化
        self.form_input_by_id(idstr="tab01_ZEAF002200_Paratest61_textboxInput", value=self.test_data.get("case_qap001_taisyou_nenngetu_3"))
        self.form_input_by_id(idstr="tab01_ZEAF002200_Paratest270_textboxInput", value=self.test_data.get("case_qap001_shinsei_date_start"))
        self.form_input_by_id(idstr="tab01_ZEAF002200_Paratest12_select", text="有")

        # 85 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 86 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理画面_86" , True)
        
        # 87 実行結果管理画面: 「No.1」ボタン押下
        self.click_button_by_label("1")

        # 88 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "結果確認画面_88" , True)

        # 89 結果確認画面: 「納品物確認」ボタン押下
        # 90 納品物管理画面: 表示
        # 91 納品物管理画面: 「ダウンロード」ボタンを押下
        # 92 ファイルダウンロード画面: 表示
        # 93 ファイルダウンロード画面: 「No.1」ボタン押下
        # 94 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 95 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # 96 ×ボタン押下で閉じる
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：入所処理名：入所保留通知書出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2","子ども子育て支援", "入所", "入所保留通知書出力処理")
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP112100_入所保留対象者一覧.pdf", "ファイルダウンロード画面_92")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
        
        # 97 ファイルダウンロード画面: パンくずリスト「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//*[@id='tab01_ZEAF002400_navi']/li[1]/a").click()
        time.sleep(1)
        
        # 98 スケジュール個別追加画面: 「No.2」ボタン押下
        self.click_button_by_label("2")
        
        # 99 実行指示画面: 表示
        self.save_screenshot_migrate(driver, "実行指示画面_99" , True)
        
        # 100 実行指示画面: 「実行」ボタン押下
        self.click_button_by_label("実行")
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        # 101 実行結果管理画面: 表示
        # Assert: 実行結果の状態が「正常終了」であることを確認する。
        self.wait_job_finished_kodomo(batch_click_count, batch_wait_seconds)
        self.save_screenshot_migrate(driver, "実行結果管理画面_101" , True)
        
        # 102 実行結果管理画面: 「No.1」ボタン押下
        self.click_button_by_label("1")

        # 103 結果確認画面: 表示
        self.save_screenshot_migrate(driver, "実行結果管理画面_103" , True)

        # 104 結果確認画面: 「納品物確認」ボタン押下
        # 105 納品物管理画面: 表示
        # 106 納品物管理画面: 「ダウンロード」ボタンを押下
        # 107 ファイルダウンロード画面: 表示
        # 108 ファイルダウンロード画面: 「No.1」ボタン押下
        # 109 ファイルダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        # 110 PDF: 表示
        # Assert: 納品物の内容を確認する。
        # 111 ×ボタン押下で閉じる
        self.goto_menu(["バッチ管理","結果管理","納品物確認"])

        # 納品物確認画面: 業務名：子ども子育て支援サブシステム名：入所処理名：入所処理名：入所保留通知書出力処理
        # 納品物確認画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku_confirm("2","子ども子育て支援", "入所", "入所保留通知書出力処理")
        
        # 納品物確認画面: 「ダウンロード」ボタン押下
        self.pdf_download("QAPP112200_入所保留通知書.pdf", "ファイルダウンロード画面_107")
        self.click_button_by_label("閉じる")

        # 納品物確認タブを閉じる
        self.find_element_by_xpath("//ul[@id='work_tab']/li[2]/span[2]").click()
        
