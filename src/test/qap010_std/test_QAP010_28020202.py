from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28020202(KodomoSiteTestCaseBase):
    """TestQAP010_28020202"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28020202(self):
        tab_index = 0
        case_data_068_QAZF100001 = self.test_data["068_QAZF100001"]
        case_data_070_QAZF100002 = self.test_data["070_QAZF100002"]
        case_data_071_QAPF100300 = self.test_data["071_QAPF100300"]
        case_data_075_QAPF105500 = self.test_data["075_QAPF105500"]
        case_data_081_ZEAF000400 = self.test_data["081_ZEAF000400"]
        case_data_083_ZEAF000400 = self.test_data["083_ZEAF000400"]
        case_data_084_ZEAF002200 = self.test_data["084_ZEAF002200"]
        case_data_095_ZEAF000400 = self.test_data["095_ZEAF000400"]
        case_data_096_ZEAF000400 = self.test_data["096_ZEAF000400"]
        case_data_097_ZEAF002200 = self.test_data["097_ZEAF002200"]
        case_data_107_ZEAF000400 = self.test_data["107_ZEAF000400"]
        case_data_108_ZEAF002200 = self.test_data["108_ZEAF002200"]
        case_data_118_ZEAF000400 = self.test_data["118_ZEAF000400"]
        case_data_119_ZEAF002200 = self.test_data["119_ZEAF002200"]
        case_data_129_ZEAF000400 = self.test_data["129_ZEAF000400"]
        case_data_130_ZEAF002200 = self.test_data["130_ZEAF002200"]
        case_data_140_ZEAF000400 = self.test_data["140_ZEAF000400"]
        case_data_141_ZEAF002200 = self.test_data["141_ZEAF002200"]
        case_data_151_ZEAF000400 = self.test_data["151_ZEAF000400"]
        case_data_152_ZEAF002200 = self.test_data["152_ZEAF002200"]
        case_data_162_ZEAF000400 = self.test_data["162_ZEAF000400"]
        case_data_163_ZEAF002200 = self.test_data["163_ZEAF002200"]
        case_data_173_ZEAF000400 = self.test_data["173_ZEAF000400"]
        case_data_174_ZEAF002200 = self.test_data["174_ZEAF002200"]
        case_data_184_ZEAF000400 = self.test_data["184_ZEAF000400"]
        case_data_186_ZEAF000400 = self.test_data["186_ZEAF000400"]
        case_data_187_ZEAF002200 = self.test_data["187_ZEAF002200"]
        case_data_197_ZEAF000400 = self.test_data["197_ZEAF000400"]
        case_data_199_ZEAF000400 = self.test_data["199_ZEAF000400"]
        case_data_200_ZEAF002200 = self.test_data["200_ZEAF002200"]
        case_data_212_QAPF105100 = self.test_data["212_QAPF105100"]
        case_data_215_QAPF105100 = self.test_data["215_QAPF105100"]

        # 64 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 65 メインメニュー 画面: 「子ども子育て支援」ボタン押下
        # 66 メインメニュー 画面: 「世帯情報」ボタン押下
        # 67 メインメニュー 画面: 「検索」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="世帯情報", menu_level_3="検索",
                                 is_new_tab=True)
        tab_index += 1

        # 68 検索条件入力 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
                              value=case_data_068_QAZF100001.get("kana_shimei_text", ""))
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "QAZF100001_selAimaiKana_select",
                              text=case_data_068_QAZF100001.get("kana_shimei_select", ""))
        # 漢字氏名
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
                              value=case_data_068_QAZF100001.get("kanji_shimei_text", ""))
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
                              text=case_data_068_QAZF100001.get("kanji_shimei_select", ""))
        # 生年月日
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
                              value=case_data_068_QAZF100001.get("seinengappi_1_ymd", ""))
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "tab0_QAZF100001_selAimaiBirth1_select",
                              text=case_data_068_QAZF100001.get("seinengappi_select", ""))
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
                              value=case_data_068_QAZF100001.get("seinengappi_2_ymd", ""))
        # 性別
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
                              text=case_data_068_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
                              value=case_data_068_QAZF100001.get("yuubin_bangou_1", ""))
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
                              value=case_data_068_QAZF100001.get("yuubin_bangou_2", ""))
        # 方書
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
                              value=case_data_068_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
                              value=case_data_068_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
                              value=case_data_068_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
                              text=case_data_068_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
                              value=case_data_068_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
                              value=case_data_068_QAZF100001.get("denwa_bangou_ichi", ""))

        self.screen_shot("検索条件入力 画面_68")

        # 69 検索条件入力 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 70 世帯履歴 画面: 対象の「№」ボタン押下
        # 世帯履歴 画面: 支給認定情報を登録する世帯台帳履歴の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_070_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code=atena_code, tab_index=tab_index, col_number=1)

        self.click_button_by_label("1")
        self.screen_shot("世帯台帳 画面_70")

        # 71 世帯台帳 画面: 対象児童の「No.」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_071_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)

        # 72 世帯台帳 画面:「入所管理」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF103500", label="入所管理")
        self.screen_shot("児童台帳 画面_72")

        # 73 世帯台帳 画面:「選考結果更新」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103500_btnSenkoKekkaKoshin_button")
        self.screen_shot("入所選考結果更新 画面_73")

        # 74 入所選考結果更新 画面:「追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF105500_btnAddChg_button")

        # 75 入所選考結果更新 画面: 選考結果情報を入力します
        # 選考年月日
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_txtSenkoKekkaInputSenkoYMD_textboxInput",
                              value=case_data_075_QAPF105500.get("senkou_nengetsubi_ymd", ""))
        # 決定年月日
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_txtSenkoKekkaInputKetteiYMD_textboxInput",
                              value=case_data_075_QAPF105500.get("kettei_nengetsubi_ymd", ""))
        # 入所予定年月日
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_txtSenkoKekkaInputNyushoYMD_textboxInput",
                              value=case_data_075_QAPF105500.get("nyuusho_yotei_nengetsubi_ymd", ""))

        # 選考結果
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKekkaInputSenkoKekka_select",
                              text=case_data_075_QAPF105500.get("senkou_kekka_cbx", ""))
        # 選考理由
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKekkaInputSenkoriyu_select",
                              text=case_data_075_QAPF105500.get("senkou_riyuu_cbx", ""))
        # 決定施設
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKekkaInputKetteiShisetsu_select",
                              text=case_data_075_QAPF105500.get("kettei_shisetsu_cbx", ""))

        # 76 入所選考結果更新 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF105500_regbtn_button")

        # 77 入所選考結果更新 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF105500_msg_span",
                                 msg="登録しました。")
        self.screen_shot("入所選考結果更新 画面_77")

        # 78 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 79 メインメニュー 画面:「即時実行」クリック
        # 80 メインメニュー 画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 81 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：選考 <処理名>：入所自動選考データ作成処理
        # 82 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_081_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_081_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_081_ZEAF000400.get("shori_mei_cmb", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_82")

        # 83 スケジュール個別追加 画面:「入所自動選考データ作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_083_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 84 実行指示 画面: パラメータを入力
        params = [
            {"title": "処理区分", "type": "select", "value": case_data_084_ZEAF002200.get("shori_kubun_cbx", "")},
            {"title": "申請有効期限日", "type": "text",
             "value": case_data_084_ZEAF002200.get("shinsei_yuukou_kigenbi_ymd", "")},
            {"title": "選考データ名", "type": "text",
             "value": case_data_084_ZEAF002200.get("senkou_deeta_mei", "")},
            {"title": "選考枠", "type": "select", "value": case_data_084_ZEAF002200.get("senkou_waku_cbx", "")},
            {"title": "入所申込日開始", "type": "text",
             "value": case_data_084_ZEAF002200.get("nyuusho_moushikomi_bi_kaishi_ymd", "")},
            {"title": "入所申込日終了", "type": "text",
             "value": case_data_084_ZEAF002200.get("nyuusho_moushikomi_bi_shuuryou_ymd", "")},
            {"title": "入所予定日", "type": "text",
             "value": case_data_084_ZEAF002200.get("nyuusho_yoteibi_ymd", "")},
            {"title": "基準日", "type": "text", "value": case_data_084_ZEAF002200.get("kijun_bi_ymd", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_84")

        # 85 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 86 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_86")

        # 87 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 88 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_88")

        # 89 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 90 ファイルダウンロード 画面:「No1」ボタン押下
        # 91 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 92 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 93 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_93")

        # 94 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：選考 <処理名>：入所自動選考処理
        # 95 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_095_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_095_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_095_ZEAF000400.get("shori_mei_cmb", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_95")

        # 96 スケジュール個別追加 画面:「選考開始処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_096_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)
        # 97 実行指示 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_097_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_97")

        # 98 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 99 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_99")

        # 100 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 101 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_101")

        # 102 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 103 ファイルダウンロード 画面:「No1」ボタン押下
        # 104 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 105 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 106 実行管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_106")

        # 107 スケジュール個別追加 画面:「事前選考処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_107_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)
        # 108 実行指示 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_108_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
            {"title": "転園人数調整", "type": "select",
             "value": case_data_108_ZEAF002200.get("tenen_ninzuu_chousei_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_108")

        # 109 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 110 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_110")

        # 111 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 112 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_112")

        # 113 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 114 ファイルダウンロード 画面:「No1」ボタン押下
        # 115 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 116 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 117 パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_117")

        # 118 スケジュール個別追加 画面:「世帯選考処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_118_ZEAF000400.get("batch_job_004", ""),
                                             tab_index=tab_index)
        # 119 実行指示 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_119_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
            {"title": "転園人数調整", "type": "select",
             "value": case_data_119_ZEAF002200.get("tenen_ninzuu_chousei_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_119")

        # 120 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 121 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_121")

        # 122 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 123 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_123")

        # 124 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 125 ファイルダウンロード 画面:「No1」ボタン押下
        # 126 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 127 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 128 実行管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_128")

        # 129 スケジュール個別追加 画面:「最終選考処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_129_ZEAF000400.get("batch_job_005", ""),
                                             tab_index=tab_index)
        # 130 実行指示 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_130_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
            {"title": "転園人数調整", "type": "select",
             "value": case_data_130_ZEAF002200.get("tenen_ninzuu_chousei_cbx", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_130")

        # 131 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 132 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=200, time_span_sec=20)
        self.screen_shot("実行管理 画面_131")

        # 133 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 134 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_134")

        # 135 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 136 ファイルダウンロード 画面:「No1」ボタン押下
        # 137 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 138 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 139 実行管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_139")

        # 140 スケジュール個別追加 画面:「NG対象指数更新処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_140_ZEAF000400.get("batch_job_006", ""),
                                             tab_index=tab_index)
        # 141 実行指示 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_141_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_141")

        # 142 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 143 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=200, time_span_sec=20)
        self.screen_shot("実行管理_画面_143")

        # 144 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 145 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認_画面_145")

        # 146 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 147 ファイルダウンロード 画面:「No1」ボタン押下
        # 148 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 149 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 150 パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_150")

        # 151 スケジュール個別追加 画面:「同点者確認処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_151_ZEAF000400.get("batch_job_007", ""),
                                             tab_index=tab_index)
        # 152 実行管理 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_152_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_152")

        # 153 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 154 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=200, time_span_sec=20)
        self.screen_shot("実行管理_画面_154")

        # 155 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 156 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認_画面_156")

        # 157 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 158 ファイルダウンロード 画面:「No1」ボタン押下
        # 159 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 160 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 161 パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_161")

        # 162 スケジュール個別追加 画面:「選考終了処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_162_ZEAF000400.get("batch_job_008", ""),
                                             tab_index=tab_index)
        # 163 実行指示 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_163_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_163")

        # 164 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 165 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=200, time_span_sec=20)
        self.screen_shot("実行管理_画面_165")

        # 166 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 167 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認_画面_167")

        # 168 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 169 ファイルダウンロード 画面:「No1」ボタン押下
        # 170 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 171 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 172 実行管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_172")

        # 173 スケジュール個別追加 画面:「自動選考処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_173_ZEAF000400.get("batch_job_009", ""),
                                             tab_index=tab_index)
        # 174 実行指示 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_174_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
            {"title": "転園人数調整", "type": "select",
             "value": case_data_174_ZEAF002200.get("tenen_ninzuu_chousei", "")},
            {"title": "同点発生時選考停止", "type": "select",
             "value": case_data_174_ZEAF002200.get("douten_hasseiji_senkou_teishi_cbx", "")},
            {"title": "選考繰返し上限回数", "type": "text",
             "value": case_data_174_ZEAF002200.get("senkou_kurikaeshi_jougen_kaisuu_cbx", "")},
            {"title": "NG対象者存在時停止", "type": "select",
             "value": case_data_174_ZEAF002200.get("ng_taishousha_sonzai_ji_teishi_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_174")

        # 175 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 176 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=200, time_span_sec=20)
        self.screen_shot("実行管理_画面_176")

        # 177 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 178 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認_画面_178")

        # 179 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 180 ファイルダウンロード 画面:「No1」ボタン押下
        # 181 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 182 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 183 実行管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_183")

        # 184 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：選考 <処理名>：入所自動選考結果出力処理
        # 185 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_184_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_184_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_184_ZEAF000400.get("shori_mei_cmb", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加_画面_185")

        # 186 スケジュール個別追加 画面:「入所選考シミュレーション結果帳票　出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_186_ZEAF000400.get("batch_job_010", ""),
                                             tab_index=tab_index)
        # 187 実行指示 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
            {"title": "選考結果登録・待機（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_touroku_taiki_setai_cbx", "")},
            {"title": "選考結果登録・内定（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_touroku_naitei_setai_cbx", "")},
            {"title": "選考結果・兄弟（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_kyoudai_setai_cbx", "")},
            {"title": "選考結果・全件（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_zenken_shisetsu_cbx", "")},
            {"title": "選考結果・集計（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_shuukei_shisetsu_cbx", "")},
            {"title": "選考結果・上位（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_joui_shisetsu_cbx", "")},
            {"title": "選考結果・上位（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_joui_setai_cbx", "")},
            {"title": "選考結果・全件（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_zenken_setai_cbx", "")},
            {"title": "選考結果・待機（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_taiki_shisetsu_cbx", "")},
            {"title": "選考結果・待機（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_taiki_setai_cbx", "")},
            {"title": "選考結果・第2（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_dai2_shisetsu_cbx", "")},
            {"title": "選考結果・第2（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_dai2_setai_cbx", "")},
            {"title": "選考結果・超過（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_chouka_shisetsu_cbx", "")},
            {"title": "選考結果・転園（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_ten_en_shisetsu_cbx", "")},
            {"title": "選考結果・転園（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_ten_en_setai_cbx", "")},
            {"title": "選考結果・同点（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_douten_shisetsu_cbx", "")},
            {"title": "選考結果・内定（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_naitei_shisetsu_cbx", "")},
            {"title": "選考結果・内定（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_naitei_setai_cbx", "")},
            {"title": "選考結果・変更（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_henkou_shisetsu_cbx", "")},
            {"title": "選考結果・変更（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_henkou_setai_cbx", "")},
            {"title": "選考結果・優先（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_yuusen_shisetsu_cbx", "")},
            {"title": "選考結果・優先（世帯）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_yuusen_setai_cbx", "")},
            {"title": "選考結果登録・内定（施設）", "type": "select",
             "value": case_data_187_ZEAF002200.get("senkou_kekka_touroku_naitei_shisetsu_cbx", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_187")

        # 188 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 189 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_189")

        # 190 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 191 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("結果確認_画面_191")

        # 192 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 193 ファイルダウンロード 画面:「No1」ボタン押下
        # 194 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 195 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 196 納品物管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_196")

        # 197 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：選考 <処理名>：入所選考結果登録処理
        # 198 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_197_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_197_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_197_ZEAF000400.get("shori_mei_cmb", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加_画面_198")

        # 199 スケジュール個別追加 画面:「入所選考結果登録」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_199_ZEAF000400.get("batch_job_011", ""),
                                             tab_index=tab_index)
        # 200 実行指示 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_200_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
            {"title": "内定履歴存在時更新有無", "type": "select",
             "value": case_data_200_ZEAF002200.get("naitei_rireki_sonzai_ji_koushin_umu_cbx", "")},
            {"title": "選考年月日", "type": "text",
             "value": case_data_200_ZEAF002200.get("senkou_nengetsubi_ymd", "")},
            {"title": "決定年月日", "type": "text",
             "value": case_data_200_ZEAF002200.get("kettei_nengetsubi_ymd", "")},
            {"title": "選考理由（承諾）", "type": "select",
             "value": case_data_200_ZEAF002200.get("senkou_riyuu_shoudaku_cbx", "")},
            {"title": "選考理由（不承諾）", "type": "select",
             "value": case_data_200_ZEAF002200.get("senkou_riyuu_fushoudaku_cbx", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_200")

        # 201 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 202 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_202")

        # 203 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 204 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認_画面_204")

        # 205 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 206 ファイルダウンロード 画面:「No1」ボタン押下
        # 207 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 208 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 209 メインメニュー 画面: 「子ども子育て支援」ボタン押下
        # 210 メインメニュー 画面: 「利用調整」ボタン押下
        # 211 メインメニュー 画面: 「入所判定登録」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="利用調整", menu_level_3="入所判定登録",
                                 is_new_tab=False)
        tab_index += 1

        # 212 入所判定登録 画面: 検索条件を入力
        # 検索条件
        # 対象年度
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_selKensakuJokenTaishoNendo_select",
                              text=case_data_212_QAPF105100.get("taishou_nendo_cbx", ""))
        # 施設コード
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_txtKensakuJokenShisetsuCD_textboxInput",
                              value=case_data_212_QAPF105100.get("shisetsu_code", ""))
        # 選考年月日
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_selWrGyoseikuKensakuJokenShokankuL_select",
                              text=case_data_212_QAPF105100.get("senkou_nengappi", ""))
        # 所管区
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_txtKensakuJokenShisetsuCD_textboxInput",
                              value=case_data_212_QAPF105100.get("shokanku_cbx", ""))
        # クラス checkbox
        # 0歳
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_chkKensakuJokenClassAgechk0",
                              value=case_data_212_QAPF105100.get("zero_sai_chk", ""))
        # 1歳
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_chkKensakuJokenClassAgechk1",
                              value=case_data_212_QAPF105100.get("ichi_sai_chk", ""))
        # 2歳
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_chkKensakuJokenClassAgechk2",
                              value=case_data_212_QAPF105100.get("ni_sai_chk", ""))
        # 3歳
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_chkKensakuJokenClassAgechk3",
                              value=case_data_212_QAPF105100.get("san_sai_chk", ""))
        # 4歳
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_chkKensakuJokenClassAgechk4",
                              value=case_data_212_QAPF105100.get("yon_sai_chk", ""))
        # 5歳
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_chkKensakuJokenClassAgechk5",
                              value=case_data_212_QAPF105100.get("go_sai_chk", ""))
        # 未処理分のみ
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_chkKensakuJokenMishoriMinuteNomiSentakuchk0",
                              value=case_data_212_QAPF105100.get("mishori_bun_nomichi_chk", ""))
        # 転園希望を含む
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_chkKensakuJokenTenenKiboHukumuSentakuchk0",
                              value=case_data_212_QAPF105100.get("tenen_kibou_wo_fukumu_chk", ""))
        self.screen_shot("入所判定登録_画面_212")

        # 213 入所判定登録 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZZZ000000_btnKensakuJokenKensaku_button")
        self.screen_shot("入所判定登録_画面_213")

        # 214 入所判定登録 画面:「修正」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF105100_btnEditChg_button")

        # 215 入所判定登録 画面: 判定結果の内容を入力します
        # 選考結果入力
        # 対象処理
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKekkaInputTaishoShori_select",
                              text=case_data_215_QAPF105100.get("taishou_shori_cbx", ""))
        # 決定年月日
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_txtSenkoKekkaInputKetteiYMD_textboxInput",
                              value=case_data_215_QAPF105100.get("kettei_nengappi_ymd", ""))
        # 入所年月日
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_txtSenkoKekkaInputNyushoYMD_textboxInput",
                              value=case_data_215_QAPF105100.get("nyuusho_nengappi_ymd", ""))
        # 理由
        self.form_input_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_selSenkoKekkaInputRiyu_select",
                              text=case_data_215_QAPF105100.get("riyuu_cbx", ""))
        self.screen_shot("入所判定登録_画面_215")

        # 216 入所判定登録 画面: 検索結果の対象一覧から更新する対象を選択

        # 217 入所判定登録 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF105100_regbtn_button")

        # 218 入所判定登録 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF103700_msg_span",
                                 msg="更新しました。")
        self.screen_shot("世帯員詳細情報管理_画面_218")
