from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070102(KodomoSiteTestCaseBase):
    """TestQAP010_28070102"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070102(self):
        tab_index = 0
        case_data_027_ZEAF000400 = self.test_data["027_ZEAF000400"]
        case_data_029_ZEAF000400 = self.test_data["029_ZEAF000400"]
        case_data_030_ZEAF002200 = self.test_data["030_ZEAF002200"]
        case_data_035_ZEAF002700 = self.test_data["035_ZEAF002700"]
        case_data_039_ZEAF002700 = self.test_data["039_ZEAF002700"]
        case_data_044_ZEAF000400 = self.test_data["044_ZEAF000400"]
        case_data_045_ZEAF002200 = self.test_data["045_ZEAF002200"]
        case_data_050_ZEAF002700 = self.test_data["050_ZEAF002700"]
        case_data_054_ZEAF002700 = self.test_data["054_ZEAF002700"]
        case_data_059_ZEAF000400 = self.test_data["059_ZEAF000400"]
        case_data_060_ZEAF002200 = self.test_data["060_ZEAF002200"]
        case_data_065_ZEAF002700 = self.test_data["065_ZEAF002700"]
        case_data_069_ZEAF002700 = self.test_data["069_ZEAF002700"]

        # 23 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 24 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 25 メインメニュー 画面: 「即時実行」クリック
        # 26 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=True)
        tab_index += 1

        # 27 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：賦課 <処理名>：月次賦課処理
        # 選択"
        # 28 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_027_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_027_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_027_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_28")

        # 29 スケジュール個別追加 画面: 「調定者一覧表作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_029_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 30 実行指示 画面: パラメータを入力
        params = [
            {"title": "データ種別", "type": "select", "value": case_data_030_ZEAF002200.get("deeta_shubetsu_cbx", "")},
            {"title": "科目", "type": "select", "value": case_data_030_ZEAF002200.get("kamoku_cbx", "")},
            {"title": "所管区", "type": "select", "value": case_data_030_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select", "value": case_data_030_ZEAF002200.get("shisho_cbx", "")},
            {"title": "収納方法", "type": "select", "value": case_data_030_ZEAF002200.get("shuunou_houhou_cbx", "")},
            {"title": "出力対象", "type": "select",
             "value": case_data_030_ZEAF002200.get("shutsuryoku_taishou_cbx", "")},
            {"title": "対象年月開始", "type": "text",
             "value": case_data_030_ZEAF002200.get("taishou_nengetsu_kaishi_ymd", "")},
            {"title": "対象年月終了", "type": "text",
             "value": case_data_030_ZEAF002200.get("taishou_nengetsu_shuuryou_ymd", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_30")

        # 31 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 32 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_32")

        # 33 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 34 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_34")

        # 35 納品物管理 画面: 納品物「QAPP109700_施設別調定者一覧表.pdf」の「ダウンロード」ボタン押下
        # 36 ファイルダウンロード 画面: 「No1」ボタン押下
        # 37 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 38 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime,
                                   tab_index=tab_index, report_name=case_data_035_ZEAF002700.get("report_name", ""))

        # 39 納品物管理 画面: 納品物「施設別調定者一覧表.csv」の「ダウンロード」ボタン押下
        # 40 ファイルダウンロード 画面: 「No1」ボタン押下
        # 41 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 42 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime,
                                   tab_index=tab_index, report_name=case_data_039_ZEAF002700.get("report_name", ""))

        # 43 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_43")

        # 44 スケジュール個別追加 画面: 「調定額集計表（個票）作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_044_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 45 実行指示 画面: パラメータを入力
        params = [
            {"title": "データ種別", "type": "select", "value": case_data_045_ZEAF002200.get("deeta_shubetsu_cbx", "")},
            {"title": "科目", "type": "select", "value": case_data_045_ZEAF002200.get("kamoku_cbx", "")},
            {"title": "所管区", "type": "select", "value": case_data_045_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select", "value": case_data_045_ZEAF002200.get("shisho_cbx", "")},
            {"title": "施設種類", "type": "select", "value": case_data_045_ZEAF002200.get("shisetsu_shurui_cbx", "")},
            {"title": "出力対象", "type": "select",
             "value": case_data_045_ZEAF002200.get("shutsuryoku_taishou_cbx", "")},
            {"title": "対象年度", "type": "text",
             "value": case_data_045_ZEAF002200.get("taishou_nendo_ymd", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_45")

        # 46 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 47 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_47")

        # 48 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 49 実行管理 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_49")

        # 50 納品物管理 画面: 納品物「QAPP109800_調定額集計表（個票）.pdf」の「ダウンロード」ボタン押下
        # 51 ファイルダウンロード 画面: 「No1」ボタン押下
        # 52 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 53 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime,
                                   tab_index=tab_index, report_name=case_data_050_ZEAF002700.get("report_name", ""))

        # 54 納品物管理 画面: 納品物「調定額集計表（個票）.csv」の「ダウンロード」ボタン押下
        # 55 ファイルダウンロード 画面: 「No2」ボタン押下
        # 56 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 57 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_054_ZEAF002700.get("report_name", ""))

        # 58 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_58")

        # 59 スケジュール個別追加 画面: 「調定額集計表（総括）」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_059_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)

        # 60 実行指示 画面: パラメータを入力
        params = [
            {"title": "データ種別", "type": "select", "value": case_data_060_ZEAF002200.get("deeta_shubetsu_cbx", "")},
            {"title": "科目", "type": "select", "value": case_data_060_ZEAF002200.get("kamoku_cbx", "")},
            {"title": "所管区", "type": "select", "value": case_data_060_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select", "value": case_data_060_ZEAF002200.get("shisho_cbx", "")},
            {"title": "収納方法", "type": "select", "value": case_data_060_ZEAF002200.get("shisetsu_shurui_cbx", "")},
            {"title": "出力対象", "type": "select",
             "value": case_data_060_ZEAF002200.get("shutsuryoku_taishou_cbx", "")},
            {"title": "対象年度", "type": "text",
             "value": case_data_060_ZEAF002200.get("taishou_nendo_ymd", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_60")

        # 61 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 62 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_62")

        # 63 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 64 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_64")

        # 65 納品物管理 画面: 納品物「QAPP1D0707_調定額集計表（総括）.pdf」の「ダウンロード」ボタン押下
        # 66 ファイルダウンロード 画面: 「No1」ボタン押下
        # 67 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 68 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime,
                                   tab_index=tab_index, report_name=case_data_065_ZEAF002700.get("report_name", ""))

        # 69 納品物管理 画面: 納品物「調定額集計表（総括）.csv」の「ダウンロード」ボタン押下
        # 70 ファイルダウンロード 画面: 「No2」ボタン押下
        # 71 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 72 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_069_ZEAF002700.get("report_name", ""))
