from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070202(KodomoSiteTestCaseBase):
    """TestQAP010_28070202"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070202(self):
        tab_index = 0
        case_data_030_ZEAF000400 = self.test_data["030_ZEAF000400"]
        case_data_032_ZEAF000400 = self.test_data["032_ZEAF000400"]
        case_data_033_ZEAF002200 = self.test_data["033_ZEAF002200"]
        case_data_038_ZEAF002700 = self.test_data["038_ZEAF002700"]
        case_data_042_ZEAF002700 = self.test_data["042_ZEAF002700"]
        case_data_047_ZEAF000400 = self.test_data["047_ZEAF000400"]

        # 26 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 27 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 28 メインメニュー 画面: 「即時実行」クリック 
        # 29 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=True)
        tab_index += 1

        # 30 スケジュール個別追加 画面: <業務名>：収納・滞納 <サブシステム名>：随時 <処理名>：口座振替開始通知作成処理
        # 31 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_030_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_030_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_030_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)

        # 32 スケジュール個別追加 画面: 「開始通知作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_032_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 33 実行指示 画面: パラメータを入力
        params = [
            {"title": "科目", "type": "checkbox", "value": case_data_033_ZEAF002200.get("kamoku_chk").values()},
            {"title": "口座開始年月日（開始）", "type": "text",
             "value": case_data_033_ZEAF002200.get("kouza_kaishi_nengetsubi_kaishi_ymd")},

            {"title": "口座開始年月日（終了）", "type": "text",
             "value": case_data_033_ZEAF002200.get("kouza_kaishi_nengetsubi_shuuryou_ymd")},
            {"title": "納期限", "type": "text",
             "value": case_data_033_ZEAF002200.get("noukigen_ymd")},
            {"title": "発送日", "type": "text",
             "value": case_data_033_ZEAF002200.get("hassoubi_ymd")},
            {"title": "期別開始", "type": "text",
             "value": case_data_033_ZEAF002200.get("kibetsu_kaishi")},
            {"title": "期別終了", "type": "text",
             "value": case_data_033_ZEAF002200.get("kibetsu_shuuryou")},
            {"title": "条文（１行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("joubun_1gyoume")},
            {"title": "条文（２行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("joubun_2gyoume")},
            {"title": "条文（３行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("joubun_3gyoume")},
            {"title": "条文（４行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("joubun_4gyoume")},
            {"title": "文言（１行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_1gyoume")},
            {"title": "文言（２行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_2gyoume")},
            {"title": "文言（３行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_3gyoume")},
            {"title": "文言（４行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_4gyoume")},
            {"title": "文言（５行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_5gyoume")},
            {"title": "文言（６行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_6gyoume")},
            {"title": "文言（７行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_7gyoume")},
            {"title": "文言（８行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_8gyoume")},
            {"title": "文言（９行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_9gyoume")},
            {"title": "文言（１０行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_10gyoume")},
            {"title": "文言（１１行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_11gyoume")},
            {"title": "文言（１２行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_12gyoume")},
            {"title": "文言（１３行目）", "type": "text",
             "value": case_data_033_ZEAF002200.get("mongen_13gyoume")},
            {"title": "文書番号", "type": "text",
             "value": case_data_033_ZEAF002200.get("bunsho_bangou")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)

        self.screen_shot("実行指示 画面_33")

        # 34 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 35 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_35")

        # 36 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 37 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_37")

        # 38 納品物管理 画面: 納品物「口座振替開始通知」の「ダウンロード」ボタン押下
        # 39 ファイルダウンロード 画面: 「No1」ボタン押下
        # 40 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 41 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_038_ZEAF002700.get("report_name", ""))

        # 42 納品物管理 画面: 納品物「発布一覧」の「ダウンロード」ボタン押下
        # 43 ファイルダウンロード 画面: 「No2」ボタン押下
        # 44 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 45 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_042_ZEAF002700.get("report_name", ""))

        # 46 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_46")

        # 47 スケジュール個別追加 画面: 「開始通知作成後処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_047_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 48 実行指示 画面: パラメータを入力
        self.screen_shot("実行指示 画面_48")

        # 49 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 50 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_50")
