from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28010202(KodomoSiteTestCaseBase):
    """TestQAP010_28010202"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28010202(self):
        tab_index = 0
        case_data_065_ZEAF000400 = self.test_data["065_ZEAF000400"]
        case_data_067_ZEAF000400 = self.test_data["067_ZEAF000400"]
        case_data_068_ZEAF002200 = self.test_data["068_ZEAF002200"]
        case_data_078_ZEAF000400 = self.test_data["078_ZEAF000400"]
        case_data_082_ZEAF000400 = self.test_data["082_ZEAF000400"]
        case_data_083_ZEAF002200 = self.test_data["083_ZEAF002200"]
        case_data_093_ZEAF000400 = self.test_data["093_ZEAF000400"]
        case_data_093_ZEAF002200 = self.test_data["093_ZEAF002200"]
        case_data_103_ZEAF000400 = self.test_data["103_ZEAF000400"]
        case_data_105_ZEAF000400 = self.test_data["105_ZEAF000400"]
        case_data_106_ZEAF002200 = self.test_data["106_ZEAF002200"]

        # 61 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 62 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 63 メインメニュー 画面:「即時実行」クリック
        # 64 メインメニュー 画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1

        # 65 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：支給認定 <処理名>：３号→２号切替処理
        # 66 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_065_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_065_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_065_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)

        # 67 スケジュール個別追加 画面:「３号→２号切替対象者抽出処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_067_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 68 実行指示 画面: パラメータを入力
        params = [
            {"title": "基準日", "type": "text", "value": case_data_068_ZEAF002200.get("kijunbi", "")},
            {"title": "児童生年月日開始", "type": "text",
             "value": case_data_068_ZEAF002200.get("jidou_seinengappi_kaishi", "")},
            {"title": "児童生年月日終了", "type": "text",
             "value": case_data_068_ZEAF002200.get("jidou_seinengappi_shuuryou", "")},
            {"title": "出産時認定可能日数", "type": "text",
             "value": case_data_068_ZEAF002200.get("shussanji_nintei_kanou_nissuu", "")},
            {"title": "求職時認定可能日数", "type": "text",
             "value": case_data_068_ZEAF002200.get("kyuushokujji_nintei_kanou_nissuu", "")},
            {"title": "支給認定番号配番有無", "type": "select",
             "value": case_data_068_ZEAF002200.get("shikyuu_nintei_bangou_haiban_umu", "")},
            {"title": "並び順", "type": "select", "value": case_data_068_ZEAF002200.get("narabijun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_68")

        # 69 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 70 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_70")

        # 71 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 72 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")

        # 73 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 74 ファイルダウンロード 画面:「No1」ボタン押下
        # 75 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 76 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 77 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_77")

        # 78 スケジュール個別追加 画面:「３号→２号切替対象者更新処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_078_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 79 実行指示 画面:「実行」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002200_executebtn_button")

        # 80 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_80")

        # 81 実行管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")

        # 82 スケジュール個別追加 画面:「支給認定職権変更一覧表出力処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_082_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)

        # 83 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_083_ZEAF002200.get("shokanku", "")},
            {"title": "施設コード", "type": "text", "value": case_data_083_ZEAF002200.get("shisetsu_koudo", "")},
            {"title": "児童宛名コード", "type": "text", "value": case_data_083_ZEAF002200.get("jidou_atena_koudo", "")},
            {"title": "基準日", "type": "text", "value": case_data_083_ZEAF002200.get("kijunbi", "")},
            {"title": "入所状態区分", "type": "select",
             "value": case_data_083_ZEAF002200.get("nyuusho_joutai_kubun", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_083_ZEAF002200.get("yuubin_kunai_tokubetsu_umu", "")},
            {"title": "並び順", "type": "select", "value": case_data_083_ZEAF002200.get("narabijun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_83")

        # 84 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 85 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_85")

        # 86 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 87 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")

        # 88 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 89 ファイルダウンロード 画面:「No1」ボタン押下
        # 90 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 91 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 92 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_92")

        # 93 スケジュール個別追加 画面:「支給認定職権変更通知書出力処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_093_ZEAF000400.get("batch_job_004", ""),
                                             tab_index=tab_index)

        params = [
            {"title": "所管区", "type": "select", "value": case_data_093_ZEAF002200.get("shokanku", "")},
            {"title": "施設コード", "type": "text", "value": case_data_093_ZEAF002200.get("shisetsu_koudo", "")},
            {"title": "児童宛名コード", "type": "text", "value": case_data_093_ZEAF002200.get("jidou_atena_koudo", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_093_ZEAF002200.get("hakkou_nengappi", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)

        # 94 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 95 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_95")

        # 96 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 97 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")

        # 98 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 99 ファイルダウンロード 画面:「No1」ボタン押下
        # 100 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 101 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 102 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_102")

        # 103 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：支給認定 <処理名>：認定区分変更一覧表（３号→２号）出力処理
        # 104 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_103_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_103_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_103_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)

        # 105 スケジュール個別追加 画面:「認定区分変更一覧表（３号→２号）　出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_105_ZEAF000400.get("batch_job_005", ""),
                                             tab_index=tab_index)

        # 106 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_106_ZEAF002200.get("shokanku", "")},
            {"title": "支給決定開始日", "type": "text",
             "value": case_data_106_ZEAF002200.get("shikyuu_kettei_kaishibi", "")},
            {"title": "支給決定終了日", "type": "text",
             "value": case_data_106_ZEAF002200.get("shikyuu_kettei_shuuryoubi", "")},
            {"title": "施設コード", "type": "text", "value": case_data_106_ZEAF002200.get("shisetsu_koudo", "")},
            {"title": "施設種類", "type": "select", "value": case_data_106_ZEAF002200.get("shisetsu_shurui", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_106_ZEAF002200.get("hakkou_nengappi", "")},
            {"title": "並び順", "type": "select", "value": case_data_106_ZEAF002200.get("narabijun", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_106")

        # 107 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 108 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_108")

        # 109 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 110 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_110")

        # 111 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 112 ファイルダウンロード 画面:「No1」ボタン押下
        # 113 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 114 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
