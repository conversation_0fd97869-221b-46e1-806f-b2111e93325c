from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28020203(KodomoSiteTestCaseBase):
    """TestQAP010_28020203"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28020203(self):
        tab_index = 0
        case_data_223_ZEAF000400 = self.test_data["223_ZEAF000400"]
        case_data_225_ZEAF000400 = self.test_data["225_ZEAF000400"]
        case_data_226_ZEAF002200 = self.test_data["226_ZEAF002200"]
        case_data_236_ZEAF000400 = self.test_data["236_ZEAF000400"]
        case_data_237_ZEAF002200 = self.test_data["237_ZEAF002200"]
        case_data_249_ZEAF000400 = self.test_data["249_ZEAF000400"]
        case_data_251_ZEAF000400 = self.test_data["251_ZEAF000400"]
        case_data_252_ZEAF002200 = self.test_data["252_ZEAF002200"]
        case_data_262_ZEAF000400 = self.test_data["262_ZEAF000400"]
        case_data_263_ZEAF002200 = self.test_data["263_ZEAF002200"]
        case_data_275_ZEAF000400 = self.test_data["275_ZEAF000400"]
        case_data_277_ZEAF000400 = self.test_data["277_ZEAF000400"]
        case_data_278_ZEAF002200 = self.test_data["278_ZEAF002200"]
        case_data_288_ZEAF000400 = self.test_data["288_ZEAF000400"]
        case_data_289_ZEAF002200 = self.test_data["289_ZEAF002200"]

        self.do_login_new_tab()

        # 220 メインメニュー 画面: 「バッチ管理」ボタン押下
        # 221 メインメニュー 画面: 「即時実行」ボタン押下
        # 222 メインメニュー 画面: 「スケジュール個別追加」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1

        # 223 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：入所 <処理名>：内定通知書_一覧出力処理
        # 224 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_223_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_223_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_223_ZEAF000400.get("shori_mei_cmb", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_224")

        # 225 スケジュール個別追加 画面:「内定通知書_一覧出力処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_225_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 226 実行指示 画面: パラメータを入力
        params = [
            {"title": "実施区分", "type": "select", "value": case_data_226_ZEAF002200.get("jisshi_kubun_cbx", "")},
            {"title": "所在", "type": "select",
             "value": case_data_226_ZEAF002200.get("shozai_cbx", "")},
            {"title": "対象年月", "type": "text",
             "value": case_data_226_ZEAF002200.get("taishou_nengetsu_ymd", "")},
            {"title": "抽出対象", "type": "select",
             "value": case_data_226_ZEAF002200.get("chuushutsu_taishou_cbx", "")},
            {"title": "入所形態", "type": "select",
             "value": case_data_226_ZEAF002200.get("nyuusho_keitai_cbx", "")},
            {"title": "入所日_開始", "type": "text",
             "value": case_data_226_ZEAF002200.get("nyuusho_bi_kaishi_ymd", "")},
            {"title": "入所日_終了", "type": "text",
             "value": case_data_226_ZEAF002200.get("nyuusho_bi_shuuryou_ymd", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_226_ZEAF002200.get("hakkou_nengetsubi_ymd", "")},
            {"title": "並び順", "type": "select", "value": case_data_226_ZEAF002200.get("narabijun_cbx", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_226_ZEAF002200.get("yuubinku_nai_tokubetsu_umu_cbx", "")},
            {"title": "所管区", "type": "select", "value": case_data_226_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "公私区分", "type": "select", "value": case_data_226_ZEAF002200.get("koushi_kubun_cbx", "")},
            {"title": "再発行区分", "type": "select", "value": case_data_226_ZEAF002200.get("saihakkou_kubun_cbx", "")},
            {"title": "支所", "type": "select", "value": case_data_226_ZEAF002200.get("shisho_cbx", "")},
            {"title": "施設種類", "type": "select", "value": case_data_226_ZEAF002200.get("shisetsu_shurui_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_226")

        # 227 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 228 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_228")

        # 229 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 230 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_230")

        # 231 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 232 ファイルダウンロード 画面:「No1」ボタン押下
        # 233 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 234 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 235 納品物管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_235")

        # 236 スケジュール個別追加 画面:「内定通知書出力処理出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_236_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 237 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select",
             "value": case_data_237_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select",
             "value": case_data_237_ZEAF002200.get("shisho_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_237")

        # 238 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 239 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_239")

        # 240 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 241 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_241")

        # 242 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 243 ファイルダウンロード 画面:「No1」ボタン押下
        # 244 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 245 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 246 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 247 メインメニュー 画面:「即時実行」クリック
        # 248 メインメニュー 画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 249 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：入所 <処理名>：入所不承諾通知書　出力
        # 250 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_249_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_249_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_249_ZEAF000400.get("shori_mei_cmb", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_250")

        # 251 スケジュール個別追加 画面:「入所不承諾者　抽出」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_251_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)
        # 252 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_252_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "公私区分", "type": "select",
             "value": case_data_252_ZEAF002200.get("koushi_kubun_cbx", "")},
            {"title": "再発行区分", "type": "select",
             "value": case_data_252_ZEAF002200.get("saihakkou_kubun_cbx", "")},
            {"title": "支所", "type": "select", "value": case_data_252_ZEAF002200.get("shisho_cbx", "")},
            {"title": "施設コード", "type": "text",
             "value": case_data_252_ZEAF002200.get("shisetsu_code", "")},
            {"title": "実施区分", "type": "select",
             "value": case_data_252_ZEAF002200.get("jisshi_kubun_cbx", "")},
            {"title": "所在", "type": "select",
             "value": case_data_252_ZEAF002200.get("shozai_cbx", "")},
            {"title": "対象年月", "type": "text", "value": case_data_252_ZEAF002200.get("taishou_nengetsu_ymd", "")},
            {"title": "対象年度", "type": "text", "value": case_data_252_ZEAF002200.get("taishou_nendo_ymd", "")},
            {"title": "入所形態", "type": "select", "value": case_data_252_ZEAF002200.get("nyuusho_keitai_cbx", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_252_ZEAF002200.get("hakkou_nengetsubi_ymd", "")},
            {"title": "並び順", "type": "select", "value": case_data_252_ZEAF002200.get("narabi_jun_cbx", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_252_ZEAF002200.get("yuubin_kuuchi_tokubetsu_umu_cbx", "")},
            {"title": "施設種類", "type": "select", "value": case_data_252_ZEAF002200.get("shisetsu_shurui_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_252")

        # 253 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 254 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_254")

        # 255 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 256 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_256")

        # 257 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 258 ファイルダウンロード 画面:「No1」ボタン押下
        # 259 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 260 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 261 納品物管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_261")

        # 262 スケジュール個別追加 画面:「入所不承諾通知書　出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_262_ZEAF000400.get("batch_job_004", ""),
                                             tab_index=tab_index)

        # 263 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_263_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select",
             "value": case_data_263_ZEAF002200.get("shisho_cbx", "")},
            {"title": "施設コード", "type": "text",
             "value": case_data_263_ZEAF002200.get("shisetsu_code", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_263")

        # 264 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 265 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_265")

        # 266 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 267 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_267")

        # 268 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 269 ファイルダウンロード 画面:「No1」ボタン押下
        # 270 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 271 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 272 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 273 メインメニュー 画面:「即時実行」クリック
        # 274 メインメニュー 画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 275 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：入所 <処理名>：入所保留通知書出力処理
        # 276 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_275_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_275_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_275_ZEAF000400.get("shori_mei_cmb", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_275")

        # 277 スケジュール個別追加 画面: スケジュール個別追加 画面:「入所保留通知書抽出」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_277_ZEAF000400.get("batch_job_005", ""),
                                             tab_index=tab_index)

        # 278 パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_278_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select",
             "value": case_data_278_ZEAF002200.get("shisho_cbx", "")},
            {"title": "公私区分", "type": "select",
             "value": case_data_278_ZEAF002200.get("koushi_kubun_cbx", "")},
            {"title": "再発行区分", "type": "select", "value": case_data_278_ZEAF002200.get("saihakkou_kubun_cbx", "")},
            {"title": "施設種類", "type": "select",
             "value": case_data_278_ZEAF002200.get("shisetsu_shurui_cbx", "")},
            {"title": "所在", "type": "select",
             "value": case_data_278_ZEAF002200.get("shozai_cbx", "")},
            {"title": "対象年月", "type": "text", "value": case_data_278_ZEAF002200.get("taishou_nengetsu_cbx", "")},
            {"title": "申請日開始", "type": "text",
             "value": case_data_278_ZEAF002200.get("shinseibi_kaishi_cbx", "")},
            {"title": "申請日終了", "type": "text",
             "value": case_data_278_ZEAF002200.get("shinseibi_shuuryou_cbx", "")},
            {"title": "希望期間開始日開始", "type": "text",
             "value": case_data_278_ZEAF002200.get("kibou_kikan_kaishibi_kaishi_cbx", "")},
            {"title": "希望期間開始日終了", "type": "text",
             "value": case_data_278_ZEAF002200.get("kibou_kikan_kaishibi_shuuryou_cbx", "")},
            {"title": "選考日開始", "type": "text", "value": case_data_278_ZEAF002200.get("senkoubi_kaishi_cbx", "")},
            {"title": "選考日終了", "type": "text",
             "value": case_data_278_ZEAF002200.get("senkoubi_shuuryou_cbx", "")},
            {"title": "発行年月日", "type": "text",
             "value": case_data_278_ZEAF002200.get("hakkou_nengetsubi_cbx", "")},
            {"title": "並び順", "type": "select", "value": case_data_278_ZEAF002200.get("narabi_jun_cbx", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_278_ZEAF002200.get("yuubin_kuuchi_tokubetsu_umu_cbx", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_278")

        # 279 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 280 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_280")

        # 281 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 282 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_282")

        # 283 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 284 ファイルダウンロード 画面:「No1」ボタン押下
        # 285 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 286 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 287 納品物管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")

        # 288 スケジュール個別追加 画面: スケジュール個別追加 画面:「入所保留通知書出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_288_ZEAF000400.get("batch_job_006", ""),
                                             tab_index=tab_index)

        # 289 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_289_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select",
             "value": case_data_289_ZEAF002200.get("shisho_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_289")

        # 290 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 291 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_291")

        # 292 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 293 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認 画面_293")

        # 294 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 295 ファイルダウンロード 画面:「No1」ボタン押下
        # 296 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 297 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
