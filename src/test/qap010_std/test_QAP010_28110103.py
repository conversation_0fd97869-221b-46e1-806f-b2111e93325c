from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28110103(KodomoSiteTestCaseBase):
    """TestQAP010_28110103"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28110101(self):
        case_data_123_ZEAF000400 = self.test_data["123_ZEAF000400"]
        case_data_125_ZEAF000400 = self.test_data["125_ZEAF000400"]
        case_data_126_ZEAF002200 = self.test_data["126_ZEAF002200"]
        case_data_136_ZEAF000400 = self.test_data["136_ZEAF000400"]
        case_data_137_ZEAF002200 = self.test_data["137_ZEAF002200"]
        case_data_149_ZEAF000400 = self.test_data["149_ZEAF000400"]
        case_data_151_ZEAF000400 = self.test_data["151_ZEAF000400"]
        case_data_152_ZEAF002200 = self.test_data["152_ZEAF002200"]
        case_data_162_ZEAF000400 = self.test_data["162_ZEAF000400"]
        case_data_163_ZEAF002200 = self.test_data["163_ZEAF002200"]
        case_data_175_ZEAF000400 = self.test_data["175_ZEAF000400"]
        case_data_177_ZEAF000400 = self.test_data["177_ZEAF000400"]
        case_data_178_ZEAF002200 = self.test_data["178_ZEAF002200"]
        case_data_188_ZEAF000400 = self.test_data["188_ZEAF000400"]
        case_data_189_ZEAF002200 = self.test_data["189_ZEAF002200"]
        case_data_201_ZEAF000400 = self.test_data["201_ZEAF000400"]
        case_data_203_ZEAF000400 = self.test_data["203_ZEAF000400"]
        case_data_204_ZEAF002200 = self.test_data["204_ZEAF002200"]
        case_data_216_ZEAF000400 = self.test_data["216_ZEAF000400"]
        case_data_218_ZEAF000400 = self.test_data["218_ZEAF000400"]
        case_data_219_ZEAF002200 = self.test_data["219_ZEAF002200"]
        tab_index = 0

        # 119 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 120 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 121 メインメニュー 画面: 「即時実行」クリック
        # 122 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=True)
        tab_index += 1

        # 123 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：認可外支払管理 <処理名>：補助金一括算定処理
        # 124 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_123_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_123_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_123_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_124")

        # 125 スケジュール個別追加 画面: 「 補助金一括算定前処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_125_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)
        # 126 実行指示 画面: パラメータを入力
        params = [
            {"title": "利用年月開始", "type": "text",
             "value": case_data_126_ZEAF002200.get("riyou_nengetsu_kaishi_ymd", "")},
            {"title": "利用年月終了", "type": "text",
             "value": case_data_126_ZEAF002200.get("riyou_nengetsu_shuuryou_ymd", "")},
            {"title": "計算対象", "type": "select",
             "value": case_data_126_ZEAF002200.get("keisan_taishou_cmb", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_126")

        # 127 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 128 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_128")

        # 129 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 130 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_130")

        # 131 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 132 ファイルダウンロード 画面: 「No1」ボタン押下
        # 133 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 134 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 135 納品物管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_135")

        # 136 スケジュール個別追加 画面: 「補助金一括算定本処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_136_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 137 実行指示 画面: パラメータを入力
        params = [
            {"title": "利用年月開始", "type": "text",
             "value": case_data_137_ZEAF002200.get("riyou_nengetsu_kaishi_ymd", "")},
            {"title": "利用年月終了", "type": "text",
             "value": case_data_137_ZEAF002200.get("riyou_nengetsu_shuuryou_ymd", "")},
            {"title": "再処理フラグ", "type": "select",
             "value": case_data_137_ZEAF002200.get("saishori_flag_cmb", "")},
            {"title": "決定年月日", "type": "text",
             "value": case_data_137_ZEAF002200.get("kettei_nengetsubi_ymd", "")},
            {"title": "計算対象", "type": "select",
             "value": case_data_137_ZEAF002200.get("keisan_taishou_cmb", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_137")

        # 138 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 139 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_139")

        # 140 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 141 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_141")

        # 142 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 143 ファイルダウンロード 画面: 「No1」ボタン押下
        # 144 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 145 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 146 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 147 メインメニュー 画面: 「即時実行」クリック
        # 148 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=False)
        tab_index += 1

        # 149 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：認可外支払管理  <処理名>：補助金支払通知書出力処理
        # 150 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_149_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_149_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_149_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_150")

        # 151 スケジュール個別追加 画面: 「 補助金支払対象一覧出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_151_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)

        # 152 実行指示 画面: パラメータを入力
        params = [
            {"title": "処理区分", "type": "select", "value": case_data_152_ZEAF002200.get("shori_kubun_cmb", "")},
            {"title": "所管区", "type": "select",
             "value": case_data_152_ZEAF002200.get("shokanku_cmb", "")},
            {"title": "支所", "type": "select",
             "value": case_data_152_ZEAF002200.get("shisho_cmb", "")},
            {"title": "発行年月日", "type": "text",
             "value": case_data_152_ZEAF002200.get("hakkou_nengetsubi_ymd", "")},
            {"title": "振込年月日", "type": "text",
             "value": case_data_152_ZEAF002200.get("furikomi_nengetsubi_ymd", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_152_ZEAF002200.get("yuubin_ku_nai_tokubetsu_umu_cmb", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_152")

        # 153 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 154 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_154")

        # 155 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 156 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_156")

        # 157 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 158 ファイルダウンロード 画面: 「No1」ボタン押下
        # 159 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 160 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 161 納品物管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_161")

        # 162 スケジュール個別追加 画面: 「補助金支払通知書出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_162_ZEAF000400.get("batch_job_004", ""),
                                             tab_index=tab_index)

        # 163 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_163_ZEAF002200.get("shokanku_cmb", "")},
            {"title": "支所", "type": "select",
             "value": case_data_163_ZEAF002200.get("shisho_cmb", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_163")

        # 164 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # # 165 実行管理 画面: 「検索」ボタン押下
        # self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        # self.screen_shot("実行管理画面_165")
        #
        # # 166 実行管理 画面: 正常終了した処理の「No」ボタン押下
        # self.click_button_by_label("1")
        #
        # # 167 結果確認 画面: 「納品物確認」ボタン押下
        # self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        # self.screen_shot("納品物管理画面_167")
        #
        # # 168 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # # 169 ファイルダウンロード 画面: 「No1」ボタン押下
        # # 170 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # # 171 納品物管理 画面: 「×」ボタン押下
        # self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 172 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 173 メインメニュー 画面: 「即時実行」クリック
        # 174 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=False)
        tab_index += 1

        # 175 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：認可外支払管理 <処理名>：補助金支払却下通知書出力処理
        # 176 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_175_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_175_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_175_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_176")

        # 177 スケジュール個別追加 画面: 「 補助金支払却下対象一覧出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_177_ZEAF000400.get("batch_job_005", ""),
                                             tab_index=tab_index)

        # 178 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_178_ZEAF002200.get("shokanku_cmb", "")},
            {"title": "支所", "type": "select",
             "value": case_data_178_ZEAF002200.get("shisho_cmb", "")},
            {"title": "決定年月日開始", "type": "text",
             "value": case_data_178_ZEAF002200.get("kettei_nengetsubi_kaishi_ymd", "")},
            {"title": "決定年月日終了", "type": "text",
             "value": case_data_178_ZEAF002200.get("kettei_nengetsubi_shuuryou_ymd", "")},
            {"title": "代理施設有無", "type": "select",
             "value": case_data_178_ZEAF002200.get("dairi_shisetsu_umu_cmb", "")},
            {"title": "発行年月日", "type": "text", "value": case_data_178_ZEAF002200.get("hakkou_nengetsubi_ymd", "")},
            {"title": "並び順", "type": "select",
             "value": case_data_178_ZEAF002200.get("narabijun_cmb", "")},
            {"title": "再発行区分", "type": "select",
             "value": case_data_178_ZEAF002200.get("saihakkou_kubun_cmb", "")},
            {"title": "郵便区内特別有無", "type": "select",
             "value": case_data_178_ZEAF002200.get("yuubin_ku_nai_tokubetsu_umu_cmb", "")},
            {"title": "児童宛名コード", "type": "text",
             "value": case_data_178_ZEAF002200.get("jidou_atenmei_code", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_178")

        # 179 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 180 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_180")

        # 181 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 182 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_182")

        # 183 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 184 ファイルダウンロード 画面: 「No1」ボタン押下
        # 185 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 186 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 187 納品物管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_187")

        # 188 スケジュール個別追加 画面: 「補助金支払却下通知書出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_188_ZEAF000400.get("batch_job_006", ""),
                                             tab_index=tab_index)

        # 189 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_189_ZEAF002200.get("shokanku_cmb", "")},
            {"title": "支所", "type": "select",
             "value": case_data_189_ZEAF002200.get("shisho_cmb", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_189")

        # 190 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # # 191 実行管理 画面: 「検索」ボタン押下
        # self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        # self.screen_shot("実行管理 画面_191")
        #
        # # 192 実行管理 画面: 正常終了した処理の「No」ボタン押下
        # self.click_button_by_label("1")
        #
        # # 193 結果確認 画面: 「納品物確認」ボタン押下
        # self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        # self.screen_shot("納品物管理 画面_193")
        #
        # # 194 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # # 195 ファイルダウンロード 画面: 「No1」ボタン押下
        # # 196 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # # 197 納品物管理 画面: 「×」ボタン押下
        # self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 198 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 199 メインメニュー 画面: 「即時実行」クリック
        # 200 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=False)
        tab_index += 1

        # 201 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：認可外支払管理 <処理名>：補助金支払振込データ作成
        # 202 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_201_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_201_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_201_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_202")

        # 203 スケジュール個別追加 画面: 「 補助金支払振込データ作成」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_203_ZEAF000400.get("batch_job_007", ""),
                                             tab_index=tab_index)

        # 204 実行指示 画面: パラメータを入力
        params = [
            {"title": "発行年月日", "type": "text", "value": case_data_204_ZEAF002200.get("hakkou_nengetsubi_ymd", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_204")

        # 205 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 206 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=300, time_span_sec=20)
        self.screen_shot("実行管理 画面_206")

        # 207 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 208 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理_画面_208")

        # 209 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 210 ファイルダウンロード 画面: 「No1」ボタン押下
        # 211 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 212 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 213 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 214 メインメニュー 画面: 「即時実行」クリック
        # 215 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=False)
        tab_index += 1

        # 216 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：認可外支払管理 <処理名>：補助金支払締め・解除処理
        # 217 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_216_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_216_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_216_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加_画面_217")

        # 218 スケジュール個別追加 画面: 「 補助金支払締め・解除」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_218_ZEAF000400.get("batch_job_008", ""),
                                             tab_index=tab_index)

        # 219 実行指示 画面: パラメータを入力
        params = [
            {"title": "処理区分", "type": "select", "value": case_data_219_ZEAF002200.get("shori_kubun_cmb", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_219")

        # 220 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 221 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_221")
