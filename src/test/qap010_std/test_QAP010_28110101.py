from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28110101(KodomoSiteTestCaseBase):
    """TestQAP010_28110101"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28110101(self):
        case_data_007_ZEAF000400 = self.test_data["007_ZEAF000400"]
        case_data_008_ZEAF000400 = self.test_data["008_ZEAF000400"]
        case_data_012_ZEAF002800 = self.test_data["012_ZEAF002800"]
        case_data_018_ZEAF002200 = self.test_data["018_ZEAF002200"]
        case_data_028_ZEAF000400 = self.test_data["028_ZEAF000400"]
        case_data_029_ZEAF002200 = self.test_data["029_ZEAF002200"]
        case_data_040_QAZF100001 = self.test_data["040_QAZF100001"]
        case_data_041_QAZF100002 = self.test_data["041_QAZF100002"]
        case_data_043_QAPF100300 = self.test_data["043_QAPF100300"]
        case_data_044_QAPF222000 = self.test_data["044_QAPF222000"]
        case_data_046_QAPF222000 = self.test_data["046_QAPF222000"]
        case_data_047_QAPF222000 = self.test_data["047_QAPF222000"]
        case_data_048_QAPF222000 = self.test_data["048_QAPF222000"]
        case_data_050_FADF100300 = self.test_data["050_FADF100300"]
        case_data_053_FADF100400 = self.test_data["053_FADF100400"]
        tab_index = 0

        # 1, 2 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 3 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 4 メインメニュー 画面: 「即時実行」クリック
        # 5 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=True)
        tab_index += 1

        # 6 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：認可外支払管理 <処理名>：補助金申請一括取込
        # 7 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_007_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_007_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_007_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_7")

        # 8 スケジュール個別追加 画面: 「補助金申請取込対象者出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_008_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 9 実行指示 画面: 「受理物登録」ボタン押下
        # self.click_by_id("tab0" + str(tab_index) + "_ZEAF002200_BtnJuributsu_button")
        # tab_index += 1
        # self.screen_shot("受理物登録 画面_9")

        # 10 受理物登録 画面: No1「アップロード」ボタン押下
        # 11 アップロードダイアログ 画面: 「ファイルの選択」押下
        # 12 アップロードダイアログ 画面: 対象ファイルをダブルクリック
        # 13 アップロードダイアログ 画面: 「追加」ボタン押下
        # 14 アップロードダイアログ 画面: 「送信」ボタン押下
        # self.upload_file_by_path(file_path=case_data_012_ZEAF002800.get("upload_file_path_1", ""),
        #                          screen_shot_name="受理物登録 画面_12")

        # 15 受理物登録 画面: 「件数表示」ボタン押下

        # 16 受理物登録 画面: 「登録実行」ボタン押下
        # self.click_by_id("tab0" + str(tab_index) + "_ZEAF002800_BtnTourokuJico_button")
        # self.screen_shot("受理物登録 画面_16")

        # 17 受理物登録 画面: 「受理物登録」タブを削除
        # self.find_element_by_xpath("//*[@id='0" + str(tab_index) + "']/span[2]").click()
        # tab_index -= 1

        # 18 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_018_ZEAF002200.get("shokanku_cmb", "")},
            {"title": "支所", "type": "select", "value": case_data_018_ZEAF002200.get("shisho_cmb", "")},
            {"title": "利用年月開始", "type": "text",
             "value": case_data_018_ZEAF002200.get("riyou_nengetsu_kaishi_ymd", "")},
            {"title": "利用年月終了", "type": "text",
             "value": case_data_018_ZEAF002200.get("riyou_nengetsu_shuuryou_ymd", "")},
            {"title": "施設コード", "type": "text", "value": case_data_018_ZEAF002200.get("shisetsu_koudo", "")},
            {"title": "前月金額反映有無", "type": "select",
             "value": case_data_018_ZEAF002200.get("zengetsu_kingaku_hanei_umu_cmb", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_18")

        # 19 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 20 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_20")

        # 21 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 22 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_22")

        # 23 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 24 ファイルダウンロード 画面: 「No1」ボタン押下
        # 25 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 26 帳票（PDF） 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 27 結果確認 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_27")

        # 28 スケジュール個別追加 画面: 「補助金申請一括取込」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_028_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 29 実行指示 画面: パラメータを入力
        params = [
            {"title": "発行年月日", "type": "text", "value": case_data_029_ZEAF002200.get("hakkou_nengetsubi_ymd", "")},
            {"title": "ワーニングデータ登録区分", "type": "select",
             "value": case_data_029_ZEAF002200.get("warning_data_touroku_kubun_cmb", "")},
            {"title": "並び順", "type": "select", "value": case_data_029_ZEAF002200.get("narabijun_cmb", "")},
            {"title": "支払予定年月日", "type": "text",
             "value": case_data_029_ZEAF002200.get("shiharai_yotei_nengetsubi_ymd", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_29")

        # 30 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 31 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_31")

        # 32 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 33 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_33")

        # 34 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 35 ファイルダウンロード 画面: 「No1」ボタン押下
        # 36 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 37 帳票（PDF） 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 38 メインメニュー 画面: メインメニューから「認可外申請管理」をクリック
        # 39 メインメニュー 画面: 「認可外申請検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="認可外申請管理", menu_level_2="認可外申請検索",
                                 is_new_tab=False)
        tab_index += 1

        # 40 認可外申請検索 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_040_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_040_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_040_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_040_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_040_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_040_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_040_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_040_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_040_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_040_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_040_QAZF100001.get("katagaki", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_040_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_040_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_040_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_040_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_040_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件を入力 画面_40")

        # 41 認可外申請検索 画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_041_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code=atena_code, tab_index=tab_index, col_number=1)
        self.click_button_by_label("1")

        # 42 世帯台帳 画面: 「児童一覧」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100300", label="児童一覧")

        # 43 世帯台帳 画面: 対象児童の「No」ボタン押下
        self.click_kodomo_by_atena_code(
            kodomo_atena_code=case_data_043_QAPF100300.get("kodomo_atena_code", ""),
            tab_index=tab_index)
        self.screen_shot("利用者申請管理 画面_43")

        # 44 利用者申請管理 画面: 「支払管理」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF221000_btnShiharaiKanri_button")
        self.screen_shot("補助金申請管理 画面_44")
        # 年度
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF222000_selKensakuJokenNendo_select",
            text=case_data_044_QAPF222000.get("nen_do_cmb", ""))

        # 45 補助金申請管理 画面: 「追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF222000_btnAddChg_button")

        # 46 補助金申請管理 画面: 利用年月を入力
        # 利用年月
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoYM_textboxInput",
            value=case_data_046_QAPF222000.get("riyou_nengetsu", ""))
        # 請求年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtSeikyuYMD_textboxInput",
            value=case_data_046_QAPF222000.get("seikyu_nengetsubi", ""))
        self.screen_shot("補助金申請管理 画面_46")

        # 47 補助金申請管理 画面: 「虫眼鏡」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZZZ000000_btnShisetuKensaku_button")
        # 利用施設
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyouServiceInf_select",
            text=case_data_047_QAPF222000.get("riyou_shisetsu", ""))

        # 48 補助金申請管理 画面: 利用施設、納付額を入力
        # 納付額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNoufuGaku_textboxInput",
            value=case_data_048_QAPF222000.get("noufu_gaku", ""))
        # 入園料年額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNuenryoNenGaku_textboxInput",
            value=case_data_048_QAPF222000.get("nyuenryou_nengaku", ""))
        # 支払予定日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtShiharaiYMD_textboxInput",
            value=case_data_048_QAPF222000.get("shiharai_yoteibi", ""))
        # 利用日数
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoNissu_textboxInput",
            value=case_data_048_QAPF222000.get("riyou_nissu", ""))
        # 年度内在園月数
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNedonaiZaenGetusu_textboxInput",
            value=case_data_048_QAPF222000.get("nendounai_zaien_gessuu", ""))
        # 支払方法区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSiharaiHouhouKubun_select",
            text=case_data_048_QAPF222000.get("shiharai_houhou_kubun", ""))
        # 開所日数
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHrakiSyoNissu_textboxInput",
            value=case_data_048_QAPF222000.get("kaisho_nissu", ""))
        # 調整額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtChouseigaku_textboxInput",
            value=case_data_048_QAPF222000.get("chousei_gaku", ""))
        # 支払方法
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSiharaiHouhou_select",
            text=case_data_048_QAPF222000.get("shiharai_houhou", ""))
        # 備考
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtBikou_textboxInput",
            value=case_data_048_QAPF222000.get("bikou", ""))
        self.screen_shot("補助金申請管理 画面_48")

        # 49 補助金申請管理 画面: 「口座登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF222000_btnKouzaReg_button")

        # 50 口座照会 画面:
        # 業務名：子ども
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100300_selGyomuNM_select",
            text=case_data_050_FADF100300.get("gyoumu_mei_cmb", ""))
        # 用途：認可外
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100300_selYoto_select",
            text=case_data_050_FADF100300.get("youto_cmb", ""))

        # 51 口座照会 画面: 「選択」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_FADF100300_btnSentaku_button")
        self.screen_shot("口座管理 画面_51")

        # 52 口座管理 画面: 「追加」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_FADF100400_btnAddChg_button")

        # 53 口座管理 画面: 異動日、金融機関名、預金科目、口座番号、口座名義人カナ、有効期間、納付区分を入力
        # 異動日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtIDobi_textboxInput",
            value=case_data_053_FADF100400.get("idoubi_ymd", ""))
        # 金融機関種別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selKinyukikanSB_select",
            text=case_data_053_FADF100400.get("kinyuukikan_shubetsu_cmb", ""))
        # 金融機関名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtGinkoCD_textboxInput",
            value=case_data_053_FADF100400.get("kinyuukikan_mei_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtShitenCD_textboxInput",
            value=case_data_053_FADF100400.get("kinyuukikan_mei_2", ""))
        # 預金科目
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selYokinKamoku_select",
            text=case_data_053_FADF100400.get("yokin_kamoku_cmb", ""))
        # 預金者電話番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtTEL_textboxInput",
            value=case_data_053_FADF100400.get("yokinjya_denwa_bangou", ""))
        # 有効期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtYukoKikanFrom_textboxInput",
            value=case_data_053_FADF100400.get("yuukou_kikan_from_ymd", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtYukoKikanTo_textboxInput",
            value=case_data_053_FADF100400.get("yuukou_kikan_to_ymd", ""))
        # 納付区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selZennoKbn_select",
            text=case_data_053_FADF100400.get("noufu_kubun_cmb", ""))
        # 送金方法
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selSoukinKbn_select",
            text=case_data_053_FADF100400.get("soukin_houhou_cmb", ""))
        # 口座番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaNo_textboxInput",
            value=case_data_053_FADF100400.get("kouza_bangou", ""))
        # 口座申込日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMoshikomibi_textboxInput",
            value=case_data_053_FADF100400.get("kouza_moushikomi_bi_ymd", ""))
        # 口座停止期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaTeishiKikanFrom_textboxInput",
            value=case_data_053_FADF100400.get("kouza_teishi_kikan_from_ymd", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaTeishiKikanTo_textboxInput",
            value=case_data_053_FADF100400.get("kouza_teishi_kikan_to_ymd", ""))
        # 公開 / 非公開
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selKokaiHikokai_select",
            text=case_data_053_FADF100400.get("koukai_hikoukai", ""))
        # 口座名義人カナ
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMeigiKanaShimei_textboxInput",
            value=case_data_053_FADF100400.get("kouza_meiginin_kana", ""))
        # 口座名義人漢字
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMeigiKanjiShimeiNM_textboxInput",
            value=case_data_053_FADF100400.get("kouza_meiginin_kanji", ""))
        self.screen_shot("口座管理 画面_53")

        # 54 口座管理 画面: 「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_FADF100400_regbtn_button")

        # 55 口座管理 画面: 「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_FADF100400_msg_span", msg="登録しました。")
        self.screen_shot("口座管理 画面_55")

        # 56 口座管理 画面: パンくず「補助金申請管理」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="FADF100400", label="補助金申請管理")
        self.screen_shot("補助金申請管理 画面_56")

        # 57 補助金申請管理 画面: 「最新口座取得」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF222000_btnSaishinKozaGet_button")

        # 58 補助金申請管理 画面: 「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF222000_regbtn_button")

        # 59 補助金申請管理 画面: 「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF222000_msg_span", msg="登録しました。")
        self.screen_shot("補助金申請管理 画面_59")
