from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070301(KodomoSiteTestCaseBase):
    """TestQAP010_28070301"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070301(self):
        tab_index = 0
        case_data_006_ZEAF000400 = self.test_data["006_ZEAF000400"]
        case_data_008_ZEAF000400 = self.test_data["008_ZEAF000400"]
        case_data_009_ZEAF002200 = self.test_data["009_ZEAF002200"]
        case_data_013_ZEAF000400 = self.test_data["013_ZEAF000400"]
        case_data_015_ZEAF000400 = self.test_data["015_ZEAF000400"]
        case_data_016_ZEAF002200 = self.test_data["016_ZEAF002200"]
        case_data_020_ZEAF000400 = self.test_data["020_ZEAF000400"]
        case_data_022_ZEAF000400 = self.test_data["022_ZEAF000400"]
        case_data_023_ZEAF002200 = self.test_data["023_ZEAF002200"]
        case_data_028_ZEAF002700 = self.test_data["028_ZEAF002700"]
        case_data_032_ZEAF002700 = self.test_data["032_ZEAF002700"]
        case_data_036_ZEAF002700 = self.test_data["036_ZEAF002700"]
        case_data_043_JAAF400100 = self.test_data["043_JAAF400100"]
        case_data_044_JAAF400200 = self.test_data["044_JAAF400200"]
        case_data_047_JAAF404800 = self.test_data["047_JAAF404800"]

        # 1, 2 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 3 メインメニュー 画面: メインメニューから「バッチ管理」クリック 
        # 4 メインメニュー 画面: 「即時実行」クリック 
        # 5 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=True)
        tab_index += 1
        self.wait_page_loaded(wait_timeout=10)

        # 6 バッチ管理 画面: <業務名>：宛名情報管理 <サブシステム名>：宛名 <処理名>：宛名・口座バッチマスタ作成処理 選択"
        # 7 バッチ管理 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_006_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_006_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_006_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("バッチ管理 画面_7")

        # 8 バッチ管理 画面: 「宛名・口座バッチマスタ作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_008_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 9 実行指示 画面: パラメータを入力
        params = [
            {"title": "業務名１", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_1", "")},
            {"title": "業務別基準日1", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijunbi_1", "")},
            {"title": "口座基準日1", "type": "text",
             "value": case_data_009_ZEAF002200.get("kouza_kijunbi_1", "")},
            {"title": "業務名2", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_2", "")},
            {"title": "業務別基準日2", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijunbi_2", "")},
            {"title": "口座基準日2", "type": "text",
             "value": case_data_009_ZEAF002200.get("kouza_kijunbi_2", "")},
            {"title": "業務名3", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_1", "")},
            {"title": "業務別基準日3", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijunbi_3", "")},
            {"title": "口座基準日3", "type": "text",
             "value": case_data_009_ZEAF002200.get("kouza_kijunbi_3", "")},
            {"title": "業務名4", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_4", "")},
            {"title": "業務別基準日4", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijunbi_4", "")},
            {"title": "口座基準日4", "type": "text",
             "value": case_data_009_ZEAF002200.get("kouza_kijunbi_4", "")},
            {"title": "業務名5", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_5", "")},
            {"title": "業務別基準日5", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijunbi_5", "")},
            {"title": "口座基準日5", "type": "text",
             "value": case_data_009_ZEAF002200.get("kouza_kijunbi_5", "")},
            {"title": "業務名6", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_6", "")},
            {"title": "業務別基準日6", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijunbi_6", "")},
            {"title": "口座基準日6", "type": "text",
             "value": case_data_009_ZEAF002200.get("kouza_kijunbi_6", "")},
            {"title": "業務名7", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_7", "")},
            {"title": "業務別基準日7", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijunbi_7", "")},
            {"title": "口座基準日7", "type": "text",
             "value": case_data_009_ZEAF002200.get("kouza_kijunbi_7", "")},
            {"title": "業務名8", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_8", "")},
            {"title": "業務別基準日8", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijunbi_8", "")},
            {"title": "口座基準日8", "type": "text",
             "value": case_data_009_ZEAF002200.get("kouza_kijunbi_8", "")},
            {"title": "業務名9", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_8", "")},
            {"title": "業務別基準日9", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijunbi_9", "")},
            {"title": "口座基準日9", "type": "text",
             "value": case_data_009_ZEAF002200.get("kouza_kijunbi_9", "")},
            {"title": "業務名10", "type": "select", "value": case_data_009_ZEAF002200.get("gyoumu_mei_10", "")},
            {"title": "業務別基準日10", "type": "text",
             "value": case_data_009_ZEAF002200.get("gyoumu_betsu_kijunbi_10", "")},
            {"title": "口座基準日10", "type": "text",
             "value": case_data_009_ZEAF002200.get("kouza_kijunbi_10", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_9")

        # 10 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 11 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_11")

        # 12 実行管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("バッチ管理 画面_12")

        # 13 バッチ管理 画面: <業務名>：収納・滞納 <サブシステム名>：マスタ作成 <処理名>：収納マスタ＆収納用宛名口座マスタ作成処理（統合版）選択"
        # 14 バッチ管理 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_013_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_013_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_013_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("バッチ管理 画面_14")

        # 15 バッチ管理 画面: 「収納マスタ＆収納用宛名口座マスタ作成本処理（統合版）」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_015_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 16 実行指示 画面: パラメータを入力
        params = [
            {"title": "未納指定", "type": "select",
             "value": case_data_016_ZEAF002200.get("minou_shitei_cbx")},
            {"title": "科目", "type": "checkbox", "value": case_data_016_ZEAF002200.get("kamoku_chk").values()},
            {"title": "調定発生年度開始", "type": "select",
             "value": case_data_016_ZEAF002200.get("choutei_hassei_nendo_kaishi_cbx")},
            {"title": "調定発生年度終了", "type": "select",
             "value": case_data_016_ZEAF002200.get("choutei_hassei_nendo_shuuryou_cbx")},
            {"title": "課税根拠年度開始", "type": "select",
             "value": case_data_016_ZEAF002200.get("kazei_konkyou_nendo_kaishi_cbx")},
            {"title": "課税根拠年度終了", "type": "select",
             "value": case_data_016_ZEAF002200.get("kazei_konkyou_nendo_shuuryou_cbx")},
            {"title": "期別開始", "type": "text",
             "value": case_data_016_ZEAF002200.get("kibetsu_kaishi")},
            {"title": "期別終了", "type": "text",
             "value": case_data_016_ZEAF002200.get("kibetsu_shuuryou")},
            {"title": "納期限開始", "type": "text",
             "value": case_data_016_ZEAF002200.get("noukigen_kaishi_ymd")},
            {"title": "納期限終了", "type": "text",
             "value": case_data_016_ZEAF002200.get("noukigen_shuuryou")},
            {"title": "除外納付区分", "type": "text",
             "value": case_data_016_ZEAF002200.get("jogai_noufu_kubun")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_16")

        # 17 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 18 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_18")

        # 19 実行管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_19")

        # 20 バッチ管理 画面: <業務名>：収納・滞納 <サブシステム名>：月次 <処理名>：納付書一括作成処理 選択"
        # 21 バッチ管理 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_020_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_020_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_020_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("バッチ管理 画面_21")

        # 22 バッチ管理 画面: 「納付書作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_022_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)
        # 23 実行指示 画面: パラメータを入力
        params = [
            {"title": "科目", "type": "checkbox", "value": case_data_023_ZEAF002200.get("kamoku_chk").values()},
            {"title": "調定発生年度開始", "type": "select",
             "value": case_data_023_ZEAF002200.get("choutei_hassei_nendo_kaishi_cbx")},
            {"title": "調定発生年度終了", "type": "select",
             "value": case_data_023_ZEAF002200.get("choutei_hassei_nendo_shuuryou_cbx")},
            {"title": "課税根拠年度開始", "type": "select",
             "value": case_data_023_ZEAF002200.get("kazei_konkyou_nendo_kaishi_cbx")},
            {"title": "課税根拠年度終了", "type": "select",
             "value": case_data_023_ZEAF002200.get("kazei_konkyou_nendo_shuuryou_cbx")},
            {"title": "期別開始", "type": "text",
             "value": case_data_023_ZEAF002200.get("kibetsu_kaishi")},
            {"title": "期別終了", "type": "text",
             "value": case_data_023_ZEAF002200.get("kibetsu_shuuryou")},
            {"title": "納期限開始", "type": "text",
             "value": case_data_023_ZEAF002200.get("noukigen_kaishi_ymd")},
            {"title": "発送日", "type": "text",
             "value": case_data_023_ZEAF002200.get("hassoubi_ymd")},
            {"title": "発行条件", "type": "select",
             "value": case_data_023_ZEAF002200.get("hakkou_jouken_cbx")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_23")

        # 24 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 25 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_25")

        # 26 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 27 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")

        # 28 納品物管理 画面: 納品物「納付書.pdf」の「ダウンロード」ボタン押下
        # 29 ファイルダウンロード 画面: 「No1」ボタン押下
        # 30 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 31 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_028_ZEAF002700.get("report_name", ""))

        # 32 納品物管理 画面: 納品物「発布一覧.pdf」の「ダウンロード」ボタン押下
        # 33 ファイルダウンロード 画面: 「No1」ボタン押下
        # 34 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 35 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_032_ZEAF002700.get("report_name", ""))

        # 36 納品物管理 画面: 納品物「発布一覧.csv」の「ダウンロード」ボタン押下
        # 37 ファイルダウンロード 画面: 「No1」ボタン押下
        # 38 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 39 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index,
                                   report_name=case_data_036_ZEAF002700.get("report_name", ""))

        # 40 メインメニュー 画面: メインメニューから「収納」をクリックする 
        # 41 メインメニュー 画面: 「個人別収納状況」をクリック 
        # 42 メインメニュー 画面: 「納付書発行」をダブルクリック
        self._goto_menu_by_label(menu_level_1="収納", menu_level_2="個人別収納状況",
                                 menu_level_3="納付書発行",
                                 is_new_tab=False)
        tab_index += 1

        # 43 （個人・法人）検索条件入力 画面: 検索条件を入力
        # カナ氏名・名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanaShimeiNM_textboxInput",
            value=case_data_043_JAAF400100.get("kana_shimei_meishou", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKana_select",
            text=case_data_043_JAAF400100.get("kana_shimei_meishou_cbx", ""))
        # 漢字氏名・名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanjiShimeiNM_textboxInput",
            value=case_data_043_JAAF400100.get("kanji_shimei_meishou", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKanji_select",
            text=case_data_043_JAAF400100.get("kanji_shimei_meishou_cbx", ""))
        # カナ氏名・名称２
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanaShimeiNM2_textboxInput",
            value=case_data_043_JAAF400100.get("kana_shimei_meishou_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKana2_select",
            text=case_data_043_JAAF400100.get("kana_shimei_meishou_2_cbx", ""))
        # 漢字氏名・名称２
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKanjiShimeiNM2_textboxInput",
            value=case_data_043_JAAF400100.get("kanji_shimei_meishou_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiKanji2_select",
            text=case_data_043_JAAF400100.get("kanji_shimei_meishou_2_cbx", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtDate01Left_textboxInput",
            value=case_data_043_JAAF400100.get("seinengappi_from_ymd", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiBirth1_select",
            text=case_data_043_JAAF400100.get("seinengappi_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtDate02Left_textboxInput",
            value=case_data_043_JAAF400100.get("seinengappi_to_ymd", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selSeibetsu_select",
            text=case_data_043_JAAF400100.get("seibetsu_cbx", ""))
        # 行政区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selGyoseiku_select",
            text=case_data_043_JAAF400100.get("gyouseiku_cbx", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_YubinbangouLeftTxt_textboxInput",
            value=case_data_043_JAAF400100.get("yuubin_bangou_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_YubinbangouRightTxt_textboxInput",
            value=case_data_043_JAAF400100.get("yuubin_bangou_2", ""))
        # 住所・所在地
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtJusho_textboxInput",
            value=case_data_043_JAAF400100.get("juusho_shozaiichi", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selAimaiJusho_select",
            text=case_data_043_JAAF400100.get("juusho_shozaiichi_cbx", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtKatagaki_textboxInput",
            value=case_data_043_JAAF400100.get("katagaki", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtJuminCD_textboxInput",
            value=case_data_043_JAAF400100.get("juumin_code", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtSetaiCD_textboxInput",
            value=case_data_043_JAAF400100.get("setai_code", ""))
        # 特徴指定番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtTokuchoShiteiNo_textboxInput",
            value=case_data_043_JAAF400100.get("tokuchou_shitei_bangou", ""))
        # 通知番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_txtTsuchiNo_textboxInput",
            value=case_data_043_JAAF400100.get("tsuuchi_bangou", ""))
        # 検索区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF400100_selKensakuKbn_select",
            text=case_data_043_JAAF400100.get("kensaku_kubun_cbx", ""))
        self.screen_shot("個人・法人）検索条件入力 画面_43")

        # 44 （個人・法人）検索条件入力 画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_JAAF400100_WrCmnBtn05_button")
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_JAAF400200_pages1"):
            atena_code = case_data_044_JAAF400200.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code=atena_code, tab_index=tab_index, col_number=1)
        self.screen_shot("（個人・法人）検索条件入力 画面_44")

        # 45 納付書発行 画面: 出力対象にチェックを入力 
        # 46 納付書発行 画面: 納付書区分に「再発」を選択 
        # 47 納付書発行 画面: 使用期限を未来日において指定
        # 納付書区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF404800_selNofushoKbn_select",
            text=case_data_047_JAAF404800.get("noufusho_kubun_cbx", ""))
        # 使用期限
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_JAAF404800_txtCVSShiyoKigen_textboxInput",
            value=case_data_047_JAAF404800.get("shiyou_kigen_ymd", ""))
        self.screen_shot("納付書発行 画面_47")

        # 48 納付書発行 画面: 印刷ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_JAAF404800_gyomu_printbtn_button")

        # 49 （収納）印刷指示 画面: 出力帳票にチェックを入力 
        # 50 （収納）印刷指示 画面: パラメータを入力
        self.screen_shot("（収納）印刷指示 画面_50")

        # 51 （収納）印刷指示 画面: 印刷ボタンをクリック
        self.screen_shot("（収納）印刷指示 画面_51")

        # 52 ファイルダウンロード 画面: 「No1」ボタン押下
        # 53 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 54 （収納）印刷指示 画面: 「×」ボタン押下
