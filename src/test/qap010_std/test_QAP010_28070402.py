from base.kodomo_case import KodomoSiteTestCaseBase


class Test_QAP010_28070402(KodomoSiteTestCaseBase):
    """Test_QAP010_28070402"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070402(self):
        tab_index = 0
        case_data_044_ZEAF000400 = self.test_data["044_ZEAF000400"]
        case_data_046_ZEAF000400 = self.test_data["046_ZEAF000400"]
        # 40 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 41 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 42 メインメニュー 画面:「即時実行」クリック
        # 43 メインメニュー 画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=True)
        tab_index += 1
        self.wait_page_loaded(wait_timeout=10)

        # 44 スケジュール個別追加 画面: <業務名>：収納・滞納 <サブシステム名>：月次 <処理名>：口座振替請求処理
        # 45 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_044_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_044_ZEAF000400.get("sabu_shisutemu_mei", ""),
                                        shoriNM=case_data_044_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_45")

        # 46 スケジュール個別追加 画面:「口座振替請求後処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_046_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 47 実行指示 画面:「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 48 実行管理画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_48")
