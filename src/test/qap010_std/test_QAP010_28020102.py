from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28020102(KodomoSiteTestCaseBase):
    """TestQAP010_28020102"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28020102(self):
        tab_index = 0
        case_data_039_ZEAF000400 = self.test_data["039_ZEAF000400"]
        case_data_041_ZEAF000400 = self.test_data["041_ZEAF000400"]
        case_data_042_ZEAF002200 = self.test_data["042_ZEAF002200"]
        case_data_046_ZEAF000400 = self.test_data["046_ZEAF000400"]
        case_data_048_ZEAF000400 = self.test_data["048_ZEAF000400"]
        case_data_049_ZEAF002200 = self.test_data["049_ZEAF002200"]

        # 35 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 36 メインメニュー 画面: 「バッチ管理」ボタン押下
        # 37 メインメニュー 画面: 「即時実行」ボタン押下
        # 38 メインメニュー 画面: 「スケジュール個別追加」ボタンをダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=True)
        tab_index += 1

        # 39 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：選考 <処理名>：入所自動選考データ作成処理
        # 40 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_039_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_039_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_039_ZEAF000400.get("shori_mei_cmb", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加_画面_40")

        # 41 スケジュール個別追加 画面:「入所自動選考データ作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_041_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 42 実行指示 画面: パラメータを入力
        params = [
            {"title": "処理区分", "type": "select",
             "value": case_data_042_ZEAF002200.get("shori_kubun_cbx", "")},
            {"title": "申請有効期限日", "type": "text",
             "value": case_data_042_ZEAF002200.get("shinsei_yuko_kigenbi_ymd", "")},
            {"title": "選考データ名", "type": "text",
             "value": case_data_042_ZEAF002200.get("senko_deeta_mei", "")},
            {"title": "選考枠", "type": "select",
             "value": case_data_042_ZEAF002200.get("senko_kanri_bangou", "")},
            {"title": "入所申込日開始", "type": "text",
             "value": case_data_042_ZEAF002200.get("nyuusho_moushikomi_bi_kaishi_ymd", "")},
            {"title": "入所申込日終了", "type": "text",
             "value": case_data_042_ZEAF002200.get("nyuusho_moushikomi_bi_shuuryou_ymd", "")},
            {"title": "入所予定日", "type": "text",
             "value": case_data_042_ZEAF002200.get("nyuusho_yoteibi_ymd", "")},
            {"title": "基準日", "type": "text",
             "value": case_data_042_ZEAF002200.get("kijunbi_ymd", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("スケジュール個別追加_画面_42")

        # 43 実行指示 画面:「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 44 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_44")

        # 45 納品物管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_45")

        # 46 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：選考 <処理名>：入所申込一覧出力処理
        # 47 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_046_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_046_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_046_ZEAF000400.get("shori_mei_cmb", ""), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加_画面_47")

        # 48 スケジュール個別追加 画面:「入所申込一覧出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_048_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)

        # 49 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select",
             "value": case_data_049_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select",
             "value": case_data_049_ZEAF002200.get("shisho_cbx", "")},
            {"title": "選考管理番号", "type": "select",
             "value": case_data_049_ZEAF002200.get("senkou_kanri_bangou_cbx", "")},
            {"title": "施設別入所申込件数表", "type": "select",
             "value": case_data_049_ZEAF002200.get("shisetsu_betsu_nyuujo_moushikomi_kensuu_hyou_cbx", "")},
            {"title": "入所申込一覧（施設）", "type": "select",
             "value": case_data_049_ZEAF002200.get("nyuusho_moushikomi_ichiran_shisetsu_cbx", "")},
            {"title": "入所申込一覧（世帯）", "type": "select",
             "value": case_data_049_ZEAF002200.get("nyuusho_moushikomi_ichiran_setai_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("スケジュール個別追加_画面_49")

        # 50 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 51 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_51")

        # 52 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 53 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認_画面_53")

        # 54 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 55 ファイルダウンロード 画面:「No1」ボタン押下
        # 56 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 57 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
