from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28140106(KodomoSiteTestCaseBase):
    """TestQAP010_28140106"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28140106(self):
        case_data_200_QAZF100001 = self.test_data["200_QAZF100001"]
        case_data_201_QAZF100002 = self.test_data["201_QAZF100002"]
        case_data_203_QAPF100300 = self.test_data["203_QAPF100300"]
        case_data_206_QAPF222000 = self.test_data["206_QAPF222000"]
        case_data_208_QAPF222000 = self.test_data["208_QAPF222000"]
        tab_index = 0

        # 197 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 198 メインメニュー 画面: メインメニューから「認可外申請管理」をクリック
        # 199 メインメニュー 画面:「認可外申請検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="認可外申請管理", menu_level_2="認可外申請検索",
                                 menu_level_3=None, is_new_tab=True)
        tab_index += 1

        # 200 認可外申請検索 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_200_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_200_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_200_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_200_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_200_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_200_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_200_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_200_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_200_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_200_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_200_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_200_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_200_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_200_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_200_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_200_QAZF100001.get("denwa_bangou_ichi", ""))

        self.screen_shot("検索条件入力 画面_200")

        # 201 認可外申請検索 画面:「検索」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_201_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code, tab_index=tab_index, col_number=1)
        self.click_button_by_label("1")

        # 202 世帯台帳 画面:「児童一覧」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100300", label="児童一覧")

        # 203 世帯台帳 画面: 対象児童の「No」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_203_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)
        self.screen_shot("利用者申請管理 画面_203")

        # 204 利用者申請管理 画面:「支払管理」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAPF221000_btnShiharaiKanri_button")
        self.screen_shot("補助金申請管理 画面_204")

        # 205 利用者申請管理 画面:「追加」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAPF222000_btnAddChg_button")

        # 206 利用者申請管理 画面: 利用年月を入力
        # 利用年月
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoYM_textboxInput",
            value=case_data_206_QAPF222000.get("yoyaku_ymd", ""))
        self.screen_shot("利用者申請管理 画面_206")

        # 207 利用者申請管理 画面:「虫眼鏡」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_ZZZ000000_btnShisetuKensaku_button")

        # 208 利用者申請管理 画面: 利用施設、納付額を入力
        # 補助金申請管理
        # 利用施設
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyouServiceInf_select",
            text=case_data_208_QAPF222000.get("riyou_shisetsu", ""))
        # 納付額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNoufuGaku_textboxInput",
            value=case_data_208_QAPF222000.get("noufu_gaku", ""))
        # 利用日数
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoNissu_textboxInput",
            value=case_data_208_QAPF222000.get("riyou_nisu", ""))
        # 開所日数
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHrakiSyoNissu_textboxInput",
            value=case_data_208_QAPF222000.get("kaisho_nisu", ""))
        # 支払方法
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSiharaiHouhou_select",
            text=case_data_208_QAPF222000.get("shiharai_houhou", ""))
        # 入園料年額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNuenryoNenGaku_textboxInput",
            value=case_data_208_QAPF222000.get("nyuen_ryo_nengaku", ""))
        # 年度内在園月数
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNedonaiZaenGetusu_textboxInput",
            value=case_data_208_QAPF222000.get("nendo_nai_zai_en_gatsu_suu", ""))
        # 入園料月額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNuenryoGetuGaku_textboxInput",
            value=case_data_208_QAPF222000.get("nyuen_ryo_getugaku", ""))
        # 限度額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtGendoGaku_textboxInput",
            value=case_data_208_QAPF222000.get("gendo_gaku", ""))
        # 決定年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtKetteiYMD_textboxInput",
            value=case_data_208_QAPF222000.get("kettei_nengappi", ""))
        # 決定結果
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selKetteiKekka_select",
            text=case_data_208_QAPF222000.get("kettei_kekka", ""))
        # 却下理由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selKyakkaRiyuu_select",
            value=case_data_208_QAPF222000.get("kyakka_riyuu", ""))
        # 補助金額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojyoKingaku_textboxInput",
            value=case_data_208_QAPF222000.get("hojokin_gaku", ""))
        # 振込年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtFurikomiYMD_textboxInput",
            value=case_data_208_QAPF222000.get("furikomi_nengappi", ""))
        # 戻入額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtModouhairuGaku_textboxInput",
            value=case_data_208_QAPF222000.get("modoin_gaku", ""))
        # 戻入日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtModouhairuNi_textboxInput",
            value=case_data_208_QAPF222000.get("modoin_nengappi", ""))
        # 備考
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtBikou_textboxInput",
            value=case_data_208_QAPF222000.get("biko", ""))
        self.screen_shot("補助金申請管理 画面_208")

        # 209 利用者申請管理 画面:「最新口座取得」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAPF222000_btnSaishinKozaGet_button")

        # 210 補助金申請管理 画面:「登録」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAPF222000_regbtn_button")

        # 211 補助金申請管理 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF222000_msg_span",
                                 msg="登録しました。")
        self.screen_shot("補助金申請管理 画面_211")
