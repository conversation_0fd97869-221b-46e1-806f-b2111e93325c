from base.kodomo_case import KodomoSiteTestCaseBase
class TestQAP010_28090302(KodomoSiteTestCaseBase):
    """TestQAP010_28090302"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28090302(self):
        case_data_061 = self.test_data["061_QAZF100001"]
        case_data_063 = self.test_data["063_QAZF100001"]
        case_data_067 = self.test_data["067_QAPF221000"]
        tab_index = 0
        self.do_login_new_tab()

        # 59 メインメニュー 画面: メインメニューから「認可外申請管理」をクリック
        # 60 メインメニュー 画面: 「認可外申請検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="認可外申請管理", menu_level_2="認可外申請検索", is_new_tab=True)
        tab_index += 1

        # 61 認可外申請検索 画面: 検索条件を入力
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_061.get("kana_shimei_meishou", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_061.get("kana_shimei_meishou_sel", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_061.get("kanji_shimei_meishou", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_061.get("kanji_shimei_meishou_sel", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_061.get("seinengappi_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_061.get("seinengappi_sel", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_061.get("seinengappi_2", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_061.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_061.get("yuubin_bangou_ko_left", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_061.get("yuubin_bangou_ko_right", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_061.get("katagaki", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_061.get("juumin_koodo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_061.get("setai_koodo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_061.get("shokan_ku", ""))
        self.screen_shot("認可外申請検索 画面_61")

        # 62 認可外申請検索 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        # 63 世帯台帳 画面: 対象児童の「No」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_063.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code=atena_code, tab_index=tab_index)
        self.click_button_by_label("1")

        self.click_kodomo_by_atena_code(case_data_061.get("juumin_koodo", ""))
        self.screen_shot("利用者申請管理 画面_63")

        # 64 利用者申請管理 画面: 「修正」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF221000_btnEditChg_button")

        # 65 利用者申請管理 画面: 決定結果で「取下」を選択
        # 決定結果
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF221000_selKetteikekka_select",
            text=case_data_067.get("kettei_kekka", ""))
        self.screen_shot("利用者申請管理 画面_利用者申請管理_65")

        # 66 利用者申請管理 画面: 汎用項目タブを押下
        self.change_tab_by_label(tab_index, "QAPF221000", "汎用項目")

        # 67 利用者申請管理 画面: 取下年月日を入力
        # 取下年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_HanyouKoumoku1_textboxInput",
            value=case_data_067.get("torisage_nengappi", ""))
        self.screen_shot("利用者申請管理 画面_汎用項目_67")

        # 68 利用者申請管理 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF221000_regbtn_button")

        # 69 利用者申請管理 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF221000_msg_span", msg="更新しました。")
        self.screen_shot("利用者申請管理 画面_69")
