from base.kodomo_case import KodomoSiteTestCaseBase



class TestQAP010_28140105(KodomoSiteTestCaseBase):
    """TestQAP010_28140105"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28140105(self):

        case_data_151_ZEAF000400 = self.test_data["151_ZEAF000400"]
        case_data_153_ZEAF000400 = self.test_data["153_ZEAF000400"]
        case_data_154_ZEAF002200 = self.test_data["154_ZEAF002200"]
        case_data_164_ZEAF000400 = self.test_data["164_ZEAF000400"]
        case_data_168_ZEAF002800 = self.test_data["168_ZEAF002800"]
        case_data_174_ZEAF002200 = self.test_data["174_ZEAF002200"]
        case_data_185_QAZF100001 = self.test_data["185_QAZF100001"]
        case_data_186_QAZF100002 = self.test_data["186_QAZF100002"]
        case_data_188_QAPF100300 = self.test_data["188_QAPF100300"]
        case_data_191_QAPF222000 = self.test_data["191_QAPF221000"]
        tab_index = 0
        
        # 147 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()
        
        # 148 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 149 メインメニュー 画面:「即時実行」クリック
        # 150 メインメニュー 画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=True)
        tab_index += 1
        
        # 151 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：補足給付支払処理 <処理名>：補足給付支払申請一括取込
        # 152 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_151_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_151_ZEAF000400.get("sabushisutemu_mei"),
                                        shoriNM=case_data_151_ZEAF000400.get("shori_mei"), tab_index=tab_index)
        self.screen_shot("スケジュール個別追加 画面_152")
        
        # 153 スケジュール個別追加 画面:「補足給付支払申請取込対象者出力」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_153_ZEAF000400.get("batch_job_001"),
                                             tab_index=tab_index)
        
        # 154 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_154_ZEAF002200.get("shokanku", "")},
            {"title": "支所", "type": "select", "value": case_data_154_ZEAF002200.get("shisho", "")},
            {"title": "対象年月開始", "type": "text", "value": case_data_154_ZEAF002200.get("taishou_nengetsu_start", "")},
            {"title": "対象年月終了", "type": "text", "value": case_data_154_ZEAF002200.get("taishou_nengetsu_end", "")},
            {"title": "施設コード", "type": "text", "value": case_data_154_ZEAF002200.get("shisetsu_code", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_154")

        # 155 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 156 実行指示 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_156")

        # 157 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")
        
        # 158 実行管理 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理 画面_158")

        # 159 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 160 ファイルダウンロード 画面:「No1」ボタン押下
        # 161 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 162 ファイル 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
        self.screen_shot("納品物管理 画面_160")
        
        # 163 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加 画面_163")

        # 164 スケジュール個別追加 画面:「補足給付支払申請一括取込」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_164_ZEAF000400.get("batch_job_002"),
                                             tab_index=tab_index)
        
        # 165 実行指示 画面:「受理物登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002200_BtnJuributsu_button")
        tab_index += 1
        self.screen_shot("受理物登録 画面_165")
        
        # 166 受理物登録 画面: No1「アップロード」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002800_BtnUpload_1_3_button")
        
        # 167 受理物登録 画面:「ファイルの選択」押下
        # 168 受理物登録 画面: 対象ファイルをダブルクリック
        # 169 受理物登録 画面:「追加」ボタン押下
        # 170 受理物登録 画面:「送信」ボタン押下
        self.upload_file_by_path(file_path=case_data_168_ZEAF002800.get("upload_file_path", ""),
                                 screen_shot_name="受理物登録 画面_168")  
        
        # 171 受理物登録 画面:「件数表示」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002800_BtnKensuHyoji_button")
        
        # 172 受理物登録 画面:「登録実行」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002800_BtnTourokuJico_button")
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_ZEAF002800_msg_span",
                                 msg="登録しました。")
        self.screen_shot("受理物登録 画面_172")
        
        # 173 受理物登録 画面:「受理物登録」タブを削除
        remove_btn = self.find_element_by_css_selector(f'li[id="0{tab_index}"] span.removeTab')
        remove_btn.click()
        tab_index -= 1
                 
        # 174 実行指示 画面: パラメータを入力
        params = [
            {"title": "発行年月日", "type": "text", "value": case_data_174_ZEAF002200.get("hakkou_nengappi", "")},
            {"title": "ワーニングデータ登録区分", "type": "select", "value": case_data_174_ZEAF002200.get("shuturyoku_chouhyou", "")},
            {"title": "並び順", "type": "select", "value": case_data_174_ZEAF002200.get("nobiritsu", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示 画面_174")
        
        # 175 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 176 実行指示 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理 画面_176")

        # 177 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")     

        # 178 実行指示 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")   
        self.screen_shot("納品物管理 画面_178")

        # 179 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 180 ファイルダウンロード 画面:「No1」ボタン押下
        # 181 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 182 ファイル 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
        self.screen_shot("納品物管理 画面_180")
        
        # 183 メインメニュー 画面: メインメニューから「認可外申請管理」をクリック
        # 184 メインメニュー 画面:「認可外申請検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="認可外申請管理", menu_level_2="認可外申請検索",
                                 menu_level_3=None, is_new_tab=False)
        tab_index += 1
        
        # 185 認可外申請検索 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_185_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_185_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_185_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_185_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_185_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_185_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_185_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_185_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_185_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_185_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_185_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_185_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_185_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_185_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_185_QAZF100001.get("setai_daichou_bangou", ""))
        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_185_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("認可外申請検索 画面_185")
        
        # 186 認可外申請検索 画面:「検索」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_186_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code, tab_index=tab_index, col_number=1)
        self.click_button_by_label("1")
        
        # 187 世帯台帳 画面:「児童一覧」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100300" ,label="児童一覧")
        
        # 188 世帯台帳 画面: 対象児童の「No」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_188_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)
        self.screen_shot("利用者申請管理 画面_188")
        
        # 189 利用者申請管理 画面:「支払管理」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAPF221000_btnShiharaiKanri_button")
        self.screen_shot("補助金申請管理 画面_189")
        
        # 190 補助金申請管理 画面:「修正」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAPF222000_btnEditChg_button")
        
        # 191 補助金申請管理 画面: 請求年月日、納付額、利用日数、開所日数、支払方法、入園料年額、年度内在園月数、入園料月額、限度額、決定年月日、決定結果、却下理由、補助金額、振込年月日、戻入額、戻入日、備考を入力
        # 補助金申請管理
        # 請求年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtSeikyuYMD_textboxInput",
            value=case_data_191_QAPF222000.get("seikyu_nengappi", ""))
        # 納付額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNoufuGaku_textboxInput",
            value=case_data_191_QAPF222000.get("noufu_gaku", ""))
        # 利用日数
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoNissu_textboxInput",
            value=case_data_191_QAPF222000.get("riyou_nisu", ""))
        # 開所日数
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHrakiSyoNissu_textboxInput",
            value=case_data_191_QAPF222000.get("kaisho_nisu", ""))
        # 支払方法
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selSiharaiHouhou_select",
            text=case_data_191_QAPF222000.get("shiharai_houhou", ""))
        # 入園料年額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNuenryoNenGaku_textboxInput",
            value=case_data_191_QAPF222000.get("nyuen_ryo_nengaku", ""))
        # 年度内在園月数
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNedonaiZaenGetusu_textboxInput",
            value=case_data_191_QAPF222000.get("nendo_nai_zai_en_gatsu_suu", ""))
        # 入園料月額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtNuenryoGetuGaku_textboxInput",
            value=case_data_191_QAPF222000.get("nyuen_ryo_getugaku", ""))
        # 限度額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtGendoGaku_textboxInput",
            value=case_data_191_QAPF222000.get("gendo_gaku", ""))
        # 決定年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtKetteiYMD_textboxInput",
            value=case_data_191_QAPF222000.get("kettei_nengappi", ""))
        # 決定結果
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selKetteiKekka_select",
            text=case_data_191_QAPF222000.get("kettei_kekka", ""))
        # 却下理由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selKyakkaRiyuu_select",
            text=case_data_191_QAPF222000.get("kyakka_riyuu", ""))
        # 補助金額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojyoKingaku_textboxInput",
            value=case_data_191_QAPF222000.get("hojokin_gaku", ""))
        # 振込年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtFurikomiYMD_textboxInput",
            value=case_data_191_QAPF222000.get("furikomi_nengappi", ""))
        # 戻入額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtModouhairuGaku_textboxInput",
            value=case_data_191_QAPF222000.get("modoin_gaku", ""))
        # 戻入日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtModouhairuNi_textboxInput",
            value=case_data_191_QAPF222000.get("modoin_nengappi", ""))
        # 備考
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtBikou_textboxInput",
            value=case_data_191_QAPF222000.get("biko", ""))
        self.screen_shot("補助金申請管理 画面_191")
        
        # 192 補助金申請管理 画面:「登録」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAPF222000_regbtn_button")
        
        # 193 補助金申請管理 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF222000_msg_span",
                                 msg="更新しました。")
        self.screen_shot("補助金申請管理 画面_193")
        
        # 194 補助金申請管理 画面:「修正」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAPF222000_btnEditChg_button")
        
        # 195 補助金申請管理 画面:「削除」ボタン押下
        self.click_by_id(idstr="tab0" + str(tab_index) + "_QAPF222000_deletebtn_button")
        
        # 196 補助金申請管理 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF222000_msg_span",
                                 msg="削除しました。")
        self.screen_shot("補助金申請管理 画面_196")
        