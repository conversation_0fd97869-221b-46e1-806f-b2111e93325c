from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28110104(KodomoSiteTestCaseBase):
    """TestQAP010_28110104"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28110104(self):
        tab_index = 0
        case_data_225_QAZF100001 = self.test_data["225_QAZF100001"]
        case_data_226_QAZF100002 = self.test_data["226_QAZF100002"]
        case_data_228_QAPF100300 = self.test_data["228_QAPF100300"]
        case_data_229_QAPF222000 = self.test_data["229_QAPF222000"]
        case_data_231_QAPF222000 = self.test_data["231_QAPF222000"]

        # 222 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 223 メインメニュー 画面: メインメニューから「認可外申請管理」をクリック
        # 224 メインメニュー 画面: 「認可外申請検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="認可外申請管理", menu_level_2="認可外申請検索",
                                 is_new_tab=True)
        tab_index += 1

        # 225 認可外申請検索 画面: 検索条件を入力
        # 検索条件1 : 宛名検索
        # カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanaShimeiNM_textboxInput",
            value=case_data_225_QAZF100001.get("kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKana_select",
            text=case_data_225_QAZF100001.get("kana_shimei_aimai_kensaku", ""))
        # 漢字氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKanjiShimeiNM_textboxInput",
            value=case_data_225_QAZF100001.get("kanji_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiKanji_select",
            text=case_data_225_QAZF100001.get("kanji_shimei_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate01Left_textboxInput",
            value=case_data_225_QAZF100001.get("seinengappi1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDate02Left_textboxInput",
            value=case_data_225_QAZF100001.get("seinengappi2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selAimaiBirth1_select",
            text=case_data_225_QAZF100001.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selSeibetsu_select",
            text=case_data_225_QAZF100001.get("seibetsu", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouRightTxt_textboxInput",
            value=case_data_225_QAZF100001.get("yuubin_bangou_ko", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_YubinbangouLeftTxt_textboxInput",
            value=case_data_225_QAZF100001.get("yuubin_bangou_oya", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtKatagaki_textboxInput",
            value=case_data_225_QAZF100001.get("housho", ""))
        # 住民コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtJuminCD_textboxInput",
            value=case_data_225_QAZF100001.get("juumin_koudo", ""))
        # 世帯コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiCD_textboxInput",
            value=case_data_225_QAZF100001.get("setai_koudo", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_selWrGyoseiku_select",
            text=case_data_225_QAZF100001.get("shokanku", ""))

        # 検索条件2 : 世帯台帳番号検索
        # 世帯台帳番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtSetaiDaichoBango_textboxInput",
            value=case_data_225_QAZF100001.get("setai_daichou_bangou", ""))

        # 検索条件3 : 電話番号検索
        # 電話番号１
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAZF100001_txtDenwaBangouKensakuTEL1_textboxInput",
            value=case_data_225_QAZF100001.get("denwa_bangou_ichi", ""))
        self.screen_shot("検索条件を入力 画面_225")

        # 226 認可外申請検索 画面: 「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAZF100001_WrCmnBtn05_button")

        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAZF100002_pages1"):
            atena_code = case_data_226_QAZF100002.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code=atena_code, tab_index=tab_index, col_number=1)

        self.click_button_by_label("1")

        # 227 世帯台帳 画面: 「児童一覧」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF100300", label="児童一覧")

        # 228 世帯台帳 画面: 対象児童の「No」ボタン押下
        self.click_kodomo_by_atena_code(kodomo_atena_code=case_data_228_QAPF100300.get("kodomo_atena_code", ""),
                                        tab_index=tab_index)
        self.screen_shot("利用者申請管理 画面_228")

        # 229 利用者申請管理 画面: 「支払管理」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF221000_btnShiharaiKanri_button")
        self.screen_shot("補助金申請管理 画面_229")
        # 年度
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF222000_selKensakuJokenNendo_select",
            text=case_data_229_QAPF222000.get("nen_do_cmb", ""))
        # 補助請求一覧
        self.click_hojo_seikyuu_ichiran(input_params=case_data_229_QAPF222000.get("seikyuu_bangou", ""))

        # 230 補助金申請管理 画面: 「強制修正」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF222000_btnForcedEdit_button")

        # 231 補助金申請管理 画面: 戻入額、戻入日を入力
        # 戻入額
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtModouhairuGaku_textboxInput",
            value=case_data_231_QAPF222000.get("modonyu_gaku", ""))
        # 戻入日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtModouhairuNi_textboxInput",
            value=case_data_231_QAPF222000.get("modonyu_bi_ymd", ""))
        self.screen_shot("補助金申請管理 画面_231")

        # 232 補助金申請管理 画面: 「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF222000_regbtn_button")

        # 233 補助金申請管理 画面: 「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF222000_msg_span", msg="更新しました。")
        self.screen_shot("補助金申請管理 画面_233")

    def click_hojo_seikyuu_ichiran(self, input_params, tab_index=1):
        tr_elem = self.find_elements_by_css_selector(
            "#tab0" + str(tab_index) + "_ZZZ000000_tblHojoSeikyuYitiran_table > tbody > tr"
        )
        for index, tr_index in enumerate(range(0, len(tr_elem), 2)):
            if index == 0:
                continue
            css_selector = (
                    "#tab0" + str(tab_index) +
                    "_ZZZ000000_lbltSeikyuNo1_" + str(index) + "_3 > span"
            )
            seikyuu_bangou = self.find_element_by_css_selector(css_selector)
            seikyuu_bangou_text = seikyuu_bangou.text.strip()
            if input_params == seikyuu_bangou_text:
                self.click_by_id("tab0" + str(tab_index) + "_ZZZ000000_btnSeikyuYitiranNo_" + str(index) + "_1_button")
                break
