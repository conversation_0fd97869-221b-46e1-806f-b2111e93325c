from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28020201(KodomoSiteTestCaseBase):
    """TestQAP010_28020201"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28020201(self):
        tab_index = 0
        case_data_006_QAPF201600 = self.test_data["006_QAPF201600"]
        case_data_007_QAPF201700 = self.test_data["007_QAPF201700"]
        case_data_010_QAPF200400 = self.test_data["010_QAPF200400"]
        case_data_016_ZEAF000400 = self.test_data["016_ZEAF000400"]
        case_data_018_ZEAF000400 = self.test_data["018_ZEAF000400"]
        case_data_019_ZEAF002200 = self.test_data["019_ZEAF002200"]
        case_data_029_ZEAF000400 = self.test_data["029_ZEAF000400"]
        case_data_031_ZEAF000400 = self.test_data["031_ZEAF000400"]
        case_data_032_ZEAF002200 = self.test_data["032_ZEAF002200"]
        case_data_042_ZEAF000400 = self.test_data["042_ZEAF000400"]
        case_data_045_ZEAF002800 = self.test_data["045_ZEAF002800"]
        case_data_051_ZEAF002200 = self.test_data["051_ZEAF002200"]
        case_data_055_ZEAF000400 = self.test_data["055_ZEAF000400"]

        # 1, 2 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 3 メインメニュー 画面: メインメニューから「子ども子育て支援」クリック
        # 4 メインメニュー 画面:「施設管理」クリック
        # 5 メインメニュー 画面:「施設検索」ダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="施設管理", menu_level_3="施設検索",
                                 is_new_tab=True)
        tab_index += 1

        # 6 施設検索 画面: 検索条件を入力
        # 検索条件1
        # 施設コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput",
            value=case_data_006_QAPF201600.get("shisetsu_koodo", ""))

        # 事業者番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuJigyoshaNo_textboxInput",
            value=case_data_006_QAPF201600.get("jigyousha_bangou", ""))
        # 法人等カナ名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuHojinKanaNM_textboxInput",
            value=case_data_006_QAPF201600.get("houjin_tou_kana_meishou", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_selShisetsuKensakuHojinKanaNMOption_select",
            text=case_data_006_QAPF201600.get("houjin_tou_kana_meishou_cbx", ""))
        # 事業所番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuJigyoshoNo_textboxInput",
            value=case_data_006_QAPF201600.get("jigyousho_bangou", ""))
        # 施設カナ名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuShisetsuKanaNM_textboxInput",
            value=case_data_006_QAPF201600.get("shisetsu_kana_meishou", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_selShisetsuKensakuShisetsuKanaNMOption_select",
            text=case_data_006_QAPF201600.get("shisetsu_kana_meishou_cbx", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuPostNoOya_textboxInput",
            value=case_data_006_QAPF201600.get("yuubin_bangou_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuPostNoKo_textboxInput",
            value=case_data_006_QAPF201600.get("yuubin_bangou_2", ""))
        # 住所
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuJusho_textboxInput",
            value=case_data_006_QAPF201600.get("juusho", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_selShisetsuKensakuJushoOption_select",
            text=case_data_006_QAPF201600.get("juusho_cbx", ""))
        # 電話番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuTEL_textboxInput",
            value=case_data_006_QAPF201600.get("denwa_bangou", ""))
        # 施設種類
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_selShisetsuKensakuShisetsuShurui_select",
            text=case_data_006_QAPF201600.get("shisetsu_shurui_cbx", ""))
        # 所在区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_selWrGyoseiku_ShisetsuKensakuShozaiKu_select",
            text=case_data_006_QAPF201600.get("shozai_ku_cbx", ""))
        # 並び替え順序
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_sort_sel_0_select",
            text=case_data_006_QAPF201600.get("ninka_kubun_cbx", ""))
        self.screen_shot("施設検索_画面_06")

        # 7 施設検索 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF201600_WrCmnBtn05_button")
        #  該当施設一覧 画面: 支給認定情報を登録する世帯台帳履歴の「№」ボタン押下
        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAPF201700_pages1"):
            atena_code = case_data_007_QAPF201700.get("atena_code", "")
            self.click_gaitou_shisetsu_by_shisetsu_koudo(atena_code, tab_index)

        # 8 施設事業所情報 画面:「修正」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF200400_btnEditChg_button")

        # 9 施設事業所情報 画面:「利用定員」タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF200400", label="利用定員")

        # 10 施設事業所情報 画面: 利用定員情報を入力します
        # 対象クラス年齢
        # １号認定
        # 利用定員
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin0Saiji_1_2_textboxInput",
            value=case_data_010_QAPF200400.get("ichigou_nintei_riyou_teiin_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin1Saiji_1_3_textboxInput",
            value=case_data_010_QAPF200400.get("ichigou_nintei_riyou_teiin_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin2Saiji_1_4_textboxInput",
            value=case_data_010_QAPF200400.get("ichigou_nintei_riyou_teiin_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin3Saiji_1_5_textboxInput",
            value=case_data_010_QAPF200400.get("ichigou_nintei_riyou_teiin_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin4Saiji_1_6_textboxInput",
            value=case_data_010_QAPF200400.get("ichigou_nintei_riyou_teiin_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin5Saiji_1_7_textboxInput",
            value=case_data_010_QAPF200400.get("ichigou_nintei_riyou_teiin_5", ""))
        # 適用定員区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei0Saiji_1_8_select",
            text=case_data_010_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei1Saiji_1_9_select",
            text=case_data_010_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei2Saiji_1_10_select",
            text=case_data_010_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei3Saiji_1_11_select",
            text=case_data_010_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei4Saiji_1_12_select",
            text=case_data_010_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei5Saiji_1_13_select",
            text=case_data_010_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_5", ""))
        # ２・３号認定
        # 利用定員
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin0Saiji_2_2_textboxInput",
            value=case_data_010_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin1Saiji_2_3_textboxInput",
            value=case_data_010_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin2Saiji_2_4_textboxInput",
            value=case_data_010_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin3Saiji_2_5_textboxInput",
            value=case_data_010_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin4Saiji_2_6_textboxInput",
            value=case_data_010_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin5Saiji_2_7_textboxInput",
            value=case_data_010_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_5", ""))
        # 適用定員区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei0Saiji_2_8_select",
            text=case_data_010_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei1Saiji_2_9_select",
            text=case_data_010_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei2Saiji_2_10_select",
            text=case_data_010_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei3Saiji_2_11_select",
            text=case_data_010_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei4Saiji_2_12_select",
            text=case_data_010_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei5Saiji_2_13_select",
            text=case_data_010_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_5", ""))
        # うち短時間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime0Saiji_textboxInput",
            value=case_data_010_QAPF200400.get("uchi_tanjikan_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime1Saiji_textboxInput",
            value=case_data_010_QAPF200400.get("uchi_tanjikan_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime2Saiji_textboxInput",
            value=case_data_010_QAPF200400.get("uchi_tanjikan_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime3Saiji_textboxInput",
            value=case_data_010_QAPF200400.get("uchi_tanjikan_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime4Saiji_textboxInput",
            value=case_data_010_QAPF200400.get("uchi_tanjikan_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime5Saiji_textboxInput",
            value=case_data_010_QAPF200400.get("uchi_tanjikan_5", ""))
        # 対象児童
        # 産休枠
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin0Saiji_1_2_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_sankyuu_waku_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin1Saiji_1_3_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_sankyuu_waku_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin2Saiji_1_4_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_sankyuu_waku_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin3Saiji_1_5_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_sankyuu_waku_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin4Saiji_1_6_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_sankyuu_waku_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin5Saiji_1_7_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_sankyuu_waku_5", ""))
        # 障がい枠
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin0Saiji_2_2_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_shougai_waku_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin1Saiji_2_3_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_shougai_waku_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin2Saiji_2_4_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_shougai_waku_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin3Saiji_2_5_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_shougai_waku_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin4Saiji_2_6_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_shougai_waku_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin5Saiji_2_7_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_shougai_waku_5", ""))
        # 特別枠
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin0Saiji_3_2_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_tokubetsu_waku_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin1Saiji_3_3_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_tokubetsu_waku_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin2Saiji_3_4_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_tokubetsu_waku_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin3Saiji_3_5_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_tokubetsu_waku_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin4Saiji_3_6_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_tokubetsu_waku_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin5Saiji_3_7_textboxInput",
            value=case_data_010_QAPF200400.get("taishou_jidou_tokubetsu_waku_5", ""))
        self.screen_shot("施設事業所情報_画面_10")

        # 11 施設事業所情報 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF200400_regbtn_button")

        # 12 施設事業所情報 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF200400_msg_span",
                                 msg="更新しました。")
        self.screen_shot("施設事業所情報_画面_12")

        # 13 バッチ管理 画面: メインメニューから「バッチ管理」クリック
        # 14 バッチ管理 画面:「即時実行」クリック
        # 15 バッチ管理 画面:「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加", is_new_tab=False)
        tab_index += 1

        # 16 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：選考 <処理名>：入所自動選考データ作成処理
        # 17 スケジュール個別追加 画面:「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_016_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_016_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_016_ZEAF000400.get("shori_mei_cmb", ""), tab_index=(
                tab_index))
        self.screen_shot("スケジュール個別追加_画面_17")

        # 18 スケジュール個別追加 画面:「入所自動選考データ作成処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_018_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)

        # 19 実行指示 画面: パラメータを入力
        params = [
            {"title": "処理区分", "type": "select", "value": case_data_019_ZEAF002200.get("shori_kubun_cbx", "")},
            {"title": "申請有効期限日", "type": "text",
             "value": case_data_019_ZEAF002200.get("shinsei_yuukou_kigenbi_ymd", "")},
            {"title": "選考データ名", "type": "text",
             "value": case_data_019_ZEAF002200.get("senkou_deeta_mei", "")},
            {"title": "選考枠", "type": "select", "value": case_data_019_ZEAF002200.get("senkou_waku_cbx", "")},
            {"title": "入所申込日開始", "type": "text",
             "value": case_data_019_ZEAF002200.get("nyuusho_moushikomi_bi_kaishi_ymd", "")},
            {"title": "入所申込日終了", "type": "text",
             "value": case_data_019_ZEAF002200.get("nyuusho_moushikomi_bi_shuuryou_ymd", "")},
            {"title": "入所予定日", "type": "text",
             "value": case_data_019_ZEAF002200.get("nyuusho_yoteibi_ymd", "")},
            {"title": "基準日", "type": "text", "value": case_data_019_ZEAF002200.get("kijun_bi_ymd", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_19")

        # 20 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 21 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_21")

        # 22 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 23 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認_画面_23")

        # 24 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 25 ファイルダウンロード 画面:「No1」ボタン押下
        # 26 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 27 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 28 納品物管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_28")

        # 29 スケジュール個別追加 画面: <業務名>：子ども子育て支援 <サブシステム名>：選考 <処理名>：入所自動選考空き人数反映処理
        # 30 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_029_ZEAF000400.get("gyoumu_cmb", ""),
                                        subSystemNM=case_data_029_ZEAF000400.get("sabushisutemu_mei_cmb", ""),
                                        shoriNM=case_data_029_ZEAF000400.get("shori_mei_cmb", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加_画面_30")

        # 31 スケジュール個別追加 画面:「空き人数反映用CSV作成」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_031_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)
        # 32 実行指示 画面: パラメータを入力
        params = [
            {"title": "選考管理番号", "type": "select",
             "value": case_data_032_ZEAF002200.get("senkou_kanri_bango_cbx", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_32")

        # 33 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 34 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_34")

        # 35 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 36 納品物管理 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認_画面_36")

        # 37 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 38 ファイルダウンロード 画面:「No1」ボタン押下
        # 39 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 40 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)

        # 41 スケジュール個別追加 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_41")

        # 42 スケジュール個別追加 画面:「空き人数反映処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_042_ZEAF000400.get("batch_job_003", ""),
                                             tab_index=tab_index)

        # 43 実行指示 画面:「受理物登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002200_BtnJuributsu_button")
        tab_index += 1
        self.screen_shot("受理物登録_画面_43")

        # 44 受理物登録 画面:「アップロード」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002800_BtnUpload_1_3_button")

        # 45 受理物登録 画面:「ファイルの選択」ボタン押下
        # 46 受理物登録 画面:「追加」ボタン押下
        # 47 受理物登録 画面:「送信」ボタン押下
        self.upload_file_by_path(file_path=case_data_045_ZEAF002800.get("upload_file_path", ""),
                                 screen_shot_name="受理物登録_アップロード 画面_46")

        # 48 受理物登録 画面:「件数表示」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002800_BtnKensuHyoji_button")
        self.screen_shot("受理物登録_画面_48")

        # 49 受理物登録 画面: 登録実行
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002800_BtnTourokuJico_button")
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_ZEAF002800_msg_span",
                                 msg="登録しました。")
        self.screen_shot("受理物登録_画面_49")

        # 50 受理物登録 画面:「受理物登録」タブを削除
        self.find_element_by_xpath("//*[@id='0" + str(tab_index) + "']/span[2]").click()
        tab_index -= 1

        # 51 実行指示 画面: パラメータを入力
        params = [
            {"title": "更新空き人数(変更分のみ)", "type": "select",
             "value": case_data_051_ZEAF002200.get("koushin_aki_ninzuu_henkou_bun", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_51")

        # 52 実行指示 画面:「実行」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002200_executebtn_button")

        # 53 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_53")

        # 54 納品物管理 画面: パンくず「スケジュール個別追加」押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002300", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_54")

        # 55 スケジュール個別追加 画面:「空き人数反映処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_055_ZEAF000400.get("batch_job_004", ""),
                                             tab_index=tab_index)

        # 56 実行指示 画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 57 実行管理 画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_57")

        # 58 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 59 結果確認 画面:「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物確認_画面_59")

        # 60 納品物管理 画面: 納品物の「ダウンロード」ボタン押下
        # 61 ファイルダウンロード 画面:「No1」ボタン押下
        # 62 ファイルダウンロード 画面:「ファイルを開く(O)」ボタン押下
        # 63 納品物管理 画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, tab_index=tab_index)
