from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070201(KodomoSiteTestCaseBase):
    """TestQAP010_28070201"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070201(self):
        tab_index = 0
        case_data_006_QAPF105800 = self.test_data["006_QAPF105800"]
        case_data_007_QAPF100100 = self.test_data["007_QAPF100100"]
        case_data_009_FADF100300 = self.test_data["009_FADF100300"]
        case_data_010_FADF100300 = self.test_data["010_FADF100300"]
        case_data_013_FADF100400 = self.test_data["013_FADF100400"]
        case_data_021_ZEAF002800 = self.test_data["021_ZEAF002800"]

        # 1, 2 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 3 メインメニュー 画面: メインメニューから「子ども子育て支援」をクリックする
        # 4 メインメニュー 画面: 「入所管理」をクリック 
        # 5 メインメニュー 画面: 「児童検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="入所管理", menu_level_3="児童検索",
                                 is_new_tab=True)
        tab_index += 1

        # 6 児童検索 画面: 検索条件を入力
        # 検索条件1:
        # 保護者カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuHogoshaKanaShimei_textboxInput",
            value=case_data_006_QAPF105800.get("hogosha_kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuHogoshaKanaShimeiOption_select",
            text=case_data_006_QAPF105800.get("hogosha_kana_shimei_cbx", ""))
        # 保護者氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuHogoshaKanjiShimei_textboxInput",
            value=case_data_006_QAPF105800.get("hogosha_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuHogoshaKanjiShimeiOption_select",
            text=case_data_006_QAPF105800.get("hogosha_shimei_cbx", ""))
        # 児童カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuJidoKanaShimei_textboxInput",
            value=case_data_006_QAPF105800.get("jidou_kana_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuJidoKanaShimeiOption_select",
            text=case_data_006_QAPF105800.get("jidou_kana_shimei_cbx", ""))
        # 児童氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuJidoKanjiShimei_textboxInput",
            value=case_data_006_QAPF105800.get("jidou_shimei", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuJidoKanjiShimeiOption_select",
            text=case_data_006_QAPF105800.get("jidou_shimei_cbx", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuSeinengappiStart_textboxInput",
            value=case_data_006_QAPF105800.get("seinengappi_ymd_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selAimaiBirth1_select",
            text=case_data_006_QAPF105800.get("seinengappi_cbx", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuSeinengappiEnd_textboxInput",
            value=case_data_006_QAPF105800.get("seinengappi_ymd_to", ""))
        # 性別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuSeibetsu_select",
            text=case_data_006_QAPF105800.get("seibetsu_cbx", ""))
        # 宛名コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuAtenaCD_textboxInput",
            value=case_data_006_QAPF105800.get("atena_code", ""))
        # 状態区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selJidoKensakuJotaiKbn_select",
            text=case_data_006_QAPF105800.get("joutai_kubun_cbx", ""))
        # 所管区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selWrGyoseiku_JidoKensakuShokanKu_select",
            text=case_data_006_QAPF105800.get("shokanku_cbx", ""))
        # 入所番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuNyushoNo_textboxInput",
            value=case_data_006_QAPF105800.get("nyuusho_bangou", ""))
        # 入所申込番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtJidoKensakuMoushikomiNo_textboxInput",
            value=case_data_006_QAPF105800.get("nyuusho_moushikomi_bangou", ""))

        # 検索条件2:
        # 施設コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtShisetsuKensakuJidoKensakuShisetsuCD_textboxInput",
            value=case_data_006_QAPF105800.get("shisetsu_code", ""))
        # 施設カナ名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtShisetsuKensakuJidoKensakuShisetsuKanaNM_textboxInput",
            value=case_data_006_QAPF105800.get("shisetsu_kana_meishou", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selShisetsuKensakuJidoKensakuShisetsuKanaNMOption_select",
            text=case_data_006_QAPF105800.get("shisetsu_kana_meishou_cbx", ""))
        # 施設種類
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selShisetsuKensakuJidoKensakuShisetsuShurui_select",
            text=case_data_006_QAPF105800.get("shisetsu_shurui_cbx", ""))
        # 所在区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selWrGyoseiku_ShisetsuKensakuJidoKensakuShozaiKu_select",
            text=case_data_006_QAPF105800.get("shozai_ku_cbx", ""))

        # 検索条件3:
        # 認定者番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtNinteiINFOKensakuNinteiBango_textboxInput",
            value=case_data_006_QAPF105800.get("ninteisha_bangou", ""))
        # 認定区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuNinteiKbn_select",
            text=case_data_006_QAPF105800.get("nintei_kubun_cbx", ""))
        # 必要性事由
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuHitsuyoseiJiyu_select",
            text=case_data_006_QAPF105800.get("hitsuyousei_jiyuu_cbx", ""))
        # 保育必要量
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuHoikuHitsuyoryo_select",
            text=case_data_006_QAPF105800.get("hoiku_hitsuyou_ryou_cbx", ""))
        # 優先利用
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuYusenRiyo_select",
            text=case_data_006_QAPF105800.get("yuusen_riyou_cbx", ""))
        # 決定年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuKetteiDay_select",
            text=case_data_006_QAPF105800.get("kettei_nengappi_cbx", ""))
        # 負担区分決定年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNinteiINFOKensakuFutanKbnKetteiDay_select",
            text=case_data_006_QAPF105800.get("futan_kubun_kettei_nengappi_cbx", ""))

        # 検索条件4:
        # 年度
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNendoKensakuNendo_select",
            text=case_data_006_QAPF105800.get("nendo_cbx", ""))
        # クラス年齢
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_selNendoKensakuClassAge_select",
            text=case_data_006_QAPF105800.get("class_nerai_cbx", ""))
        # 入所番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF105800_txtNendoKensakuNyushoNo_textboxInput",
            value=case_data_006_QAPF105800.get("nyuusho_bangou_2", ""))

        self.screen_shot("児童検索 画面_6")

        # 7 児童検索 画面: 「検索」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF105800_WrCmnBtn05_button")

        if self.has_elements_by_css_selector("#tab0" + str(tab_index) + "_QAPF100100_pages1"):
            atena_code = case_data_007_QAPF100100.get("atena_code", "")
            self.select_atena_code_after_relative_search(atena_code=atena_code, tab_index=tab_index, col_number=7)
        self.screen_shot("児童検索 画面_7")

        # 8 児童台帳 画面: 「口座情報」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_QAPF103500_btnKozaInfo_button")

        # 9 口座照会 画面: 業務名を「子ども」に変更します。
        # 業務名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100300_selGyomuNM_select",
            text=case_data_009_FADF100300.get("gyoumu_mei_cbx", ""))

        # 10 口座照会 画面: 用途を「共通」に変更します。
        # 用途
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100300_selYoto_select",
            text=case_data_010_FADF100300.get("youto_cbx", ""))
        self.screen_shot("口座照会 画面_10")

        # 11 口座管理 画面: 「選択」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_FADF100300_btnSentaku_button")

        # 12 口座管理 画面: 「追加」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_FADF100400_btnAddChg_button")

        # 13 口座管理 画面: "金融機関CD：支店CD： 預金科目：口座番号：口座名義人管カナ：有効期間（開始）：を入力"
        # 異動日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtIDobi_textboxInput",
            value=case_data_013_FADF100400.get("idoubi_ymd", ""))
        # 金融機関種別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selKinyukikanSB_select",
            text=case_data_013_FADF100400.get("kinyuukikan_shubetsu_cbx", ""))
        # 金融機関名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtGinkoCD_textboxInput",
            value=case_data_013_FADF100400.get("kinyuukikan_mei_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtShitenCD_textboxInput",
            value=case_data_013_FADF100400.get("kinyuukikan_mei_2", ""))
        # 預金科目
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selYokinKamoku_select",
            text=case_data_013_FADF100400.get("yokin_kamoku_cbx", ""))
        # 預金者電話番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtTEL_textboxInput",
            value=case_data_013_FADF100400.get("yokinjya_denwa_bangou", ""))
        # 有効期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtYukoKikanFrom_textboxInput",
            value=case_data_013_FADF100400.get("yuukou_kikan_from_ymd", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtYukoKikanTo_textboxInput",
            value=case_data_013_FADF100400.get("yuukou_kikan_to_ymd", ""))
        # 納付区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selZennoKbn_select",
            text=case_data_013_FADF100400.get("noufu_kubun_cbx", ""))
        # 送金方法
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selSoukinKbn_select",
            text=case_data_013_FADF100400.get("soukin_houhou_cbx", ""))
        # 口座番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaNo_textboxInput",
            value=case_data_013_FADF100400.get("kouza_bangou", ""))
        # 口座申込日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMoshikomibi_textboxInput",
            value=case_data_013_FADF100400.get("kouza_moushikomi_bi_ymd", ""))
        # 口座停止期間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaTeishiKikanFrom_textboxInput",
            value=case_data_013_FADF100400.get("kouza_teishi_kikan_from_ymd", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaTeishiKikanTo_textboxInput",
            value=case_data_013_FADF100400.get("kouza_teishi_kikan_to_ymd", ""))
        # 公開 / 非公開
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_selKokaiHikokai_select",
            text=case_data_013_FADF100400.get("koukai_hikoukai", ""))
        # 口座名義人カナ
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMeigiKanaShimei_textboxInput",
            value=case_data_013_FADF100400.get("kouza_meiginin_kana", ""))
        # 口座名義人漢字
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_FADF100400_txtKozaMeigiKanjiShimeiNM_textboxInput",
            value=case_data_013_FADF100400.get("kouza_meiginin_kanji", ""))

        self.screen_shot("口座管理 画面_13")

        # 14 口座管理 画面: 「入力チェック」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_FADF100400_checkbtn_button")
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_FADF100400_msg_span",
                                 msg="入力チェックが完了しました。")

        # 15 口座管理 画面: 「登録」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_FADF100400_regbtn_button")

        # 16 口座管理 画面: 「はい」ボタンをクリック
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_FADF100400_msg_span",
                                 msg="登録しました。")
        self.screen_shot("口座管理 画面_16")

        # 17 メインメニュー 画面: メインメニューから「収納」をクリックする 
        # 18 メインメニュー 画面: メインメニューから「収納消込処理」をクリックする 
        # 19 メインメニュー 画面: メインメニュー「口座取込」をダブルクリックする
        self._goto_menu_by_label(menu_level_1="収納", menu_level_2="収納消込処理", menu_level_3="口座取込",
                                 is_new_tab=False)
        tab_index += 1
        self.screen_shot("メインメニュー 画面_19")

        # 20 口座取込 画面: 「読込」ボタンをクリックする
        self.click_by_id("tab0" + str(tab_index) + "_JAAF301200_btnYomikomi_button")

        # 21 口座取込 画面: 「ファイルの選択」をクリック
        # 22 口座取込 画面: 「追加」をクリック 
        # 23 口座取込 画面: 「送信」をクリック
        self.upload_file_by_path(file_path=case_data_021_ZEAF002800.get("upload_file_path", ""),
                                 screen_shot_name="口座取込_アップロード 画面_23")
        self.screen_shot("口座取込 画面_23")

        # 24 口座取込 画面: 「ファイル出力」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_JAAF301200_btnFILEOutput_button")

        # 25 口座取込 画面: 「登録」ボタンをクリック
        self.click_by_id("tab0" + str(tab_index) + "_JAAF301200_regbtn_button")
        self.screen_shot("口座取込 画面_25")
