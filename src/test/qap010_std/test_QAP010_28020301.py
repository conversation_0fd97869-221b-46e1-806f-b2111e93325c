from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28020301(KodomoSiteTestCaseBase):
    """Test_QAP010_280203_01"""

    tabIndex = 1

    def setUp(self):
        super().setUp()

    # 選考対象施設の定員が登録できることを確認する。
    def test_QAP010_28020301(self):
        """定員の登録"""

        case_data_006_QAPF105800 = self.test_data.get(f"006_QAPF105800", {})
        case_data_013_QAPF103700 = self.test_data.get(f"013_QAPF103700", {})
        case_data_022_ZEAF002200 = self.test_data.get(f"022_ZEAF002200", {})
        case_data_019_ZEAF000400 = self.test_data.get(f"019_ZEAF000400", {})
        case_data_021_ZEAF000400 = self.test_data.get(f"021_ZEAF000400", {})

        # 1, 2 メインメニュー画面: 表示
        self.do_login_new_tab()
        

        # 3 メインメニュー画面:メインメニューから「子ども子育て支援」をクリック
        # 4 メインメニュー画面:「入所管理」をクリック
        # 5 メインメニュー画面:「児童検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援",
                                 menu_level_2="入所管理",
                                 menu_level_3="児童検索",
                                 is_new_tab=True)
        

        # 6 児童検索画面:「検索条件」を入力
        form_fields = {key: value for key, value in case_data_006_QAPF105800.items() if value is not None}
        self.fill_search_form(input_data=form_fields)
        self.auto_screen_shot(step_index=6)

        # 7 児童検索画面:「検索」ボタン押下
        self.click_by_id(f"tab{self.tabIndex:02d}_QAPF105800_WrCmnBtn05_button")
        

        # 8 児童台帳画面:「入所管理」タブ押下
        self.change_tab_by_label(tab_id=self.tabIndex, label="入所管理", screen_id="QAPF103500")
        self.auto_screen_shot(step_index=8)

        # 9 児童台帳画面:取下げしたい入所履歴を選択
        self.click_by_id(f"tab{self.tabIndex:02d}_ZZZ000000_radNyushokanriSentaku_1_2radio0")
        
        self.auto_screen_shot(step_index=9)

        # 10 児童台帳画面:「入所管理」ボタン押下
        self.click_by_id(f"tab{self.tabIndex:02d}_QAPF103500_btnNyushoKanri_button")
        
        self.auto_screen_shot(step_index=10)

        # 11 入所管理（申込）画面:「取下げ」タブ押下
        self.change_tab_by_label(tab_id=self.tabIndex, label="取下げ", screen_id="QAPF103700")

        # 12 入所管理（申込）画面:「修正（取下げ）」ボタン押下
        self.click_by_id(f"tab{self.tabIndex:02d}_QAPF103700_btnShuseiTorisage_button")
        

        # 13 入所管理（申込）画面:「取下げ年月日」「更新区分」「理由」を入力
        form_fields_QAPF103700 = {
            "txtTorisageYMD_textboxInput": case_data_013_QAPF103700.get("torishine_nengappi"),
            "selTorisageKoshinKbn_select": case_data_013_QAPF103700.get("torisage_koushin_kubun"),
            "selTorisageRiyu_select": case_data_013_QAPF103700.get("torisage_riyuu"),
            "txtTorisageBiko_textarea": case_data_013_QAPF103700.get("torisage_bikou")
        }
        form_fields_QAPF103700 = {k: v for k, v in form_fields_QAPF103700.items() if v is not None and v != ""}
        self.fill_form_from_data(f"tab{self.tabIndex:02d}_QAPF103700_", form_fields_QAPF103700)
        self.auto_screen_shot(step_index=13)

        # 14 入所管理（申込）画面:「登録」ボタン押下
        self.click_by_id(f"tab{self.tabIndex:02d}_QAPF103700_regbtn_button")
        

        # 15 入所管理（申込）画面:「はい」ボタン押下
        self.alert_accept()
        
        self.assert_message_area(msg_span_id=f"tab{self.tabIndex:02d}_QAPF103700_msg_span", msg="更新しました。")
        self.change_tab_by_label(tab_id=self.tabIndex, label="取下げ", screen_id="QAPF103700")
        self.auto_screen_shot(step_index=15)

        # 16 メインメニュー画面:メインメニューから「バッチ管理」をクリック
        # 17 メインメニュー画面:「即時実行」をクリック
        # 18 メインメニュー画面:「スケジュール個別管理」をダブルクリック

        self._goto_menu_by_label(menu_level_1="バッチ管理",
                                 menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=False)
        self.wait_page_loaded(wait_timeout=15)
        self.tabIndex += 1

        # 19 スケジュール個別追加画面:"業務名：子ども子育て支援
        # サブシステム名：入所
        # 処理名：申込取下者一覧出力処理
        # 選択"
        # 20 スケジュール個別追加画面:「検索」ボタン押下
        
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_019_ZEAF000400.get("gyoumu_mei"),
                                        subSystemNM=case_data_019_ZEAF000400.get("sabu_shisutemu_mei"),
                                        shoriNM=case_data_019_ZEAF000400.get("shori_mei"),
                                        tab_index=self.tabIndex,
                                        )
        
        self.auto_screen_shot(step_index=20)

        # 21 スケジュール個別追加画面:「申込取下者一覧表　データ抽出」の「NO」ボタン押下
        job_label = case_data_021_ZEAF000400.get("batch_job_001", "")
        self.click_batch_job_button_by_label(job_label=job_label, tab_index=self.tabIndex)
        
        self.auto_screen_shot(step_index=21)

        # 22 実行指示画面:パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_022_ZEAF002200.get("shokan_ku")},
            {"title": "基準日", "type": "text", "value": case_data_022_ZEAF002200.get("kijun_hi")},
            {"title": "取下日開始", "type": "text", "value": case_data_022_ZEAF002200.get("torisage_bi_start")},
            {"title": "取下日終了", "type": "text", "value": case_data_022_ZEAF002200.get("torisage_bi_end")},
            {"title": "入所形態", "type": "select", "value": case_data_022_ZEAF002200.get("nyuusho_keitai")},
            {"title": "実施区分", "type": "select", "value": case_data_022_ZEAF002200.get("jisshi_kubun")},
            {"title": "公私区分", "type": "select", "value": case_data_022_ZEAF002200.get("koushi_kubun")},
            {"title": "所在", "type": "select", "value": case_data_022_ZEAF002200.get("shozai")},
            {"title": "施設種類", "type": "select", "value": case_data_022_ZEAF002200.get("shisetsu_shurui")},
            {"title": "発行年月日", "type": "text", "value": case_data_022_ZEAF002200.get("hakkou_nengappi")},
            {"title": "並び順", "type": "select", "value": case_data_022_ZEAF002200.get("narabi_jun")},
            {"title": "申込区分", "type": "select", "value": case_data_022_ZEAF002200.get("moushikomi_kubun")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=self.tabIndex)
        self.auto_screen_shot(step_index=22)

        # 23 実行指示画面:「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=self.tabIndex)
        

        # 24 実行管理画面:「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=30, time_span_sec=3)
        
        self.auto_screen_shot(step_index=24)

        # 25 実行管理画面:正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")
        

        # 26 結果確認画面:「納品物確認」ボタン押下
        self.click_by_id(f"tab{self.tabIndex:02d}_ZEAF002400_BtnNohinbutsuKakunin_button")
        
        self.auto_screen_shot(step_index=26)

        # 27 納品物管理画面:納品物の「ダウンロード」ボタン押下
        # 28 ファイルダウンロード画面:「No1」ボタン押下
        # 29 ファイルダウンロード画面:「ファイルを開く(O)」ボタン押下
        # 30 ファイルダウンロード画面:「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime, case_name="028_ZEAF002700", tab_index=self.tabIndex)
        
        self.auto_screen_shot(step_index=28)

    def fill_search_form(self, input_data):
        # Map the input parameters to the actual form field IDs
        field_mappings = {
            # 保護者カナ氏名
            "hogousha_kana_shimei": f"tab{self.tabIndex:02d}_QAPF105800_txtJidoKensakuHogoshaKanaShimei_textboxInput",
            "hogousha_kana_shimei_opushon": f"tab{self.tabIndex:02d}_QAPF105800_selJidoKensakuHogoshaKanaShimeiOption_select",

            # 保護者氏名
            "hogousha_kanji_shimei": f"tab{self.tabIndex:02d}_QAPF105800_txtJidoKensakuHogoshaKanjiShimei_textboxInput",
            "hogousha_kanji_shimei_opushon": f"tab{self.tabIndex:02d}_QAPF105800_selJidoKensakuHogoshaKanjiShimeiOption_select",

            # 児童カナ氏名
            "jidou_kana_shimei": f"tab{self.tabIndex:02d}_QAPF105800_txtJidoKensakuJidoKanaShimei_textboxInput",
            "jidou_kana_shimei_opushon": f"tab{self.tabIndex:02d}_QAPF105800_selJidoKensakuJidoKanaShimeiOption_select",

            # 児童氏名
            "jidou_kanji_shimei": f"tab{self.tabIndex:02d}_QAPF105800_txtJidoKensakuJidoKanjiShimei_textboxInput",
            "jidou_kanji_shimei_opushon": f"tab{self.tabIndex:02d}_QAPF105800_selJidoKensakuJidoKanjiShimeiOption_select",

            # 生年月日
            "seinengappi_kaishi": f"tab{self.tabIndex:02d}_QAPF105800_txtJidoKensakuSeinengappiStart_textboxInput",
            "seinengappi_shuuryou": f"tab{self.tabIndex:02d}_QAPF105800_txtJidoKensakuSeinengappiEnd_textboxInput",
            "seinengappi_aimai_kensaku": f"tab{self.tabIndex:02d}_QAPF105800_selAimaiBirth1_select",

            # 性別
            "seibetsu": f"tab{self.tabIndex:02d}_QAPF105800_selJidoKensakuSeibetsu_select",

            # 宛名コード
            "atena_code": f"tab{self.tabIndex:02d}_QAPF105800_txtJidoKensakuAtenaCD_textboxInput",

            # 状態区分
            "joutai_kubun": f"tab{self.tabIndex:02d}_QAPF105800_selJidoKensakuJotaiKbn_select",

            # 所管区
            "shokan_ku": f"tab{self.tabIndex:02d}_QAPF105800_selWrGyoseiku_JidoKensakuShokanKu_select",

            # 支所
            "shisho": f"tab{self.tabIndex:02d}_QAPF105800_selWrShisyo_JidoKensakuShisho_select",

            # 入所番号
            "nyuusho_bangou": f"tab{self.tabIndex:02d}_QAPF105800_txtJidoKensakuNyushoNo_textboxInput",

            # 学区
            "gakku": f"tab{self.tabIndex:02d}_QAPF105800_selJidoKensakuGakku_select",

            # 入所申込番号
            "moushikomi_bangou": f"tab{self.tabIndex:02d}_QAPF105800_txtJidoKensakuMoushikomiNo_textboxInput",

            # 施設検索
            "shisetsu_koudo": f"tab{self.tabIndex:02d}_QAPF105800_txtShisetsuKensakuJidoKensakuShisetsuCD_textboxInput",
            "shisetsu_kana_meishou": f"tab{self.tabIndex:02d}_QAPF105800_txtShisetsuKensakuJidoKensakuShisetsuKanaNM_textboxInput",
            "shisetsu_kana_meishou_opushon": f"tab{self.tabIndex:02d}_QAPF105800_selShisetsuKensakuJidoKensakuShisetsuKanaNMOption_select",
            "shisetsu_shurui": f"tab{self.tabIndex:02d}_QAPF105800_selShisetsuKensakuJidoKensakuShisetsuShurui_select",
            "shozai_ku": f"tab{self.tabIndex:02d}_QAPF105800_selWrGyoseiku_ShisetsuKensakuJidoKensakuShozaiKu_select",
            "shozai_shisho": f"tab{self.tabIndex:02d}_QAPF105800_selWrShisyo_ShisetsuKensakuJidoKensakuShozaiShisho_select",

            # 認定情報検索
            "ninteisha_bangou": f"tab{self.tabIndex:02d}_QAPF105800_txtNinteiINFOKensakuNinteiBango_textboxInput",
            "nintei_kubun": f"tab{self.tabIndex:02d}_QAPF105800_selNinteiINFOKensakuNinteiKbn_select",
            "hitsuyousei_jiyuu": f"tab{self.tabIndex:02d}_QAPF105800_selNinteiINFOKensakuHitsuyoseiJiyu_select",
            "hoiku_hitsuyouryou": f"tab{self.tabIndex:02d}_QAPF105800_selNinteiINFOKensakuHoikuHitsuyoryo_select",
            "yuusen_riyou": f"tab{self.tabIndex:02d}_QAPF105800_selNinteiINFOKensakuYusenRiyo_select",
            "kettei_hi": f"tab{self.tabIndex:02d}_QAPF105800_selNinteiINFOKensakuKetteiDay_select",
            "futan_kubun": f"tab{self.tabIndex:02d}_QAPF105800_selNinteiINFOKensakuFutanKbnKetteiDay_select",

            # 年度検索
            "nendo": f"tab{self.tabIndex:02d}_QAPF105800_selNendoKensakuNendo_select",
            "kurasu_nenrei": f"tab{self.tabIndex:02d}_QAPF105800_selNendoKensakuClassAge_select",
            "nendo_nyuusho_bangou": f"tab{self.tabIndex:02d}_QAPF105800_txtNendoKensakuNyushoNo_textboxInput",
        }

        # Populate each field with its corresponding value
        for param_name, value in input_data.items():
            if param_name in field_mappings and value is not None:
                element_id = field_mappings[param_name]
                self.form_input_by_id(idstr=element_id, value=value, text=value)

    def fill_form_from_data(self, prefix, fields_data):
        """
        Fill form fields using a data dictionary

        Args:
            prefix: The prefix for all field IDs (like tab_screencode_)
            fields_data: Dictionary mapping field suffixes to their values
        """
        for field_suffix, value in fields_data.items():
            if value is not None:  # Only process fields with values
                self.form_input_by_id(idstr=f"{prefix}{field_suffix}", value=value, text=value)
