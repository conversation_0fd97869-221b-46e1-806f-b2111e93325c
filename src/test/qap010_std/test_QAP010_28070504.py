from base.kodomo_case import KodomoSiteTestCaseBase


class Test_QAP010_28070504(KodomoSiteTestCaseBase):
    """Test_QAP010_28070504"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070504(self):
        tab_index = 0
        case_data_165_JAAF400100 = self.test_data["165_JAAF400100"]
        case_data_167_JAAF403400 = self.test_data["167_JAAF403400"]
        case_data_170_JAPF000100 = self.test_data["170_JAPF000100"]

        # 161 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 162 メインメニュー 画面: メインメニューから「収納」クリック
        # 163 メインメニュー 画面:「個人別収納状況」クリック
        # 164 メインメニュー 画面:「通番明細」ダブルクリック
        self._goto_menu_by_label(menu_level_1="収納", menu_level_2="個人別収納状況", menu_level_3="通番明細",
                                 is_new_tab=True)
        tab_index += 1
        self.wait_page_loaded(wait_timeout=10)

        # 165 検索条件入力画面:検索条件を入力
        # カナ氏名・名称
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtKanaShimeiNM_textboxInput",
                              value=case_data_165_JAAF400100.get("kana_shimei_meisho", ""))
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_selAimaiKana_select",
                              text=case_data_165_JAAF400100.get("kana_shimei_meisho_aimai_kensaku", ""))
        # 漢字氏名・名称
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtKanjiShimeiNM_textboxInput",
                              value=case_data_165_JAAF400100.get("kanji_shimei_meisho", ""))
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_selAimaiKanji_select",
                              text=case_data_165_JAAF400100.get("kanji_shimei_meisho_aimai_kensaku", ""))
        # カナ氏名・名称２
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtKanaShimeiNM2_textboxInput",
                              value=case_data_165_JAAF400100.get("kana_shimei_meisho_2", ""))
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_selAimaiKana2_select",
                              text=case_data_165_JAAF400100.get("kana_shimei_meisho_2_aimai_kensaku", ""))
        # 漢字氏名・名称２
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtKanjiShimeiNM2_textboxInput",
                              value=case_data_165_JAAF400100.get("kanji_shimei_meisho_2", ""))
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_selAimaiKanji2_select",
                              text=case_data_165_JAAF400100.get("kanji_shimei_meisho_2_aimai_kensaku", ""))
        # 生年月日
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtDate01Left_textboxInput",
                              value=case_data_165_JAAF400100.get("seinengappi_1", ""))
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtDate02Left_textboxInput",
                              value=case_data_165_JAAF400100.get("seinengappi_2", ""))
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_selAimaiBirth1_select",
                              text=case_data_165_JAAF400100.get("seinengappi_aimai_kensaku", ""))
        # 性別
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_selSeibetsu_select",
                              text=case_data_165_JAAF400100.get("seibetsu", ""))
        # 行政区
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_selGyoseiku_select",
                              text=case_data_165_JAAF400100.get("gyosei_ku", ""))
        # 郵便番号
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_YubinbangouLeftTxt_textboxInput",
                              value=case_data_165_JAAF400100.get("yubin_bangou_oya", ""))
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_YubinbangouRightTxt_textboxInput",
                              value=case_data_165_JAAF400100.get("yubin_bangou_ko", ""))
        # 住所・所在地
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtJusho_textboxInput",
                              value=case_data_165_JAAF400100.get("jusho_shozaichi", ""))
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_selAimaiJusho_select",
                              text=case_data_165_JAAF400100.get("jusho_shozaichi_aimai_kensaku", ""))
        # 方書
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtKatagaki_textboxInput",
                              value=case_data_165_JAAF400100.get("katagaki", ""))
        # 住民コード
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtJuminCD_textboxInput",
                              value=case_data_165_JAAF400100.get("jumin_code", ""))
        # 世帯コード
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtSetaiCD_textboxInput",
                              value=case_data_165_JAAF400100.get("setai_code", ""))
        # 特徴指定番号
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtTokuchoShiteiNo_textboxInput",
                              value=case_data_165_JAAF400100.get("tokucho_shitei_no", ""))
        # 通知番号
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_txtTsuchiNo_textboxInput",
                              value=case_data_165_JAAF400100.get("tsuchi_no", ""))
        # 検索区分
        self.form_input_by_id(idstr=f"tab{tab_index:02d}_JAAF400100_selKensakuKbn_select",
                              text=case_data_165_JAAF400100.get("kensaku_kbn", ""))
        self.screen_shot("（個人・法人）検索条件入力 画面_165")

        # 166 検索条件入力画面:「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.screen_shot("通番明細 画面_166")

        # 167 通番明細画面:個別消込をする対象の「No」ボタン押下
        self.select_no_kobetsu_keshikomirow_by_tsuuchisho_bangou(
            tsuuchisho_bangou=case_data_167_JAAF403400.get("tsuuchisho_bangou", ""), tab_index=tab_index)
        self.screen_shot("収納状況照会 画面_167")

        # 168 収納状況照会画面:「印刷」ボタン押下
        self.click_button_by_label("印刷")
        self.screen_shot("（収納）印刷指示 画面_168")

        # 169 （収納）印刷指示画面:「納付納入証明書」にチェック
        # 170 （収納）印刷指示画面:パラメータを入力
        exec_params = [
            {
                "report_name": case_data_170_JAPF000100.get("chouhyou_mei_1", ""),
                "params_1": [
                    {"title": "発行場所", "value": case_data_170_JAPF000100.get("hakkou_basho_1", "")},
                    {"title": "トレイ", "value": case_data_170_JAPF000100.get("torei_1", "")},
                    {"title": "部数", "value": case_data_170_JAPF000100.get("busuu_1", "")}
                ],
                "params_2": [
                    {"title": "発行日", "value": case_data_170_JAPF000100.get("hakkoubi_1", "")}
                ]
            }
        ]
        checked_reports_count = self.select_kodomo_report(report_param_list=exec_params, tab_index=tab_index,
                                                          screen_shot_name="（収納）印刷指示 画面_170",
                                                          screen_id="JAPF000100")

        # 171 （収納）印刷指示画面:「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 172 確認ダイアログ画面:「印刷してよろしいですか」に「はい」ボタン押下
        self.alert_accept()

        # 173 ファイルダウンロード画面:「納付納入証明書」の「No1」ボタン押下
        # 174 帳票（PDF）画面:「ファイルを開く(O)」ボタン押下
        # 175 帳票（PDF）画面:「×」ボタン押下
        # 176 ファイルダウンロード画面:「閉じる」ボタン押下
        self.print_kodomo_reports(limit_wait_count=20, time_span_sec=4, tab_index=tab_index,
                                  screen_shot_name="ファイルダウンロード 画面_173",
                                  count_report=checked_reports_count)
