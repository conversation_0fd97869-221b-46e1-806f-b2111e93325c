from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28050102(KodomoSiteTestCaseBase):
    """TestQAP010_28050102"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28050102(self):
        tab_index = 0
        case_data_025_QAPF200100 = self.test_data["025_QAPF200100"]
        case_data_026_QAPF200100 = self.test_data["026_QAPF200100"]
        case_data_032_QAPF200200 = self.test_data["032_QAPF200200"]
        case_data_033_QAPF200200 = self.test_data["033_QAPF200200"]
        case_data_036_QAPF201000 = self.test_data["036_QAPF201000"]
        case_data_037_QAPF201100 = self.test_data["037_QAPF201100"]
        case_data_043_QAPF201600 = self.test_data["043_QAPF201600"]
        case_data_044_QAPF201700 = self.test_data["044_QAPF201700"]
        case_data_047_QAPF200400 = self.test_data["047_QAPF200400"]
        case_data_049_QAPF200400 = self.test_data["049_QAPF200400"]
        case_data_051_QAPF200400 = self.test_data["051_QAPF200400"]
        case_data_053_QAPF200400 = self.test_data["053_QAPF200400"]
        case_data_055_QAPF200400 = self.test_data["055_QAPF200400"]

        # 21 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 22 メインメニュー 画面: メインメニューから「子ども子育て支援」をクリック
        # 23 メインメニュー 画面:「施設事業者（認可事業者）」をクリック
        # 24 メインメニュー 画面:「追加」ボタン押下
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="施設事業者（認可事業者）",
                                 menu_level_3="追加",
                                 is_new_tab=True)
        tab_index += 1

        # 25 認可申請情報（事業者） 画面: 異動入力欄の情報を入力
        # 異動年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtIDoYMD_textboxInput",
            value=case_data_025_QAPF200100.get("idou_nengappi", ""))
        # 申請年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtShinseiYMD_textboxInput",
            value=case_data_025_QAPF200100.get("shinsei_nengappi", ""))
        # 決定年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtKetteiYMD_textboxInput",
            value=case_data_025_QAPF200100.get("kettei_nengappi", ""))
        # 審査結果
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selShinsaKekka_select",
            text=case_data_025_QAPF200100.get("shinsa_kekka", ""))
        self.screen_shot("認可申請情報（事業者） 画面_25")

        # 26 認可申請情報（事業者） 画面: 申請者欄の情報を入力
        # 法人等カナ名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojinKanaNM_textboxInput",
            value=case_data_026_QAPF200100.get("houjintou_kana_meishou", ""))
        # 事業者番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtJigyoshaNo_textboxInput",
            value=case_data_026_QAPF200100.get("jigyousha_bangou", ""))
        # 法人等名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojinNM_textboxInput",
            value=case_data_026_QAPF200100.get("houjintou_meishou", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojinPostNoOya_textboxInput",
            value=case_data_026_QAPF200100.get("hojin_yuubin_bangou_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojinPostNoKo_textboxInput",
            value=case_data_026_QAPF200100.get("hojin_yuubin_bangou_to", ""))
        # 直接入力
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_chkHojinChokusetsuInputchk0",
            value=case_data_026_QAPF200100.get("hojin_chokusetsu_nyuuryoku", ""))
        # 住所
        if case_data_026_QAPF200100.get("hojin_chokusetsu_nyuuryoku", "") == "1":
            self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojinJusho_textboxInput",
                value=case_data_026_QAPF200100.get("hojin_juusho", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojinKatagaki_textboxInput",
            value=case_data_026_QAPF200100.get("hojin_katagaki", ""))
        # 電話番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojinTEL_textboxInput",
            value=case_data_026_QAPF200100.get("hojin_denwa_bangou", ""))
        # FAX番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojinFaxNo_textboxInput",
            value=case_data_026_QAPF200100.get("hojin_fax_bangou", ""))
        # メールアドレス
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtHojinMailAddress_textboxInput",
            value=case_data_026_QAPF200100.get("hojin_meeru_adoresu", ""))
        # 法人等種別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selHojinShubetsu_select",
            text=case_data_026_QAPF200100.get("houjintou_shubetsu", ""))
        # 法人等所在地
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selHojinShozaichiTodofuken_select",
            text=case_data_026_QAPF200100.get("houjintou_shozaichi_todofuken", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selHojinShozaichiShikuchoson_select",
            text=case_data_026_QAPF200100.get("houjintou_shozaichi_shikuchoson", ""))
        # 設立年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtSetsuritsuYMD_textboxInput",
            value=case_data_026_QAPF200100.get("setsuritsu_nengappi", ""))
        # 代表者カナ氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtDaihyoshaKanaShimei_textboxInput",
            value=case_data_026_QAPF200100.get("daihyousha_kana_shimei", ""))
        # 代表者氏名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtDaihyoshaShimei_textboxInput",
            value=case_data_026_QAPF200100.get("daihyousha_shimei", ""))
        # 代表者職名
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtDaihyoshaShokuNM_textboxInput",
            value=case_data_026_QAPF200100.get("daihyousha_shokumei", ""))
        # 生年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtSeinengappi_textboxInput",
            value=case_data_026_QAPF200100.get("seinengappi", ""))
        # 就任年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtShuninYMD_textboxInput",
            value=case_data_026_QAPF200100.get("shuunin_nengappi", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtDaihyoshaPostNoOya_textboxInput",
            value=case_data_026_QAPF200100.get("daihyosha_yuubin_bangou_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtDaihyoshaPostNoKo_textboxInput",
            value=case_data_026_QAPF200100.get("daihyosha_yuubin_bangou_to", ""))
        # 直接入力
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_chkDaihyoshaChokusetsuInputchk0",
            value=case_data_026_QAPF200100.get("daihyosha_chokusetsu_nyuuryoku", ""))
        # 住所
        if case_data_026_QAPF200100.get("daihyosha_chokusetsu_nyuuryoku", "") == "1":
            self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_ZZZ000000_txtDaihyoshaJusho_textboxInput",
                value=case_data_026_QAPF200100.get("daihyosha_juusho", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtDaihyoshaKatagaki_textboxInput",
            value=case_data_026_QAPF200100.get("daihyosha_katagaki", ""))
        # 電話番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtDaihyoshaTEL_textboxInput",
            value=case_data_026_QAPF200100.get("daihyosha_denwa_bangou", ""))
        # FAX番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtDaihyoshaFaxNo_textboxInput",
            value=case_data_026_QAPF200100.get("daihyosha_fax_bangou", ""))
        # メールアドレス
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtDaihyoshaMailAddress_textboxInput",
            value=case_data_026_QAPF200100.get("daihyosha_meeru_adoresu", ""))
        self.screen_shot("認可申請情報（事業者） 画面_26")

        # 27 認可申請情報（事業者） 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF200100_regbtn_button")

        # 28 認可申請情報（事業者） 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF200100_msg_span", msg="登録しました。")
        self.screen_shot("認可申請情報（事業者）画面_28")

        # 29 メインメニュー 画面: メインメニューから「子ども子育て支援」をクリック
        # 30 メインメニュー 画面:「施設事業所（認可事業所）」をクリック
        # 31 メインメニュー 画面:「追加」ボタン押下
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="施設事業者（認可事業所）",
                                 menu_level_3="追加",
                                 is_new_tab=False)
        tab_index += 1

        # 32 認可申請情報（施設事業所） 画面: 異動入力欄の情報を入力
        # 異動年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtIDoYMD_textboxInput",
            value=case_data_032_QAPF200200.get("idou_nengappi", ""))
        # 申請年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtShinseiYMD_textboxInput",
            value=case_data_032_QAPF200200.get("shinsei_nengappi", ""))
        # 決定年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtKetteiYMD_textboxInput",
            value=case_data_032_QAPF200200.get("kettei_nengappi", ""))
        # 審査結果
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_selShinsaKekka_select",
            text=case_data_032_QAPF200200.get("shinsa_kekka", ""))
        self.screen_shot("認可申請情報（施設事業所）画面_32")

        # 33 認可申請情報（施設事業所） 画面: 事業所情報タブの情報を入力
        # 事業所情報
        # 施設コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtShisetsuCD_textboxInput",
            value=case_data_033_QAPF200200.get("shisetsu_koudo", ""))
        # 所在区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_selWrGyoseiku_JigyoshoShokanku_select",
            text=case_data_033_QAPF200200.get("shozaiku", ""))
        # 認可区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_selNinkaKbn_select",
            text=case_data_033_QAPF200200.get("ninka_kubun", ""))
        # 認可年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtNinkaYMD_textboxInput",
            value=case_data_033_QAPF200200.get("ninka_nengappi", ""))
        # 認定年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtNinteiYMD_textboxInput",
            value=case_data_033_QAPF200200.get("nintei_nengappi", ""))
        # 施設カナ名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtShisetsuKanaNM_textboxInput",
            value=case_data_033_QAPF200200.get("shisetsu_kana_meishou", ""))
        # 施設名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtShisetsuNM_textboxInput",
            value=case_data_033_QAPF200200.get("shisetsu_meishou", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtShisetsuPostNoOya_textboxInput",
            value=case_data_033_QAPF200200.get("yuubin_bangou_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtShisetsuPostNoKo_textboxInput",
            value=case_data_033_QAPF200200.get("yuubin_bangou_to", ""))
        # 直接入力
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_chkShisetsuChokusetsuInputchk0",
            value=case_data_033_QAPF200200.get("chokusetsu_nyuuryoku", ""))
        # 住所
        if case_data_033_QAPF200200.get("chokusetsu_nyuuryoku", "") == "1":
            self.form_input_by_id(
                idstr="tab0" + str(tab_index) + "_QAPF200200_txtShisetsuJusho_textboxInput",
                value=case_data_033_QAPF200200.get("juusho", ""))
        # 方書
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtShisetsuKatagaki_textboxInput",
            value=case_data_033_QAPF200200.get("katagaki", ""))
        # 電話番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtShisetsuTEL_textboxInput",
            value=case_data_033_QAPF200200.get("denwa_bangou", ""))
        # FAX番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtShisetsuFaxNo_textboxInput",
            value=case_data_033_QAPF200200.get("fax_bangou", ""))
        # メールアドレス
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtShisetsuMailAddress_textboxInput",
            value=case_data_033_QAPF200200.get("meeru_adoresu", ""))
        # 施設種類
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_selShisetsuJigyoshoMeisaiKbn_select",
            text=case_data_033_QAPF200200.get("shisetsu_shurui", ""))
        # 施設所在地
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_selShisetsuShozaichiTodofuken_select",
            text=case_data_033_QAPF200200.get("shisetsu_shozaichi_todofuken", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_selShisetsuShozaichiShikuchoson_select",
            text=case_data_033_QAPF200200.get("shisetsu_shozaichi_shikuchoson", ""))
        # 事業開始年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtJigyoStartYMD_textboxInput",
            value=case_data_033_QAPF200200.get("jigyou_kaishi_nengappi", ""))
        # 公表確定フラグ
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_selKouhyouKakutei_select",
            text=case_data_033_QAPF200200.get("kouhyou_kakutei_furagu", ""))
        # 認可定員
        # 認可定員
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtNinkaTein_textboxInput",
            value=case_data_033_QAPF200200.get("ninka_teiin", ""))
        # 地域枠
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtChikiwakuNinkaTein_textboxInput",
            value=case_data_033_QAPF200200.get("chiiki_waku", ""))
        # １号認定
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtNinkaTein1_textboxInput",
            value=case_data_033_QAPF200200.get("ichigou_nintei", ""))
        # ２号認定
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtNinkaTein2_textboxInput",
            value=case_data_033_QAPF200200.get("nigou_nintei", ""))
        # ３号認定
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_txtNinkaTein3_textboxInput",
            value=case_data_033_QAPF200200.get("sangou_nintei", ""))
        # 特例移行他
        # 適用有無
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_selTekiyoUmu_select",
            text=case_data_033_QAPF200200.get("tekiyou_umu", ""))
        # みなし確認適用の有無
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_selMinashiKakuninTekiyoUmu_select",
            text=case_data_033_QAPF200200.get("minashi_kakunin_tekiyou_no_umu", ""))
        # 適用の内容
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_chkTekiyoNaiyochk0",
            value=case_data_033_QAPF200200.get("tekiyou_no_naiyou_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_chkTekiyoNaiyochk1",
            value=case_data_033_QAPF200200.get("tekiyou_no_naiyou_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_chkTekiyoNaiyochk2",
            value=case_data_033_QAPF200200.get("tekiyou_no_naiyou_3", ""))
        # 連絡票
        # 送信停止
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200200_chkSoshinTeishichk0",
            value=case_data_033_QAPF200200.get("soushin_teishi", ""))
        self.screen_shot("認可申請情報（施設事業所）画面_33")

        # 34 認可申請情報（施設事業所） 画面: 事業者情報タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF200200", label="事業者情報")
        self.screen_shot("認可申請情報（施設事業所）画面_34")

        # 35 認可申請情報（施設事業所） 画面: 事業者番号の虫メガネボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF200200_btnJigyoshaKensaku_button")

        # 36 認可事業者検索 画面: 検索条件を入力
        # 事業者番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_txtNinkaJigyoshaKensakuJigyoshaNo_textboxInput",
            value=case_data_036_QAPF201000.get("jigyousha_bangou", ""))
        # 法人等カナ名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_txtNinkaJigyoshaKensakuHojinKanaNM_textboxInput",
            value=case_data_036_QAPF201000.get("houjintou_kana_meishou_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_selNinkaJigyoshaKensakuHojinKanaNMOption_select",
            text=case_data_036_QAPF201000.get("houjintou_kana_meishou_select", ""))
        # 代表者カナ名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_txtNinkaJigyoshaKensakuDaihyoshaKanaShimei_textboxInput",
            value=case_data_036_QAPF201000.get("daihyou_sha_kana_meishou_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_selNinkaJigyoshaKensakuDaihyoshaKanaShimeiOption_select",
            text=case_data_036_QAPF201000.get("daihyou_sha_kana_meishou_select", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_txtNinkaJigyoshaKensakuPostNoOya_textboxInput",
            value=case_data_036_QAPF201000.get("yuubin_bangou_start", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_txtNinkaJigyoshaKensakuPostNoKo_textboxInput",
            value=case_data_036_QAPF201000.get("yuubin_bangou_end", ""))
        # 住所
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_txtNinkaJigyoshaKensakuNinkaJigyoshaKensakuJusho_textboxInput",
            value=case_data_036_QAPF201000.get("juusho_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_selNinkaJigyoshaKensakuJushoOption_select",
            text=case_data_036_QAPF201000.get("juusho_select", ""))
        # 電話番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_txtNinkaJigyoshaKensakuTEL_textboxInput",
            value=case_data_036_QAPF201000.get("denwa_bangou", ""))
        # メールアドレス
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_txtNinkaJigyoshaKensakuMailAddress_textboxInput",
            value=case_data_036_QAPF201000.get("meeru_adoresu_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_selNinkaJigyoshaKensakuMailAddressOption_select",
            text=case_data_036_QAPF201000.get("meeru_adoresu_select", ""))
        # 法人等種別
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_selNinkaJigyoshaKensakuHojinShubetsu_select",
            text=case_data_036_QAPF201000.get("houjintou_shubetsu", ""))
        # 設立日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_txtNinkaJigyoshaKensakuSetsuritsubiStart_textboxInput",
            value=case_data_036_QAPF201000.get("setsuritsubi_start", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201000_txtNinkaJigyoshaKensakuSetsuritsubiEnd_textboxInput",
            value=case_data_036_QAPF201000.get("setsuritsubi_end", ""))
        self.screen_shot("認可事業者検索 画面_36")

        # 37 認可事業者検索 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF201000_WrCmnBtn05_button")
        self.click_ninka_jigyousha_by_jigyousha_bangou(
            jigyousha_bangou=case_data_037_QAPF201100.get("jigyousha_bangou", ""), tab_index=tab_index)
        self.screen_shot("認可申請情報（施設事業所）画面_37")

        # 38 認可申請情報（施設事業所） 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF200200_regbtn_button")

        # 39 認可申請情報（施設事業所） 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF200200_msg_span", msg="登録しました。")
        self.screen_shot("認可申請情報（施設事業所）画面_39")

        # 40 メインメニュー 画面: メインメニューから「子ども子育て支援」をクリック
        # 41 メインメニュー 画面:「施設管理」をクリック
        # 42 メインメニュー 画面:「施設検索」をダブルクリック
        self._goto_menu_by_label(menu_level_1="子ども子育て支援", menu_level_2="施設管理",
                                 menu_level_3="施設検索",
                                 is_new_tab=False)
        tab_index += 1

        # 43 施設検索 画面: 検索条件を入力
        # 施設コード
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuShisetsuCD_textboxInput",
            value=case_data_043_QAPF201600.get("shisetsu_koudo", ""))
        # 事業者番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuJigyoshaNo_textboxInput",
            value=case_data_043_QAPF201600.get("jigyousha_bangou", ""))
        # 法人等カナ名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuHojinKanaNM_textboxInput",
            value=case_data_043_QAPF201600.get("houjintou_kana_meishou_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_selShisetsuKensakuHojinKanaNMOption_select",
            text=case_data_043_QAPF201600.get("houjintou_kana_meishou_select", ""))
        # 事業所番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuJigyoshoNo_textboxInput",
            value=case_data_043_QAPF201600.get("jigyousho_bangou", ""))
        # 施設カナ名称
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuShisetsuKanaNM_textboxInput",
            value=case_data_043_QAPF201600.get("shisetsu_kana_meishou_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_selShisetsuKensakuShisetsuKanaNMOption_select",
            text=case_data_043_QAPF201600.get("shisetsu_kana_meishou_select", ""))
        # 郵便番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuPostNoOya_textboxInput",
            value=case_data_043_QAPF201600.get("yuubin_bangou_start", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuPostNoKo_textboxInput",
            value=case_data_043_QAPF201600.get("yuubin_bangou_end", ""))
        # 住所
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuJusho_textboxInput",
            value=case_data_043_QAPF201600.get("juusho_text", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_selShisetsuKensakuJushoOption_select",
            text=case_data_043_QAPF201600.get("juusho_select", ""))
        # 電話番号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_txtShisetsuKensakuTEL_textboxInput",
            value=case_data_043_QAPF201600.get("denwa_bangou", ""))
        # 施設種類
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_selShisetsuKensakuShisetsuShurui_select",
            text=case_data_043_QAPF201600.get("shisetsu_shurui", ""))
        # 所在区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF201600_selWrGyoseiku_ShisetsuKensakuShozaiKu_select",
            text=case_data_043_QAPF201600.get("shozaiku", ""))
        self.screen_shot("施設検索 画面_43")

        # 44 施設検索 画面:「検索」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF201600_WrCmnBtn05_button")
        self.click_gaitou_shisetsu_by_shisetsu_koudo(shisetsu_koudo=case_data_044_QAPF201700.get("shisetsu_koudo", ""),
                                                     tab_index=tab_index)
        self.screen_shot("施設事業所情報 画面_44")

        # 45 施設事業所情報 画面:「修正」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF200400_btnEditChg_button")

        # 46 施設事業所情報 画面: 管理情報タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF200400", label="管理情報")

        # 47 施設事業所情報 画面: 管理情報を入力
        # 経営主体
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selKeieiShutai_select",
            text=case_data_047_QAPF200400.get("keiei_shutai", ""))
        # 委託先担当部署
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtItakusakiTantoBusyo_textboxInput",
            value=case_data_047_QAPF200400.get("itakusaki_tantou_busho", ""))
        # 委託先代表者
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtItakusakiDaihyosya_textboxInput",
            value=case_data_047_QAPF200400.get("itakusaki_daihyou_sha", ""))
        # 本園分園
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHonenBunen_select",
            text=case_data_047_QAPF200400.get("hon_en_bun_en", ""))
        # 入所選考対象
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_chkNyushoSenkoTaishochk0",
            value=case_data_047_QAPF200400.get("nyuusho_senkou_taishou", ""))
        # 支弁対象
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_chkShibenTaisyochk0",
            value=case_data_047_QAPF200400.get("shiben_taishou", ""))
        # 地域区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selChiikiKubun_select",
            text=case_data_047_QAPF200400.get("chiiki_kubun", ""))
        # 主食費徴収1号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selshusyokuhiitigou_select",
            text=case_data_047_QAPF200400.get("shushokuhin_choushuu_ichigou", ""))
        # 保育料算定
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_chkHoiikuryoSanteichk0",
            value=case_data_047_QAPF200400.get("hoikuryou_santei", ""))
        # 給付対象
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_chkKyufuTaisyochk0",
            value=case_data_047_QAPF200400.get("kyuufu_taishou", ""))
        # 寒冷地区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selKanreichiKbn_select",
            text=case_data_047_QAPF200400.get("kanrei_chikubun", ""))
        # 主食費徴収2号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selshusyokuhinigou_select",
            text=case_data_047_QAPF200400.get("shushokuhin_choushuu_nigou", ""))
        # 賦課対象
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_chkFukaTaishochk0",
            value=case_data_047_QAPF200400.get("fuka_taishou", ""))
        # 収納対象(主食費）
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selshusyokuhi_select",
            text=case_data_047_QAPF200400.get("shuunou_taishou_shushokuhin", ""))
        # 採暖費地区
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selSaIDanhiChikuKbn_select",
            text=case_data_047_QAPF200400.get("saidannhi_chiku", ""))
        # 副食費徴収1号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selfukusyokuhiitigou_select",
            text=case_data_047_QAPF200400.get("fukushokuhin_choushuu_ichigou", ""))
        # 収納対象
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_chkChoshuTaishochk0",
            value=case_data_047_QAPF200400.get("shuunou_taishou", ""))
        # 収納対象(副食費）
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selfukusyokuhi_select",
            text=case_data_047_QAPF200400.get("shuunou_taishou_fukushokuhin", ""))
        # 副食費徴収2号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selfukusyokuhinigou_select",
            text=case_data_047_QAPF200400.get("fukushokuhin_choushuu_nigou", ""))
        # 市独自預かり保育
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_chkShiDokujiAzukariHoikuchk0",
            value=case_data_047_QAPF200400.get("shi_dokuji_azukari_hoiku", ""))
        # 土曜閉所1号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_chkToyobiheishoIchigochk0",
            value=case_data_047_QAPF200400.get("doyou_heisho_ichigou", ""))
        # 土曜閉所2・3号
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_chkToyobiheishoNisangochk0",
            value=case_data_047_QAPF200400.get("doyou_heisho_nigou_sangou", ""))
        # その他
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtSoNota_textboxInput",
            value=case_data_047_QAPF200400.get("sonota", ""))
        # 汎用項目1
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoKomoku1_select",
            text=case_data_047_QAPF200400.get("hanyou_koumoku1", ""))
        # 汎用項目2
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoKomoku2_select",
            text=case_data_047_QAPF200400.get("hanyou_koumoku2", ""))
        # 汎用項目3
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoKomoku3_select",
            text=case_data_047_QAPF200400.get("hanyou_koumoku3", ""))
        # 汎用項目4
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoKomoku4_select",
            text=case_data_047_QAPF200400.get("hanyou_koumoku4", ""))
        # 汎用項目5
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoKomoku5_select",
            text=case_data_047_QAPF200400.get("hanyou_koumoku5", ""))
        self.screen_shot("施設事業所情報_管理情報 画面_47")

        # 48 施設事業所情報 画面: サービスタブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF200400", label="サービス")

        # 49 施設事業所情報 画面: サービスを入力
        # 保育サービス
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtHoikuService_textboxInput",
            value=case_data_049_QAPF200400.get("hoiku_saabisu", ""))
        # 障がい
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoService1_select",
            text=case_data_049_QAPF200400.get("shougai", ""))
        # 延長
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoService2_select",
            text=case_data_049_QAPF200400.get("enchou", ""))
        # 長時間保育
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoService3_select",
            text=case_data_049_QAPF200400.get("choujikan_hoiku", ""))
        # 夜間保育
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoService4_select",
            text=case_data_049_QAPF200400.get("yakan_hoiku", ""))
        # 一時保育
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoService5_select",
            text=case_data_049_QAPF200400.get("ichiji_hoiku", ""))
        # 病後児保育
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoService6_select",
            text=case_data_049_QAPF200400.get("byougoji_hoiku", ""))
        # 休日保育
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoService7_select",
            text=case_data_049_QAPF200400.get("kyuujitsu_hoiku", ""))
        # 汎用サービス
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selHanyoService8_select",
            text=case_data_049_QAPF200400.get("hanyou_saabisu", ""))
        self.screen_shot("施設事業所情報_サービス 画面_49")

        # 50 施設事業所情報 画面: 特別保育タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF200400", label="特別保育")

        # 51 施設事業所情報 画面: 特別保育を入力
        # 対象年度
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selTaishouNendo_select",
            text=case_data_051_QAPF200400.get("taishou_nendo", ""))

        service_params = [
            {
                "service_name": case_data_051_QAPF200400.get("saabisu_shurui_1", ""),
                "params": [
                    {"title": "使用", "value": case_data_051_QAPF200400.get("shiyou_1", "")},
                    {"title": "独自料金", "value": case_data_051_QAPF200400.get("dokuji_ryoukin_1", "")},
                    {"title": "月額(上限額)", "value": case_data_051_QAPF200400.get("getsugaku_jougen_gaku_1", "")},
                    {"title": "単位当たり額", "value": case_data_051_QAPF200400.get("tani_atari_gaku_1", "")},
                    {"title": "実績行割当", "value": case_data_051_QAPF200400.get("jisseki_gyou_wariate_1", "")}
                ]
            },
            {
                "service_name": case_data_051_QAPF200400.get("saabisu_shurui_2", ""),
                "params": [
                    {"title": "使用", "value": case_data_051_QAPF200400.get("shiyou_2", "")},
                    {"title": "独自料金", "value": case_data_051_QAPF200400.get("dokuji_ryoukin_2", "")},
                    {"title": "月額(上限額)", "value": case_data_051_QAPF200400.get("getsugaku_jougen_gaku_2", "")},
                    {"title": "単位当たり額", "value": case_data_051_QAPF200400.get("tani_atari_gaku_2", "")},
                    {"title": "実績行割当", "value": case_data_051_QAPF200400.get("jisseki_gyou_wariate_2", "")}
                ]
            }
        ]
        self.select_kodomo_saabisu_shurui(service_params=service_params, tab_index=tab_index)
        self.screen_shot("施設事業所情報_特別保育 画面_51")

        # 52 施設事業所情報 画面: 利用認定タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF200400", label="利用定員")

        # 53 施設事業所情報 画面: 利用認定を入力
        # 対象クラス年齢
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selTaisyoClassNennreiShita_select",
            text=case_data_053_QAPF200400.get("taishou_kurasu_nenrei_from", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selTaisyoClassNennreiUe_select",
            text=case_data_053_QAPF200400.get("taishou_kurasu_nenrei_to", ""))
        # 対象児童
        # １号認定
        # 利用定員
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin0Saiji_1_2_textboxInput",
            value=case_data_053_QAPF200400.get("ichigou_nintei_riyou_teiin_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin1Saiji_1_3_textboxInput",
            value=case_data_053_QAPF200400.get("ichigou_nintei_riyou_teiin_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin2Saiji_1_4_textboxInput",
            value=case_data_053_QAPF200400.get("ichigou_nintei_riyou_teiin_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin3Saiji_1_5_textboxInput",
            value=case_data_053_QAPF200400.get("ichigou_nintei_riyou_teiin_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin4Saiji_1_6_textboxInput",
            value=case_data_053_QAPF200400.get("ichigou_nintei_riyou_teiin_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin5Saiji_1_7_textboxInput",
            value=case_data_053_QAPF200400.get("ichigou_nintei_riyou_teiin_5", ""))
        # 適用定員区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei0Saiji_1_8_select",
            text=case_data_053_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei1Saiji_1_9_select",
            text=case_data_053_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei2Saiji_1_10_select",
            text=case_data_053_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei3Saiji_1_11_select",
            text=case_data_053_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei4Saiji_1_12_select",
            text=case_data_053_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei5Saiji_1_13_select",
            text=case_data_053_QAPF200400.get("ichigou_nintei_tekiyou_teiin_kubun_5", ""))
        # ２・３号認定
        # 利用定員
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin0Saiji_2_2_textboxInput",
            value=case_data_053_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin1Saiji_2_3_textboxInput",
            value=case_data_053_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin2Saiji_2_4_textboxInput",
            value=case_data_053_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin3Saiji_2_5_textboxInput",
            value=case_data_053_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin4Saiji_2_6_textboxInput",
            value=case_data_053_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtRiyoTeiin5Saiji_2_7_textboxInput",
            value=case_data_053_QAPF200400.get("nigou_sangou_nintei_riyou_teiin_5", ""))
        # 適用定員区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei0Saiji_2_8_select",
            text=case_data_053_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei1Saiji_2_9_select",
            text=case_data_053_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei2Saiji_2_10_select",
            text=case_data_053_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei3Saiji_2_11_select",
            text=case_data_053_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei4Saiji_2_12_select",
            text=case_data_053_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_selRiyoSettei5Saiji_2_13_select",
            text=case_data_053_QAPF200400.get("nigou_sangou_nintei_tekiyou_teiin_kubun_5", ""))
        # うち短時間
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime0Saiji_textboxInput",
            value=case_data_053_QAPF200400.get("uchi_tanjikan_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime1Saiji_textboxInput",
            value=case_data_053_QAPF200400.get("uchi_tanjikan_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime2Saiji_textboxInput",
            value=case_data_053_QAPF200400.get("uchi_tanjikan_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime3Saiji_textboxInput",
            value=case_data_053_QAPF200400.get("uchi_tanjikan_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime4Saiji_textboxInput",
            value=case_data_053_QAPF200400.get("uchi_tanjikan_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtTanTime5Saiji_textboxInput",
            value=case_data_053_QAPF200400.get("uchi_tanjikan_5", ""))
        # 対象児童
        # 産休枠
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin0Saiji_1_2_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_sankyuu_waku_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin1Saiji_1_3_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_sankyuu_waku_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin2Saiji_1_4_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_sankyuu_waku_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin3Saiji_1_5_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_sankyuu_waku_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin4Saiji_1_6_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_sankyuu_waku_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin5Saiji_1_7_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_sankyuu_waku_5", ""))
        # 障がい枠
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin0Saiji_2_2_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_shougai_waku_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin1Saiji_2_3_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_shougai_waku_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin2Saiji_2_4_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_shougai_waku_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin3Saiji_2_5_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_shougai_waku_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin4Saiji_2_6_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_shougai_waku_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin5Saiji_2_7_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_shougai_waku_5", ""))
        # 特別枠
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin0Saiji_3_2_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_tokubetsu_waku_0", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin1Saiji_3_3_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_tokubetsu_waku_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin2Saiji_3_4_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_tokubetsu_waku_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin3Saiji_3_5_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_tokubetsu_waku_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin4Saiji_3_6_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_tokubetsu_waku_4", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_txtWakuRiyoTeiin5Saiji_3_7_textboxInput",
            value=case_data_053_QAPF200400.get("taishou_jidou_tokubetsu_waku_5", ""))
        self.screen_shot("施設事業所情報_利用定員 画面_53")

        # 54 施設事業所情報 画面: 54表利用定員タブ押下
        self.change_tab_by_label(tab_id=tab_index, screen_id="QAPF200400", label="54表利用定員")

        # 55 施設事業所情報 画面: 54表利用定員を入力
        # 休止年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtKyufuyoInfoKyushiYMD_textboxInput",
            value=case_data_055_QAPF200400.get("kyuushi_nengappi", ""))
        # 再開年月日
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_txtKyufuyoInfoSaikaiYMD_textboxInput",
            value=case_data_055_QAPF200400.get("saikai_nengappi", ""))
        # 年齢区分
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_QAPF200400_selNenreiKubun_select",
            text=case_data_055_QAPF200400.get("nenrei_kubun", ""))
        # １号認定
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_1Go3SaiJi_1_3_textboxInput",
            value=case_data_055_QAPF200400.get("ichigou_nintei_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_1Go4SaiIjoJi_1_4_textboxInput",
            value=case_data_055_QAPF200400.get("ichigou_nintei_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_1Go4SaiJi_1_5_textboxInput",
            value=case_data_055_QAPF200400.get("ichigou_nintei_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_1Go5SaiJi_1_6_textboxInput",
            value=case_data_055_QAPF200400.get("ichigou_nintei_4", ""))
        # ２号認定
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_2Go3SaiJi_1_9_textboxInput",
            value=case_data_055_QAPF200400.get("nigou_nintei_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_2Go4SaiIjoJi_1_10_textboxInput",
            value=case_data_055_QAPF200400.get("nigou_nintei_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_2Go4SaiJi_1_11_textboxInput",
            value=case_data_055_QAPF200400.get("nigou_nintei_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_2Go5SaiJi_1_12_textboxInput",
            value=case_data_055_QAPF200400.get("nigou_nintei_4", ""))
        # うち保育短時間認定
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_2GoTanjikan3SaiJi_1_15_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_2GoTanjikan4SaiIjoJi_1_16_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_2GoTanjikan4SaiJi_1_17_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_2GoTanjikan5SaiJi_1_18_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_4", ""))
        # ３号認定
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_3Go0SaiJi_1_3_textboxInput",
            value=case_data_055_QAPF200400.get("sangou_nintei_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_3Go12SaiJi_1_4_textboxInput",
            value=case_data_055_QAPF200400.get("sangou_nintei_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_3Go1SaiJi_1_5_textboxInput",
            value=case_data_055_QAPF200400.get("sangou_nintei_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_3Go2SaiJi_1_6_textboxInput",
            value=case_data_055_QAPF200400.get("sangou_nintei_4", ""))
        # うち保育短時間認定
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_3GoTanjikan0SaiJi_1_9_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_5", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_3GoTanjikan12SaiJi_1_10_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_6", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_3GoTanjikan1SaiJi_1_11_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_7", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_3GoTanjikan2SaiJi_1_12_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_8", ""))
        # 従業員枠
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_Jugyoinwaku0SaiJi_1_15_textboxInput",
            value=case_data_055_QAPF200400.get("juugyouin_waku_1", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_Jugyoinwaku12SaiJi_1_16_textboxInput",
            value=case_data_055_QAPF200400.get("juugyouin_waku_2", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_Jugyoinwaku1SaiJi_1_17_textboxInput",
            value=case_data_055_QAPF200400.get("juugyouin_waku_3", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_Jugyoinwaku2SaiJi_1_18_textboxInput",
            value=case_data_055_QAPF200400.get("juugyouin_waku_4", ""))
        # うち保育短時間認定
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_JugyoinwakuTanjikan0SaiJi_1_21_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_9", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_JugyoinwakuTanjikan12SaiJi_1_22_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_10", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_JugyoinwakuTanjikan1SaiJi_1_23_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_11", ""))
        self.form_input_by_id(
            idstr="tab0" + str(tab_index) + "_ZZZ000000_JugyoinwakuTanjikan2SaiJi_1_24_textboxInput",
            value=case_data_055_QAPF200400.get("uchi_hoiku_tanjikan_nintei_12", ""))
        self.screen_shot("施設事業所情報_54表利用定員 画面_55")

        # 56 施設事業所情報 画面:「登録」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_QAPF200400_regbtn_button")

        # 57 施設事業所情報 画面:「はい」ボタン押下
        self.alert_accept()
        self.assert_message_area(msg_span_id="tab0" + str(tab_index) + "_QAPF200400_msg_span",
                                 msg="更新しました。")
        self.screen_shot("施設事業所情報 画面_57")
