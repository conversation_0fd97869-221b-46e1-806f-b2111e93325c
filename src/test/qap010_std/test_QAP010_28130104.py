import time
from selenium.webdriver.support.ui import Select
from base.kodomo_case import KodomoSiteTestCaseBase

class TestQAP010_28130104(KodomoSiteTestCaseBase):
    """TestQAP010_28130104"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()
    
    # 延長保育料の算定ができることを確認する。
    def test_QAP010_28130104(self):
        """延長保育料算定"""
        
        batch_click_count = self.common_test_data.get("batch_click_count")
        batch_wait_seconds = self.common_test_data.get("batch_wait_seconds")
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ管理」ボタン押下
        # 4 メインメニュー画面: 「即時実行」ボタン押下
        # 5 メインメニュー画面: 「スケジュール個別追加」ボタンをダブルクリック
        self.goto_menu(["バッチ管理","即時実行","スケジュール個別追加"])
        
        # 6 バッチ管理画面: 業務名：子ども子育て支援サブシステム名：賦課処理名：月次賦課処理
        # 7 バッチ管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名"),  case_data.get("サブシステム名"), case_data.get("処理名"))
        self.screen_shot("バッチ管理画面_6")

        # 8 バッチ起動画面: 月次賦課処理「(QP7BN00280) 整合性チェック」のNoボタン押下
        self.click_button_by_label("1")
        self.wait_page_loaded()
        
        # 9 バッチ起動画面: 所管区：                               　を選択支所：                               　を選択開始年月：                               　を入力終了年月：                               　を入力チェック項目設定：全項目                               　を選択並び順：世帯台帳番号、児童_宛名C、対象年月、影響する科目　を選択
        params = [
            {"title":"所管区", "type": "select", "value": case_data.get("整合性チェック_所管区")},
            {"title":"支所", "type": "select", "value": case_data.get("整合性チェック_支所")},
            {"title":"開始年月", "type": "text", "value": case_data.get("整合性チェック_開始年月")},
            {"title":"終了年月", "type": "text", "value": case_data.get("整合性チェック_終了年月")},
            {"title":"チェック項目設定", "type": "select", "value": case_data.get("整合性チェック_チェック項目設定")},
            {"title":"並び順", "type": "select", "value": case_data.get("整合性チェック_並び順")},
        ]
        self.set_job_param_kodomo(params)
        # Assert: パラメータ化
        self.screen_shot("バッチ起動画面_9")

        # 実行ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()
        
        # 10 実行結果画面: 表示
        # Assert: 「(QP7BN00280) 整合性チェック」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果画面_10")
        
        # 11 実行結果画面: 「No1」ボタン押下
        self.click_button_by_label("1")
        time.sleep(2) 
        # 12 実行結果画面: 表示
        self.screen_shot("実行結果画面_12")
        
        # 13 バッチ管理画面: パンくずリストの「スケジュール個別追加」ボタンをクリック
        self.find_element_by_xpath("//div[@id='tab01_ZEAF002400_navi']/li[1]/a").click()

        # バッチ管理画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(case_data.get("業務名"),  case_data.get("サブシステム名"), case_data.get("処理名"))

        # 14 バッチ起動画面: 月次賦課処理「(QP7BN00210) 月次賦課計算処理」のNoボタン押下
        self.click_button_by_label("2")
        
        # 15 バッチ起動画面:対象年月：                               　を入力対象年度：                               　を入力基準年月：                               　を入力
        # Assert: パラメータ化
 
        params = [
            {"title":"対象年月", "type": "text", "value": case_data.get("月次賦課計算_対象年月")},
            {"title":"対象年度", "type": "text", "value": case_data.get("月次賦課計算_対象年度")},
            {"title":"基準年月", "type": "text", "value": case_data.get("月次賦課計算_基準年月")},
        ]
        self.set_job_param_kodomo(params)
        self.screen_shot("バッチ起動画面_15")

        # 実行ボタン押下
        self.click_button_by_label("実行")
        self.wait_page_loaded()

        # 16 実行結果画面: 表示
        # Assert: 「(QP7BN00210) 月次賦課計算処理」の状態が正常終了となることを確認
        self.wait_job_finished_kodomo(batch_click_count,batch_wait_seconds)
        self.screen_shot("実行結果画面_16")
        
        # 17 実行結果画面: 「No1」ボタン押下
        self.click_button_by_label("1")
        
        # 18 実行結果画面: 表示
        self.screen_shot("実行結果画面_18")
        
        # 20 メインメニュー画面: 表示
        self.find_element_by_xpath("//li[@id='01']/span[2]").click()
        self.screen_shot("メインメニュー画面_20")
        
        # 21 メインメニュー画面: 「子ども子育て支援」ボタン押下
        # 22 メインメニュー画面: 「入所管理」ボタン押下
        # 23 メインメニュー画面: 「児童検索」ボタンをダブルクリック
        self.goto_menu(["子ども子育て支援","入所管理","児童検索"])
        
        # 24 児童検索画面: 表示
        self.screen_shot("児童検索画面_24")
        
        # 25 児童検索画面: 宛名コード：
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF105800_txtJidoKensakuAtenaCD_textboxInput").send_keys(case_data.get("宛名コード"))
        
        # 26 児童検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索(Enter)")
        self.wait_page_loaded()
        
        # 27 児童台帳画面支給認定登録・履歴タブ: 表示
        self.screen_shot("児童台帳画面支給認定登録・履歴タブ_27")
        
        # 28 児童台帳画面支給認定登録・履歴タブ: 「賦課」ボタン押下
        self.click_button_by_label("賦課")
        self.wait_page_loaded()
        
        # 29 児童賦課情報画面保育料タブ: 表示
        self.screen_shot("児童賦課情報画面保育料タブ_29")
        
        # 30 児童賦課情報画面保育料タブ: 「特別保育利用料」タブ選択
        self.find_element_by_xpath("//li[@id='tab01_QAPF107000_hituyoriyu_li']/a/span").click()
        
        # 31 児童賦課情報画面特別保育利用料タブ: 表示
        self.screen_shot("児童賦課情報画面特別保育利用料タブ_31")
        
        # 32 児童賦課情報画面特別保育利用料タブ: 賦課年度：                               　を選択賦課年月：                               　を選択
        
        Select(self.find_element_by_id(u"tab01_QAPF107000_selFukaYear_select")).select_by_visible_text(case_data.get("賦課年度"))

        self.find_element_by_id(u"tab01_QAPF107000_selFukaNendo_textboxInput").click()
        self.find_element_by_id(u"tab01_QAPF107000_selFukaNendo_textboxInput").clear()
        self.find_element_by_id(u"tab01_QAPF107000_selFukaNendo_textboxInput").send_keys(case_data.get("賦課年月"))
        
        # Assert: パラメータ化

        self.screen_shot("児童賦課情報画面特別保育利用料タブ_32")
        
        # 33 児童賦課情報画面特別保育利用料タブ: 「賦課計算」ボタン押下
        self.click_button_by_label("賦課計算")

        
        # 34 児童賦課情報画面特別保育利用料タブ: 確認ダイアログ「賦課計算を実行します、よろしいですか？」「はい」ボタン押下
        self.find_element_by_xpath("//div[@class='ui_wrpopup_foot']/input").click()
        self.wait_page_loaded()
        # 35 児童賦課情報画面特別保育利用料タブ: 表示
        # Assert: 「個別賦課計算処理が正常に完了しました。」が表示されること。
        self.screen_shot("児童賦課情報画面特別保育利用料タブ_35")
        self.assert_message_area("tab01_QAPF107000_msg_span","個別賦課計算処理が正常に完了しました。")
        
