from base.kodomo_case import KodomoSiteTestCaseBase


class TestQAP010_28070101(KodomoSiteTestCaseBase):
    """TestQAP010_28070101"""

    def setUp(self):
        super().setUp()

    def test_QAP010_28070101(self):
        tab_index = 0
        case_data_007_ZEAF000400 = self.test_data["007_ZEAF000400"]
        case_data_008_ZEAF000400 = self.test_data["008_ZEAF000400"]
        case_data_009_ZEAF002200 = self.test_data["009_ZEAF002200"]
        case_data_014_ZEAF002700 = self.test_data["014_ZEAF002700"]
        case_data_019_ZEAF000400 = self.test_data["019_ZEAF000400"]
        case_data_020_ZEAF002200 = self.test_data["020_ZEAF002200"]

        # 1, 2 メインメニュー 画面: 「子ども子育て表示」
        self.do_login_new_tab()

        # 3 メインメニュー 画面: メインメニューから「バッチ管理」クリック
        # 4 メインメニュー 画面: 「即時実行」クリック 
        # 5 メインメニュー 画面: 「スケジュール個別追加」ダブルクリック
        self._goto_menu_by_label(menu_level_1="バッチ管理", menu_level_2="即時実行",
                                 menu_level_3="スケジュール個別追加",
                                 is_new_tab=True)
        tab_index += 1

        # 6 <スケジュール個別追加> 画面: "業務名：子ども子育て支援 <サブシステム名>：賦課 <処理名>：月次賦課処理
        # 選択" 
        # 7 スケジュール個別追加 画面: 「検索」ボタン押下
        self.kennsakujyoukenn_nyuuryoku(gyomuNM=case_data_007_ZEAF000400.get("gyoumu_mei", ""),
                                        subSystemNM=case_data_007_ZEAF000400.get("sabushisutemu_mei", ""),
                                        shoriNM=case_data_007_ZEAF000400.get("shori_mei", ""),
                                        tab_index=tab_index)
        self.screen_shot("スケジュール個別追加_画面_7")

        # 8 スケジュール個別追加 画面: 「整合性チェック」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_008_ZEAF000400.get("batch_job_001", ""),
                                             tab_index=tab_index)
        # 9 実行指示 画面: パラメータを入力
        params = [
            {"title": "所管区", "type": "select", "value": case_data_009_ZEAF002200.get("shokanku_cbx", "")},
            {"title": "支所", "type": "select", "value": case_data_009_ZEAF002200.get("shisho_cbx", "")},
            {"title": "開始年月", "type": "text", "value": case_data_009_ZEAF002200.get("kaishi_nengetsu_ymd", "")},
            {"title": "終了年月", "type": "text", "value": case_data_009_ZEAF002200.get("shuryo_nengetsu_ymd", "")},
            {"title": "チェック項目設定", "type": "select",
             "value": case_data_009_ZEAF002200.get("check_mokuhyo_settei_cbx", "")},
            {"title": "並び順", "type": "select", "value": case_data_009_ZEAF002200.get("narabi_jun_cbx", "")}
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_9")

        # 10 実行指示 画面: 「実行」ボタン押下
        exec_datetime = self.exec_batch_job_kodomo(tab_index=tab_index)

        # 11 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_11")

        # 12 実行管理 画面: 正常終了した処理の「No」ボタン押下
        self.click_button_by_label("1")

        # 13 結果確認 画面: 「納品物確認」ボタン押下
        self.click_by_id("tab0" + str(tab_index) + "_ZEAF002400_BtnNohinbutsuKakunin_button")
        self.screen_shot("納品物管理_画面_13")

        # 14 納品物管理 画面: 納品物「整合性チェック_エラー.csv」の「ダウンロード」ボタン押下 
        # 15 ファイルダウンロード 画面: 「No1」ボタン押下
        # 16 ファイルダウンロード 画面: 「ファイルを開く(O)」ボタン押下
        # 17 納品物管理 画面: 「×」ボタン押下
        self.get_job_report_kodomo(exec_datetime=exec_datetime,
                                   tab_index=tab_index, report_name=case_data_014_ZEAF002700.get("report_name", ""))
        # 18 納品物管理 画面: パンくず「スケジュール個別追加」を押下
        self.click_breadcrumb_by_label(tab_index=tab_index, screen_id="ZEAF002700", label="スケジュール個別追加")
        self.screen_shot("スケジュール個別追加_画面_18")

        # 19 スケジュール個別追加 画面: 「月次賦課計算処理」の「No」ボタン押下
        self.click_batch_job_button_by_label(job_label=case_data_019_ZEAF000400.get("batch_job_002", ""),
                                             tab_index=tab_index)
        # 20 実行指示 画面: パラメータを入力
        params = [
            {"title": "対象年月", "type": "text", "value": case_data_020_ZEAF002200.get("taishou_nengetsu_ymd", "")},
            {"title": "対象年度", "type": "text", "value": case_data_020_ZEAF002200.get("taishou_nendo_ymd", "")},
            {"title": "基準年月", "type": "text", "value": case_data_020_ZEAF002200.get("kijun_nengetsu_ymd", "")},
        ]
        self.set_job_param_kodomo(job_params=params, tab_index=tab_index)
        self.screen_shot("実行指示_画面_20")

        # 21 実行指示 画面: 「実行」ボタン押下
        self.exec_batch_job_kodomo(tab_index=tab_index)

        # 22 実行管理 画面: 「検索」ボタン押下
        self.wait_job_finished_kodomo(limit_wait_count=120, time_span_sec=20)
        self.screen_shot("実行管理_画面_22")
