DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN


DELETE FROM WR$$JICHITAI_CODE$$QA..QAK被保険者資格内容	WHERE 業務コード = 'QAK010' AND 宛名コード = '$$RAK010_ATENACODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAK収納年額保険料	WHERE 業務コード = 'QAK010' AND 宛名コード = '$$RAK010_ATENACODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAK収納期別保険料	WHERE 業務コード = 'QAK010' AND 宛名コード = '$$RAK010_ATENACODE$$'

INSERT INTO WR$$JICHITAI_CODE$$QA..QAK被保険者資格内容
           ([業務コード]
           ,[履歴番号]
           ,[履歴分類]
           ,[自治体コード]
           ,[福祉事務所コード]
           ,[宛名コード]
           ,[個人区分コード]
           ,[被保険者番号]
           ,[被保険者資格取得事由コード]
           ,[被保険者資格取得年月日]
           ,[被保険者資格喪失事由コード]
           ,[被保険者資格喪失年月日]
           ,[保険者番号適用開始年月日]
           ,[保険者番号適用終了年月日]
           ,[氏名カナ]
           ,[生年月日]
           ,[性別コード]
           ,[現都道府県名]
           ,[現市区町村名]
           ,[現住所]
           ,[作成年月日]
           ,[作成時刻]
           ,[取込年月日]
           ,[取込時刻]
           ,[削除フラグ]
           ,[データ作成担当者]
           ,[データ更新担当者]
           ,[データ作成日時]
           ,[データ更新日時]
           ,[データ更新プログラム])
     VALUES
		   ('QAK010',100030,'0','$$JICHITAI_CODE$$','00000','$$RAK010_ATENACODE$$','1','00100030','001','20080401','000','99999999','20080401','99999999','コウキ チチ30030','19650101','1','アイネス県','アイネス区','テスト町丁１','20210907','000000','20210907','000000','0',9501,9501,'2021-09-08 10:47:38.587','2021-09-08 10:47:38.587','QAKB1003')


INSERT INTO WR$$JICHITAI_CODE$$QA..QAK収納年額保険料
           ([自治体コード]
           ,[業務コード]
           ,[福祉事務所コード]
           ,[通知書番号]
           ,[賦課年度]
           ,[相当年度]
           ,[収納更正履歴番号]
           ,[徴収方法区分コード]
           ,[賦課事由]
           ,[賦課決定年月日]
           ,[被保険者番号]
           ,[被保険者番号枝番]
           ,[宛名コード]
           ,[賦課管理番号]
           ,[削除フラグ]
           ,[データ作成担当者]
           ,[データ更新担当者]
           ,[データ作成日時]
           ,[データ更新日時]
           ,[データ更新プログラム])
     VALUES
           ('$$JICHITAI_CODE$$','QAK010','$$JICHITAI_CODE$$','2021100030','2017','2017',1,'2','00','20170401','00100030',1,'$$RAK010_ATENACODE$$','01','0','9501','9501','2015-10-28 15:37:35.000','2016-09-12 17:46:09.513','QAKB0061')


INSERT INTO WR$$JICHITAI_CODE$$QA..QAK収納期別保険料
           ([自治体コード]
           ,[業務コード]
           ,[福祉事務所コード]
           ,[通知書番号]
           ,[賦課年度]
           ,[相当年度]
           ,[収納更正履歴番号]
           ,[徴収方法区分コード]
           ,[期別番号]
           ,[宛名コード]
           ,[被保険者番号]
           ,[被保険者番号枝番]
           ,[期別保険料]
           ,[納期限]
           ,[時効完成予定日]
           ,[時効完成予定起算日]
           ,[時効完成予定起算事由]
           ,[不能欠損フラグ]
           ,[賦課管理番号]
           ,[削除フラグ]
           ,[データ作成担当者]
           ,[データ更新担当者]
           ,[データ作成日時]
           ,[データ更新日時]
           ,[データ更新プログラム])
     VALUES
           ('$$JICHITAI_CODE$$','QAK010','$$JICHITAI_CODE$$','2021100030','2017','2017',1,'2','01','$$RAK010_ATENACODE$$','00100030',1,0,'20500425','20220331','20200331','1','0','01','0','9501','9501','2015-10-28 15:37:35.000','2018-01-31 20:01:42.000','QAKF_Tai')



IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END