DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

UPDATE WR$$JICHITAI_CODE$$QA..QAK還付内容 SET 還付支払方法 = '1',処理内容 = '3' WHERE 宛名コード = '$$宛名コード$$' AND 業務コード = 'QAK010' AND 削除フラグ = '0'

DELETE FROM WR$$JICHITAI_CODE$$QZ..QZ口座マスタ WHERE 固有コード = '$$宛名コード$$' AND 口座番号 = '$$口座番号$$' AND 業務コード = 'QAK010'
UPDATE WR$$JICHITAI_CODE$$QZ..QZ口座マスタ SET 有効期間終了 = '99999999' WHERE 固有コード = '$$宛名コード$$' AND 業務コード = 'QAK010' AND 削除フラグ = '0'

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END