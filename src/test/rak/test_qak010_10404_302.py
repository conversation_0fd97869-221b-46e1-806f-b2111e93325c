import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01010404302(FukushiSiteTestCaseBase):
    """TESTQAK01010404302"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10404_302.sql", params=atena_list)
        super().setUp() 

    #ここに操作したコードをコピーして貼り付ける
    def test_case_qak010_10404_302(self):
        """test_case_qak010_10404_302"""
        
        case_data = self.test_data[self.__class__.__name__]
        insatsu_tyouhyou_name = case_data.get("印刷帳票名", "")
        # ログイン
        self.do_login()

        #1	メインメニュー画面	「バッチ起動」ボタン押下
        self.batch_kidou_click()
        #2	バッチ起動画面	業務「高齢」選択
        self.form_input_by_id(idstr="GyomuSelect", text="高齢")
        #3	バッチ起動画面	事業「後期高齢者医療」選択
        self.form_input_by_id(idstr="JigyoSelect", text="後期高齢者医療")
        #4	バッチ起動画面	処理区分「収納業務_広域連携」選択
        self.form_input_by_id(idstr="ShoriKubunSelect", text="収納業務_広域連携")
        #5	バッチ起動画面	処理分類「広域連合連携_送信処理」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="広域連合連携_送信処理")

        if not self.check_batch_job_exist(insatsu_tyouhyou_name):
            return
        
        #6	バッチ起動画面	「収納情報作成」のNoボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name)
        self.screen_shot("10404_302-06" )

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("10404_302-08" )

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("10404_302-11" )

        # ジョブ実行履歴画面: 「戻る」ボタン押下
        self.return_click()

        # メインメニュー画面: 「広域連携情報参照」ボタン押下
        self.click_button_by_label("広域連携情報参照")

        # 広域連携情報参照画面: 「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("10404_302-16" )

        # 「処理日時」ソートボタン押下
        self.click_by_id("CmdSortTimeStamp")
        self.click_by_id("DL1_1")
        self.click_by_id("DL2_1")
        self.click_by_id("DL3_1")

        # 広域連携情報参照画面: 「戻る」ボタン押下
        self.return_click()