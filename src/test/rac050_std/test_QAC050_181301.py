import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181301(FukushiSiteTestCaseBase):
    """TestQAC050_181301"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181301"]
        # sql_params = {"TARGET_GYOUMU_CODE": "QAC050",
        #               "TARGET_NENDO": case_data.get("nendoY", ""),
        #               "TARGET_NENDO_2": case_data.get("nendoY_2", ""),
        #               "INSERT_SETAI_CODE_0": case_data.get("setai_code_0", ""),
        #               "INSERT_SETAI_CODE_1": case_data.get("setai_code_1", ""),
        #               "INSERT_SETAI_CODE_2": case_data.get("setai_code_2", ""),
        #               "INSERT_SETAI_CODE_3": case_data.get("setai_code_3", ""),
        #               "INSERT_ATENA_CODE_0_1": case_data.get("atena_code_0_1", ""),
        #               "INSERT_ATENA_CODE_0_2": case_data.get("atena_code_0_2", ""),
        #               "INSERT_ATENA_CODE_0_3": case_data.get("atena_code_0_3", ""),
        #               "INSERT_ATENA_CODE_0_4": case_data.get("atena_code_0_4", ""),
        #               "INSERT_ATENA_CODE_1_1": case_data.get("atena_code_1_1", ""),
        #               "INSERT_ATENA_CODE_2_1": case_data.get("atena_code_2_1", ""),
        #               "INSERT_ATENA_CODE_3_1": case_data.get("atena_code_3_1", "")}
        # self.exec_sqlfile("Test_QAC050_181301.sql", params=sql_params)
        super().setUp()

    # 現況届提出対象者を抽出できることを確認する。
    def test_QAC050_181301(self):
        """現況届提出依頼対象者抽出"""

        case_data = self.test_data["TestQAC050_181301"]
        atena_code_0_1 = case_data.get("atena_code_0_1", "")
        atena_code_1_1 = case_data.get("atena_code_1_1", "")
        preShinseiShubetsuCmb_1 = case_data.get("preShinseiShubetsuCmb_1", "")
        preShinseiRiyuuCmb_1 = case_data.get("preShinseiRiyuuCmb_1", "")
        preTxtShinseiYMD_1 = case_data.get("preTxtShinseiYMD_1", "")
        preTantoShokatsukuCmb_1 = case_data.get("preTantoShokatsukuCmb_1", "")
        preJyukyusyaKbnCmb_1 = case_data.get("preJyukyusyaKbnCmb_1", "")
        preTxtpreShikyuKaishiYMD_1 = case_data.get("preTxtpreShikyuKaishiYMD_1", "")
        preGengakuYMChkBox_1 = case_data.get("preGengakuYMChkBox_1", "")
        preTxtGengakuYM_1 = case_data.get("preTxtGengakuYM_1", "")
        preTxtKaitei_1 = case_data.get("preTxtKaitei_1", "")
        preTxtGaitoYMD_1 = case_data.get("preTxtGaitoYMD_1", "")
        preCmbGaitoJiyu_1 = case_data.get("preCmbGaitoJiyu_1", "")
        preTxtJiyuYMD_1 = case_data.get("preTxtJiyuYMD_1", "")
        preTxtToushoShikyuYM_1 = case_data.get("preTxtToushoShikyuYM_1", "")
        preTxtShintatsu1YMD_1 = case_data.get("preTxtShintatsu1YMD_1", "")
        preShintatsu1HanteiCmb_1 = case_data.get("preShintatsu1HanteiCmb_1", "")
        preTxtKetteiYMD_1 = case_data.get("preTxtKetteiYMD_1", "")
        preKetteiKekkaCmb_1 = case_data.get("preKetteiKekkaCmb_1", "")
        atena_code_2_1 = case_data.get("atena_code_2_1", "")
        atena_code_3_1 = case_data.get("atena_code_3_1", "")
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pGenjyoY = case_data.get("pGenjyoY", "")
        pOutputOrder = case_data.get("pOutputOrder", "")
        pHakkoYMD = case_data.get("pHakkoYMD", "")
        pAtenaStart = case_data.get("pAtenaStart", "")
        pAtenaEnd = case_data.get("pAtenaEnd", "")

#        # 「1813-24」用データ入力
#        self.do_login()
#        self.shinsei_shikaku_kanri_click()
#        self.kojin_kensaku_by_atena_code(atena_code=atena_code_0_1)
#        self.click_button_by_label("児童扶養手当")
#        self.click_button_by_label("申請内容入力")
#        # 申請種別
#        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
#        # 申請理由
#        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
#        self.click_button_by_label("確定")
#        # 請求年月日
#        self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_1)
#        # 管理場所
#        self.form_input_by_id(idstr="TantoShokatsukuCmb", text=preTantoShokatsukuCmb_1)
#        # 受給者区分
#        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=preJyukyusyaKbnCmb_1)
#        # 当初支給開始年月日
#        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=preTxtpreShikyuKaishiYMD_1)
#        # 減額開始年月
#        self.form_input_by_id(idstr="GengakuYMChkBox", value=preGengakuYMChkBox_1)
#        self.form_input_by_id(idstr="TxtGengakuYM", value=preTxtGengakuYM_1)
#        # 改定年月
#        self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_1)
#        self.click_button_by_label("児童追加")
#        self.click_button_by_label("2")
#        # 該当年月日
#        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
#        # 該当事由
#        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
#        # 支給事由発生年月日
#        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
#        # 当初支給開始年月日
#        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("児童追加")
#        self.click_button_by_label("3")
#        # 該当年月日
#        self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
#        # 該当事由
#        self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
#        # 支給事由発生年月日
#        self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
#        # 当初支給開始年月日
#        self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("本庁進達入力")
#        # 進達年月日
#        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("本庁進達結果入力")
#        # 進達結果
#        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("決定内容入力")
#        # 決定年月日
#        self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_1)
#        # 決定結果
#        self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#
        # ＜対象者の条件＞
        # ・児童扶養手当の資格を持っている住民
        # ・本人と児童がいる世帯の住民
        # ・児童扶養手当で令和５年度の現況履歴がない
        # ・児童扶養手当の支給区分が「全部支給」
        # self.do_login()
        # self.shinsei_shikaku_kanri_click()
        # self.kojin_kensaku_by_atena_code(atena_code=atena_code_1_1)
        # self.click_button_by_label("児童扶養手当")
        # self.click_button_by_label("申請内容入力")
        # 申請種別
        # self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
        # 申請理由
        # self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
        # self.click_button_by_label("確定")
        # 請求年月日
        # self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_1)
        # 管理場所
        # self.form_input_by_id(idstr="TantoShokatsukuCmb", text=preTantoShokatsukuCmb_1)
        # 受給者区分
        # self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=preJyukyusyaKbnCmb_1)
        # 当初支給開始年月日
        # self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=preTxtpreShikyuKaishiYMD_1)
        # 減額開始年月
        # self.form_input_by_id(idstr="GengakuYMChkBox", value=preGengakuYMChkBox_1)
        # self.form_input_by_id(idstr="TxtGengakuYM", value=preTxtGengakuYM_1)
        # 改定年月
        # self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_1)
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("2")
        # 該当年月日
        # self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        # self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("3")
        # 該当年月日
        # self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        # self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")
        # self.click_button_by_label("本庁進達入力")
        # 進達年月日
        # self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")
        # self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        # self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")
        # self.click_button_by_label("決定内容入力")
        # 決定年月日
        # self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_1)
        # 決定結果
        # self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")

        # ＜対象者の条件＞
        # ・児童扶養手当の資格を持っている住民
        # ・本人と児童がいる世帯の住民
        # ・児童扶養手当で令和５年度の現況履歴がない
        # self.do_login()
        # self.shinsei_shikaku_kanri_click()
        # self.kojin_kensaku_by_atena_code(atena_code=atena_code_2_1)
        # self.click_button_by_label("児童扶養手当")
        # self.click_button_by_label("申請内容入力")
        # 申請種別
        # self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
        # 申請理由
        # self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
        # self.click_button_by_label("確定")
        # 請求年月日
        # self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_1)
        # 管理場所
        # self.form_input_by_id(idstr="TantoShokatsukuCmb", text=preTantoShokatsukuCmb_1)
        # 受給者区分
        # self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=preJyukyusyaKbnCmb_1)
        # 当初支給開始年月日
        # self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=preTxtpreShikyuKaishiYMD_1)
        # 減額開始年月
        # self.form_input_by_id(idstr="GengakuYMChkBox", value=preGengakuYMChkBox_1)
        # self.form_input_by_id(idstr="TxtGengakuYM", value=preTxtGengakuYM_1)
        # 改定年月
        # self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_1)
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("2")
        # 該当年月日
        # self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        # self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("3")
        # 該当年月日
        # self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        # self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")
        # self.click_button_by_label("本庁進達入力")
        # 進達年月日
        # self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")
        # self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        # self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")
        # self.click_button_by_label("決定内容入力")
        # 決定年月日
        # self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_1)
        # 決定結果
        # self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")

        # ＜対象者の条件＞
        # ・児童扶養手当の資格を持っている住民
        # ・本人と児童がいる世帯の住民
        # ・児童扶養手当で令和５年度の現況履歴がない
        # self.do_login()
        # self.shinsei_shikaku_kanri_click()
        # self.kojin_kensaku_by_atena_code(atena_code=atena_code_3_1)
        # self.click_button_by_label("児童扶養手当")
        # self.click_button_by_label("申請内容入力")
        # 申請種別
        # self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
        # 申請理由
        # self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
        # self.click_button_by_label("確定")
        # 請求年月日
        # self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_1)
        # 管理場所
        # self.form_input_by_id(idstr="TantoShokatsukuCmb", text=preTantoShokatsukuCmb_1)
        # 受給者区分
        # self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=preJyukyusyaKbnCmb_1)
        # 当初支給開始年月日
        # self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=preTxtpreShikyuKaishiYMD_1)
        # 減額開始年月
        # self.form_input_by_id(idstr="GengakuYMChkBox", value=preGengakuYMChkBox_1)
        # self.form_input_by_id(idstr="TxtGengakuYM", value=preTxtGengakuYM_1)
        # 改定年月
        # self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_1)
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("2")
        # 該当年月日
        # self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        # self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("3")
        # 該当年月日
        # self.form_input_by_id(idstr="TxtGaitoYMD", value=preTxtGaitoYMD_1)
        # 該当事由
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text=preCmbGaitoJiyu_1)
        # 支給事由発生年月日
        # self.form_input_by_id(idstr="TxtJiyuYMD", value=preTxtJiyuYMD_1)
        # 当初支給開始年月日
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value=preTxtToushoShikyuYM_1)
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")
        # self.click_button_by_label("本庁進達入力")
        # 進達年月日
        # self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")
        # self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        # self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")
        # self.click_button_by_label("決定内容入力")
        # 決定年月日
        # self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_1)
        # 決定結果
        # self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：年次処理分類：現況出力
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「現況届出力対象者抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3101", ""))

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 現況年度「令和05年」出力順「証書番号順」宛名コード開始「100300000100001」宛名コード終了「100400000100000」
        params = [
            {"title": "現況年度", "type": "select", "value": pGenjyoY},
            {"title": "出力順", "type": "select", "value": pOutputOrder},
            {"title": "宛名コード開始", "type": "text", "value": pAtenaStart},
            {"title": "宛名コード終了", "type": "text", "value": pAtenaEnd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.click_job_exec_log_search()

        # 19 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「現況届対象者一覧」のNoボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.pdf_output_and_download(case_name="ジョブ帳票履歴画面")  # instead of self.click_button_by_label("ファイルを開く")

        # 22 現況届対象者一覧（PDF）: 表示
        # self.screen_shot("現況届対象者一覧（PDF）_22")

        # 23 現況届対象者一覧（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("現況届対象者一覧（PDF）_23")

        # 24 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_24")

        # 25 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 26 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_26")

        # 27 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 28 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_28")

        # 29 バッチ起動画面: 「現況届出力対象者更新処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3102", ""))

        # 30 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_30")

        # 31 バッチ起動画面: 発行年月日「20230702」
        params = [
            {"title": "発行年月日", "type": "text", "value": pHakkoYMD}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_31")

        # 32 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 33 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_33")

        # 34 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 35 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_35")

        # 36 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 37 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_37")

        # 38 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_38")
