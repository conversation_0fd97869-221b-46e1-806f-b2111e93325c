import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181403(FukushiSiteTestCaseBase):
    """TestQAC050_181403"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181403"]
        super().setUp()

    # 一部支給停止適用除外事由届未提出者の抽出ができることを確認する。
    def test_QAC050_181403(self):
        """一部支給停止適用除外事由届未提出者抽出"""

        case_data = self.test_data["TestQAC050_181403"]
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        batch_job_racj3176 = case_data.get("batch_job_racj3176", "")
        pIhiranShubetsu = case_data.get("pIhiranShubetsu", "")
        pTaishoYM = case_data.get("pTaishoYM", "")
        pStartYMD = case_data.get("pStartYMD", "")
        pEndYMD = case_data.get("pEndYMD", "")
        pOutputOrder = case_data.get("pOutputOrder", "")
        pShoriKubun = case_data.get("pShoriKubun", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：13条の3関連処理分類：除外届未提出者
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「除外届未提出者_一覧」のNoボタン押下
        self.click_batch_job_button_by_label(batch_job_racj3176)

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面:
        #  一覧種別「1」
        # 対象年月「202306」
        # 開始提出日「（空白）」
        # 終了提出日「（空白）」
        # 出力順「証書番号順」選択
        # 処理区分「0」
        params = [
            {"title": "一覧種別", "type": "text", "value": pIhiranShubetsu},
            {"title": "対象年月", "type": "text", "value": pTaishoYM},
            {"title": "開始提出日", "type": "text", "value": pStartYMD},
            {"title": "終了提出日", "type": "text", "value": pEndYMD},
            {"title": "出力順", "type": "select", "value": pOutputOrder},
            {"title": "処理区分", "type": "text", "value": pShoriKubun}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 19 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「除外届未提出者・提出者一覧」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 除外届未提出者・提出者一覧（PDF）: 表示
        # self.screen_shot("除外届未提出者・提出者一覧（PDF）_22")

        # 23 除外届未提出者・提出者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 24 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_24")
