import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181313(FukushiSiteTestCaseBase):
    """TestQAC050_181313"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181313"]
        super().setUp()

    # 所得判定で全部支給の場合、全部支給の情報を登録できることを確認する。
    def test_QAC050_181313(self):
        """全部支給処理"""

        case_data = self.test_data["TestQAC050_181313"]
        atena_code = case_data.get("atena_code", "")
        atena_code_1 = case_data.get("atena_code_1", "")
        pShinseiShubetsuCmb = case_data.get("pShinseiShubetsuCmb", "")
        pShinseiRiyuuCmb = case_data.get("pShinseiRiyuuCmb", "")
        pTantoShokatsukuCmb = case_data.get("pTantoShokatsukuCmb", "")
        pTxtShinseiYMD = case_data.get("pTxtShinseiYMD", "")
        pTxtJiyuHasseiYMD = case_data.get("pTxtJiyuHasseiYMD", "")
        pTxtKaitei = case_data.get("pTxtKaitei", "")
        pTxtKetteiYMD = case_data.get("pTxtKetteiYMD", "")
        pKetteiKekkaCmb = case_data.get("pKetteiKekkaCmb", "")
        pTxtShoushoKoufuYMD = case_data.get("pTxtShoushoKoufuYMD", "")
        pCmbGyomu = case_data.get("pCmbGyomu", "")
        pCmbJigyo = case_data.get("pCmbJigyo", "")
        pRdoKesikomiGenkyo = case_data.get("pRdoKesikomiGenkyo", "")
        pRdoKKubun_1 = case_data.get("pRdoKKubun_1", "")
        pCmbNendo = case_data.get("pCmbNendo", "")
        pRdoTKubun_1 = case_data.get("pRdoTKubun_1", "")
        pTxtTYMDS = case_data.get("pTxtTYMDS", "")
        pTxtTYMDE = case_data.get("pTxtTYMDE", "")
        pChkIkkatsu = case_data.get("pChkIkkatsu", "")
        preTxtShintatsu1YMD_1 = case_data.get("preTxtShintatsu1YMD_1", "")
        preShintatsu1HanteiCmb_1 = case_data.get("preShintatsu1HanteiCmb_1", "")
        pTxtHanteiYMD = case_data.get("pTxtHanteiYMD", "")
        pCmbHKekka = case_data.get("pCmbHKekka", "")
        pCmbSHTaisyo = case_data.get("pCmbSHTaisyo", "")
        pGyomuSelect = case_data.get("pGyomuSelect", "")
        pJigyoSelect = case_data.get("pJigyoSelect", "")
        pShoriKubunSelect = case_data.get("pShoriKubunSelect", "")
        pShoriBunruiSelect = case_data.get("pShoriBunruiSelect", "")
        pKetteiYMD = case_data.get("pKetteiYMD", "")
        pTxtHakkouYMD = case_data.get("pTxtHakkouYMD", "")
        pShintatsu1HanteiCmb = case_data.get("pShintatsu1HanteiCmb", "")

#        # 「1813-24」用データ入力
#        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_0, gyoumu_code="QAC050")
#        self.click_button_by_label("申請内容入力")
#        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=pShinseiShubetsuCmb)
#        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=pShinseiRiyuuCmb)
#        self.click_button_by_label("確定")
#        self.form_input_by_id(idstr="TantoShokatsukuCmb", text=pTantoShokatsukuCmb)
#        self.form_input_by_id(idstr="TxtShinseiYMD", value=pTxtShinseiYMD)
#        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=pTxtJiyuHasseiYMD)
#        self.form_input_by_id(idstr="TxtKaitei", value=pTxtKaitei)
#        self.click_button_by_label("1")
#        self.return_click()
#        self.click_button_by_label("公的年金等停止額")
#        self.return_click()
#        self.find_common_buttons()
#        self.open_common_buttons_area()
#        self.common_button_click(button_text="所得判定詳細情報")
#        self.return_click()
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("本庁進達入力")
#        # 進達年月日
#        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("本庁進達結果入力")
#        # 進達結果
#        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("決定内容入力")
#        self.form_input_by_id(idstr="TxtKetteiYMD", value=pTxtKetteiYMD)
#        self.form_input_by_id(idstr="KetteiKekkaCmb", text=pKetteiKekkaCmb)
#        self.form_input_by_id(idstr="TxtShoushoKoufuYMD", value=pTxtShoushoKoufuYMD)
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.do_login()
#        self.click_button_by_label("現況消込")
#        self.form_input_by_id(idstr="CmbGyomu", text=pCmbGyomu)
#        self.form_input_by_id(idstr="CmbJigyo", text=pCmbJigyo)
#        self.form_input_by_id(idstr="RdoKesikomiGenkyo", value=pRdoKesikomiGenkyo)
#        self.form_input_by_id(idstr="RdoKKubun_1", value=pRdoKKubun_1)
#        self.form_input_by_id(idstr="CmbNendo", text=pCmbNendo)
#        self.form_input_by_id(idstr="RdoTKubun_1", value=pRdoTKubun_1)
#        self.form_input_by_id(idstr="TxtTYMDS", value=pTxtTYMDS)
#        self.form_input_by_id(idstr="TxtTYMDE", value=pTxtTYMDE)
#        self.click_button_by_label("入力完了")
#        atena_col = '4'
#        table_idx = 0
#        tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
#        table_idx = 0
#        for elem in tr_elem:
#            table_idx += 1
#            td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + atena_col + ")")
#            if atena_code_0 == td_elem.text:
#                self.form_input_by_id(idstr="ChkIkkatsu_" + str(table_idx), value=pChkIkkatsu)
#                self.click_by_id("SelBtn" + str(table_idx))
#                break
#        self.click_button_by_label("修正")
#        self.find_common_buttons()
#        self.open_common_buttons_area()
#        self.common_button_click(button_text="現況情報")
#        self.click_button_by_label("1")
#        self.click_button_by_label("修正")
#        self.form_input_by_id(idstr="TxtHanteiYMD", value=pTxtHanteiYMD)
#        self.form_input_by_id(idstr="CmbHKekka", text=pCmbHKekka)
#        self.form_input_by_id(idstr="CmbSHTaisyo", text=pCmbSHTaisyo)
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.return_click()
#        self.click_button_by_label("登録")
#        self.alert_ok()
#
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_1")

        # 2 "児童扶養手当 資格管理画面": 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")
        self.wait_page_loaded(wait_timeout=10)

        # 3 "児童扶養手当 資格管理画面": "申請種別「支給停止事由変更」選択 申請理由「その他」選択"
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=pShinseiShubetsuCmb)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=pShinseiRiyuuCmb)

        # 4 "児童扶養手当 資格管理画面": 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 5 "児童扶養手当 資格管理画面": "申請年月日「20230802」 事由発生日「20230802」 改定年月「202311」"
        # self.form_input_by_id(idstr="TantoShokatsukuCmb", text=pTantoShokatsukuCmb)
        self.form_input_by_id(idstr="TxtShinseiYMD", value=pTxtShinseiYMD)
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=pTxtJiyuHasseiYMD)
        self.form_input_by_id(idstr="TxtKaitei", value=pTxtKaitei)
        self.screen_shot("児童扶養手当資格管理画面_5")

        # 6 "児童扶養手当 資格管理画面": 児童「1」Noボタン押下
        self.click_button_by_label("1")

        # 7 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_7")

        # 8 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 9 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_9")

        # 10 "児童扶養手当 資格管理画面": 「公的年金等停止額」ボタン押下
        self.click_button_by_label("公的年金等停止額")

        # 11 年金登録確認画面: 表示
        self.screen_shot("年金登録確認画面_11")

        # 12 年金登録確認画面: 「戻る」ボタン押下
        self.return_click()

        # 13 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_13")

        # 14 "児童扶養手当 資格管理画面": 「所得判定詳細情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="所得判定詳細情報")

        # 15 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_15")

        # 16 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 17 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_17")

        # 18 "児童扶養手当 資格管理画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 19 "児童扶養手当 資格管理画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 20 "児童扶養手当 資格管理画面": 表示	メッセージエリアに「登録しました。 」と表示されていることを確認する。
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_20")

        # self.click_button_by_label("本庁進達入力")
        # 進達年月日
        # self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")
        # self.click_button_by_label("本庁進達結果入力")
        # 進達結果
        # self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました。")

        # 21 "児童扶養手当 資格管理画面": 「決定内容入力」ボタン押下
        can_shintatsu_button = self.click_button_by_label(case_data.get("shintatsu_button", ""))
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value=case_data.get("txt_shintatsu1_ymd", ""))
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.wait_page_loaded(wait_timeout=10)
            self.assert_message_area("登録しました")
            self.click_button_by_label(case_data.get("shintatsu_kekka_button", ""))
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=case_data.get("shintatsu1_hantei_cmb", ""))
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.wait_page_loaded(wait_timeout=10)
            self.assert_message_area("登録しました")

        self.click_button_by_label("決定内容入力")

        # 22 "児童扶養手当 資格管理画面": "決定年月日「20230902」 決定結果「決定」選択 証書交付年月日「20230902」"
        self.form_input_by_id(idstr="TxtKetteiYMD", value=pTxtKetteiYMD)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=pKetteiKekkaCmb)
        self.form_input_by_id(idstr="TxtShoushoKoufuYMD", value=pTxtShoushoKoufuYMD)
        self.screen_shot("児童扶養手当資格管理画面_22")

        # 23 "児童扶養手当 資格管理画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 24 "児童扶養手当 資格管理画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 25 "児童扶養手当 資格管理画面": 表示	メッセージエリアに「登録しました。 」と表示されていることを確認する
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_25")

        # 26 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_26")

        # 27 メインメニュー画面: 「現況消込」ボタン押下
        self.click_button_by_label("現況消込")

        # 28 現況消込対象検索: 表示
        self.screen_shot("現況消込対象検索_28")

        # 29 現況消込対象検索: "業務「児童」選択 事業「児童扶養手当」選択"
        self.form_input_by_id(idstr="CmbGyomu", text=pCmbGyomu)
        self.form_input_by_id(idstr="CmbJigyo", text=pCmbJigyo)

        # 30 現況消込対象検索: 表示
        self.screen_shot("現況消込対象検索_30")

        # 31 現況消込対象検索: "消込対象「現況届と除外届」チェック 消込区分「検索」チェック"
        self.form_input_by_id(idstr="RdoKesikomiGenkyo", value=pRdoKesikomiGenkyo)
        self.form_input_by_id(idstr="RdoKKubun_1", value=pRdoKKubun_1)

        # 32 現況消込対象検索: 表示
        self.screen_shot("現況消込対象検索_32")

        # 33 現況消込対象検索: "対象年度「令和5年」選択 提出区分「提出済」チェック"
        self.form_input_by_id(idstr="CmbNendo", text=pCmbNendo)
        self.form_input_by_id(idstr="RdoTKubun_1", value=pRdoTKubun_1)

        # 34 現況消込対象検索: 表示
        self.screen_shot("現況消込対象検索_34")

        # 35 現況消込対象検索: "提出年月日開始「20230802」 提出年月日終了「（空欄）」"
        self.form_input_by_id(idstr="TxtTYMDS", value=pTxtTYMDS)
        self.form_input_by_id(idstr="TxtTYMDE", value=pTxtTYMDE)
        self.screen_shot("現況消込対象検索_35")

        # 36 現況消込対象検索: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 37 現況消込対象一覧: 表示
        self.screen_shot("現況消込対象一覧_37")

        # 38 現況消込対象一覧: 現況消込対象一覧」Noボタン押下
        atena_col = '4'
        tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
        table_idx = 0
        for elem in tr_elem:
            table_idx += 1
            td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + atena_col + ")")
            if atena_code_1 == td_elem.text:
                self.form_input_by_id(idstr="ChkIkkatsu_" + str(table_idx), value=pChkIkkatsu)
                self.click_by_id("SelBtn" + str(table_idx))
                break

        # 39 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_39")

        # 40 "児童扶養手当 資格管理画面": 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 41 "児童扶養手当 資格管理画面": 「現況情報」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="現況情報")

        # 42 現況履歴画面: 表示
        self.screen_shot("現況履歴画面_42")

        # 43 現況履歴画面: 現況履歴一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 44 現況履歴画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 45 現況履歴画面: "判定年月日「20230902」 判定結果「全額支給」 所得判定対象者「本人（父または母）」"
        self.form_input_by_id(idstr="TxtHakkouYMD", value=pTxtHakkouYMD)
        self.form_input_by_id(idstr="TxtHanteiYMD", value=pTxtHanteiYMD)
        self.form_input_by_id(idstr="CmbHKekka", text=pCmbHKekka)
        self.form_input_by_id(idstr="CmbSHTaisyo", text=pCmbSHTaisyo)
        self.screen_shot("現況履歴画面_45")

        # 46 現況履歴画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 47 "児童扶養手当 資格管理画面": 表示
        self.screen_shot("児童扶養手当資格管理画面_47")

        # 48 "児童扶養手当 資格管理画面": 「月額計算」ボタン押下
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=pShintatsu1HanteiCmb)
        self.click_button_by_label("月額計算")

        # 49 "児童扶養手当 資格管理画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 50 "児童扶養手当 資格管理画面": 表示	メッセージエリアに「登録しました。 」と表示されていることを確認する。
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_50")

        # 51 "児童扶養手当 資格管理画面": 「戻る」ボタン押下
        self.return_click()

        # 52 現況消込対象一覧: 表示
        self.screen_shot("現況消込対象一覧_52")

        # 53 現況消込対象一覧: 「登録」ボタン押下
        self.find_element(By.ID, "ChkIkkatsu_1").click()
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 54 現況消込対象一覧: 表示
        self.screen_shot("現況消込対象一覧_54")

        self.do_login()
        # 55 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_55")

        # 56 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 57 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_57")

        # 58 バッチ起動画面: "業務：児童 事業：児童扶養手当 処理区分：年次処理 処理分類：現況更新処理"
        self.form_input_by_id(idstr="GyomuSelect", text=pGyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=pJigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=pShoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=pShoriBunruiSelect)

        # 59 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_59")

        # 60 バッチ起動画面: 「現況届一括更新処理_資格更新」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_3124", ""))

        # 61 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_61")

       # 62 バッチ起動画面: 審査決定年月日「20230902」
        params = [
           {"title": "審査決定年月日", "type": "text", "value": pKetteiYMD}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_62")

        # 63 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 64 バッチ起動画面: 表示	メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_64")

        # 65 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 66 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_66")

        # 67 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 68 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_68")

        # 69 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # Assert: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_69")