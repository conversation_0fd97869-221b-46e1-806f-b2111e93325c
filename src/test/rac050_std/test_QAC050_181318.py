import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181318(FukushiSiteTestCaseBase):
    """TestQAC050_181318"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 現況届未提出で時効になる場合、資格喪失の登録ができることを確認する。
    def test_QAC050_181318(self):
        """資格喪失処理"""

        case_data = self.test_data["TestQAC050_181318"]

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: "業務：児童 事業：児童扶養手当 処理区分：年次 処理分類：現況時効者処理"
        self.select_by_id("GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.select_by_id("JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.select_by_id("ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.select_by_id("ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「現況届未提出時効者更新処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3163", ""))

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: "決定年月日「20231002」 消滅年月日「20231031」"
        params = [
            {"title": "決定年月日", "type": "text", "value": case_data.get("bungo_hyoji_kubun", "")},
            {"title": "消滅年月日", "type": "text", "value": case_data.get("shometsu_nengetsuhi", "")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示 メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # Assert: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_37")
