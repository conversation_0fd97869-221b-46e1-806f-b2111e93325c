import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180205(FukushiSiteTestCaseBase):
    """TestQAC050_180205"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180205"]
        super().setUp()

    # 受給資格者台帳送付依頼書等を出力できることを確認する。
    def test_QAC050_180205(self):
        """受給資格者台帳送付依頼書等作成"""

        case_data = self.test_data["TestQAC050_180205"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「受給者台帳送付依頼処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3190", ""))

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 発行年月日「20230401」文書番号「任意」申請年月日開始「20230401」申請年月日終了「20230401」出力順「証書番号順」
        params = [
            {"title": "発行年月日", "type": "text", "value": case_data.get("hak_kou_nen_gap_pi", "")},
            {"title": "文書番号", "type": "text", "value": "12345"},
            {"title": "申請年月日開始", "type": "text", "value": case_data.get("shin_sei_nen_gap_pi_kai_shi", "")},
            {"title": "申請年月日終了", "type": "text", "value": case_data.get("shin_sei_nen_gap_pi_shu_ryou", "")},
            {"title": "出力順", "type": "select", "value": case_data.get("shut_ryo_ku", "")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="受給者台帳送付依頼")

        # 19 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「受給者台帳送付依頼」のNoボタン押下
        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # 22 受給者台帳送付依頼（PDF）: 表示
        # 23 受給者台帳送付依頼（PDF）: ×ボタン押下でPDFを閉じる

        # 24 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_24")
