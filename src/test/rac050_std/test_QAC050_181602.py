import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181602(FukushiSiteTestCaseBase):
    """TestQAC050_181602"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181602"]
        super().setUp()

    # 過払金の情報を確認する。
    def test_QAC050_181602(self):
        """過払金額計算"""

        case_data = self.test_data["TestQAC050_181602"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「債権管理」ボタン押下
        self.saiken_kanri_click()  # Button with ID: CmdProcess7_2 instead of self.click_button_by_label("債権管理")

        # 3 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_3")

        # 4 債権履歴画面: 事業「児童扶養手当」選択絞り込み条件「返納中」チェック
        self.form_input_by_id(idstr="CmbGyomu", text=case_data.get("cmb_gyomu", ""))
        self.form_input_by_id(idstr="RadioC", value=case_data.get("radio_c", ""))
        self.screen_shot("債権履歴画面_4")

        # 5 債権履歴画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 6 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_6")

        # 7 債権履歴画面: 該当者一覧 「1」Noボタン押下
        # Instead of call self.click_batch_job_button_by_label("1") or self.click_button_by_label("該当者一覧 1No")
        atena_col = '3'
        table_idx = 0
        th_idx = 0
        tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
        for elem in tr_elem:
            table_idx += 1
            try:
                td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + atena_col + ")")
            except Exception:
                th_idx += 1
                continue
            if  atena_code == td_elem.text:
                table_idx = table_idx - th_idx
                self.click_by_id("Sel" + str(table_idx))
                break

        # 8 債権情報画面: 表示
        self.screen_shot("債権情報画面_8")

        # 9 債権情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 10 債権情報画面: 「計算」ボタン押下
        self.click_button_by_label("計算")
        self.click_button_by_label("計画作成")

        # 11 返済計画作成画面: 表示
        self.screen_shot("返済計画作成画面_11")

        # 12 返済計画作成画面: 「戻る」ボタン押下
        self.return_click()

        # 13 債権情報画面: 表示
        self.screen_shot("債権情報画面_13")

        # 14 債権情報画面: 「初期表示」ボタン押下
        self.click_button_by_label("初期表示")

        # 15 債権情報画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 16 債権情報画面: 返納状況一覧 「1」Noボタン押下
        self.click_button_by_label("1")  # Instead of self.click_button_by_label("返納状況一覧 1No")

        # 17 債権入金登録: 表示
        self.screen_shot("債権入金登録_17")

        # 18 債権入金登録: 「戻る」ボタン押下
        self.return_click()

        # 19 債権情報画面: 表示
        self.screen_shot("債権情報画面_19")

        # 20 債権情報画面: 「戻る」ボタン押下
        self.return_click()

        # 21 債権履歴画面: 表示
        self.screen_shot("債権履歴画面_21")

        # 22 債権履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 23 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_23")
