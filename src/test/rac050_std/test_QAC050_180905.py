import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180905(FukushiSiteTestCaseBase):
    """TestQAC050_180905"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180905"]
        super().setUp()

    # 支給停止関係届を提出した住民に対し、提出書類や所得情報等を確認する、
    def test_QAC050_180905(self):
        """支給停止関係審査"""

        case_data = self.test_data["TestQAC050_180905"]
        atena_codes = [case_data.get("atena_code_1", ""), case_data.get("atena_code_2", ""), case_data.get("atena_code_3", "")]

        for i,atena_code in enumerate(atena_codes):
            if i == 0: atena_no = "①"
            if i == 1: atena_no = "②"
            if i == 2: atena_no = "③"
            self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
            # self.do_login()
            # 1 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
            self.open_common_buttons_area()
            self.common_button_click("提出書類管理")

            # 2 提出書類管理: 表示
            self.screen_shot(f"提出書類管理_2_{atena_no}")

            # 3 提出書類管理: 「戻る」ボタン押下
            self.return_click()

            # 4 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_4_{atena_no}")

            # 5 児童扶養手当資格管理画面: 「所得判定詳細情報」ボタン押下
            self.open_common_buttons_area()
            self.click_button_by_label("所得判定詳細情報")

            # 6 所得判定詳細情報画面: 表示
            self.screen_shot(f"所得判定詳細情報画面_6_{atena_no}")

            # 7 所得判定詳細情報画面: 「戻る」ボタン押下
            self.return_click()

            # 8 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_8_{atena_no}")

            # # 9 児童扶養手当資格管理画面: 「被災状況」ボタン押下
            # self.click_button_by_label("被災状況")

            # # 10 被災状況画面: 表示

            # # 11 被災状況画面: 「戻る」ボタン押下
            # self.return_click()
            # self.screen_shot(f"被災状況画面_11_{atena_no}")

            # # 12 児童扶養手当資格管理画面: 表示
            self.screen_shot(f"児童扶養手当資格管理画面_12_{atena_no}")
