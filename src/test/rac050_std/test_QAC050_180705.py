import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180705(FukushiSiteTestCaseBase):
    """TestQAC050_180705"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180705"]
        super().setUp()

    # 未支払有無を、支払履歴画面で確認する。
    def test_QAC050_180705(self):
        """未支払請求審査"""

        case_data = self.test_data["TestQAC050_180705"]
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # self.click_button_by_label("修正")
        # self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230601")
        # self.form_input_by_id(idstr="TxtKetteiYMD", value="20230601")
        # self.form_input_by_id(idstr="TxtKaitei", value="20230601")
        # self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        # self.form_input_by_id(idstr="TxtShoushoBango", value="180701")
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました")

        # 1 児童扶養手当資格管理画面: 「支払履歴」ボタン押下
        self.click_button_by_label("支払履歴")

        # 2 支払履歴画面: 表示
        self.screen_shot("支払履歴画面_2")

        # 3 支払履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 4 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_4")

        # 5 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons_open_button().click()
        self.click_button_by_label("提出書類管理")

        # 6 提出書類管理画面: 表示
        self.screen_shot("提出書類管理画面_6")

        # 7 提出書類管理画面: 「戻る」ボタン押下
        self.return_click()

        # 8 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_8")
