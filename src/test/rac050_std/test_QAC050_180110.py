import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180110(FukushiSiteTestCaseBase):
    """TestQAC050_180110"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 所得状況届の未提出者に対して、所得状況未提出のお知らせを出力できることを確認する。
    def test_QAC050_180110(self):
        """所得状況届未提出のお知らせ作成"""

        case_data = self.test_data["TestQAC050_180110"]
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：年次処理分類：現況未提出者督促処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「現況届未提出督促対象者抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3141",""))

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 現況年度「令和５年」選択発行年月日「20231002]提出期限年月日「20231031」出力順「証書番号順」選択
        params = [
            {"title": "現況年度", "type": "select", "value": case_data.get("genkyo_nendo","")},
            {"title": "発行年月日", "type": "text", "value": case_data.get("hakko_nengetsuhi","")},
            {"title": "提出期限年月日", "type": "text", "value": case_data.get("teishutsu_kigen_nengetsuhi","")},
            {"title": "出力順", "type": "select", "value": case_data.get("shutsuryoku_jun","")},
            {"title": "宛名コード開始_テスト用", "type": "text", "value": case_data.get("atena_kodo_kaishi_tesuto_yo","")},
            {"title": "宛名コード終了_テスト用", "type": "text", "value": case_data.get("atena_kodo_shuryo_tesuto_yo","")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 19 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 帳票名「所得状況届未提出督促者一覧」のNoボタン押下

        # 21 所得状況届未提出督促者一覧（PDF）: 表示
        # self.screen_shot("所得状況届未提出督促者一覧（PDF）_21")

        # 22 所得状況届未提出督促者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 23 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_23")

        # 24 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 25 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_25")

        # 26 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 27 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_27")

        # 28 バッチ起動画面: 「現況届督促状出力処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3142",""))

        # 29 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_29")

        # 30 バッチ起動画面: 出力区分「0」開始頁「000001」終了頁「999999」
        params = [
            {"title": "出力区分", "type": "text", "value": case_data.get("shutsuryoku_kubun","")},
            {"title": "開始頁", "type": "text", "value": case_data.get("kaishi_peji","")},
            {"title": "終了頁", "type": "text", "value": case_data.get("shuryo_peji","")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_30")

        # 31 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 32 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_32")

        # 33 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 34 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_34")

        # 35 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 36 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_36")

        # 37 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_37")

        # 38 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 39 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_39")

        # 40 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 41 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_41")

        # 42 ジョブ帳票履歴画面: 帳票名「所得状況届督促状」のNoボタン押下

        # 43 所得状況届督促状（PDF）: 表示
        # self.screen_shot("所得状況届督促状（PDF）_43")

        # 44 所得状況届督促状（PDF）: ×ボタン押下でPDFを閉じる

        # 45 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_45")

        # 46 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 47 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_47")

        # 48 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 49 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_49")

        # 50 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：年次処理分類：現況未提出者再発行処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))

        # 51 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_51")

        # 52 バッチ起動画面: 「現況未提出者再発行_対象者一覧」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3131",""))

        # 53 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_53")

        # 54 バッチ起動画面: 現況年度「令和５年」選択出力順「証書番号順」選択
        params = [
            {"title": "現況年度", "type": "select", "value": case_data.get("genkyo_nendo","")},
            {"title": "出力順", "type": "select", "value": case_data.get("shutsuryoku_jun","")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_54")

        # 55 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 56 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_56")

        # 57 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 58 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_58")

        # 59 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 60 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_60")

        # 61 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_61")

        # 62 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 63 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_63")

        # 64 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)
        # 65 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_65")

        # 66 ジョブ帳票履歴画面: 帳票名「所得状況届対象者一覧」のNoボタン押下

        # 67 所得状況届対象者一覧（PDF）: 表示
        # self.screen_shot("所得状況届対象者一覧（PDF）_67")

        # 68 所得状況届対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 69 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_69")

        # 70 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 71 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_71")

        # 72 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 73 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_73")

        # 74 バッチ起動画面: 「現況未提出者再発行_お知らせ出力」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3134",""))

        # 75 バッチ起動画面: 開始通番「000001」終了通番「999999」現況年度「令和５年度」選択発行年月日「20230502」
        params = [
            {"title": "開始通番", "type": "text", "value": case_data.get("kaishi_tsuban","")},
            {"title": "終了通番", "type": "text", "value": case_data.get("shuryo_tsuban","")},
            {"title": "現況年度", "type": "select", "value": case_data.get("genkyo_nendo","")},
            {"title": "発行年月日", "type": "text", "value": case_data.get("hakko_nengetsuhi_1","")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_75")

        # 76 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 77 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_77")

        # 78 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 79 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_79")

        # 80 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 81 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_81")

        # 82 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_82")

        # 83 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 84 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_84")

        # 85 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 86 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_86")

        # 87 ジョブ帳票履歴画面: 帳票名「所得状況届お知らせ」のNoボタン押下

        # 88 所得状況届お知らせ（PDF）: 表示
        # self.screen_shot("所得状況届お知らせ（PDF）_88")

        # 89 所得状況届お知らせ（PDF）: ×ボタン押下でPDFを閉じる

        # 90 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_90")
