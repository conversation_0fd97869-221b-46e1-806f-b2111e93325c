import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_182003(FukushiSiteTestCaseBase):
    """TestQAC050_182003"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_182003"]
        super().setUp()

    # 支給区分が変わる対象者の差止情報を登録できることを確認する。
    def test_QAC050_182003(self):
        """支払差止処理"""

        case_data = self.test_data["TestQAC050_182003"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「差止情報」ボタン押下
        self.open_common_buttons_area()
        self.click_button_by_label("差止情報")

        # 3 差止情報画面: 表示
        self.screen_shot("差止情報画面_4")

        # 4 差止情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 5 差止情報画面: 差止事由「その他」選択支払差止決定日「20230602」支払差止開始年月「202306」
        self.form_input_by_id(idstr="CmbRiyu", text=case_data.get("cmb_riyu", ""))
        self.form_input_by_id(idstr="TxtKettei", value=case_data.get("txt_kettei", ""))
        self.form_input_by_id(idstr="TxtKaishi", value=case_data.get("txt_kaishi", ""))
        self.screen_shot("差止情報画面_6")

        # 6 差止情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 7 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_7")

        # 8 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 9 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 10 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("児童扶養手当資格管理画面_10")
