import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180109(FukushiSiteTestCaseBase):
    """TestQAC050_180109"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180109"]
        super().setUp()

    # 認定請求却下通知書を出力できることを確認する。
    def test_QAC050_180109(self):
        """認定請求却下通知書作成"""

        case_data = self.test_data["TestQAC050_180109"]
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")
        tsuuchisho_kubun = case_data.get("tsuuchisho_kubun", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面: 「認定請求却下通知書」行の印刷チェックボックス選択「認定請求却下通知書」行の発行年月日チェックボックス選択「認定請求却下通知書」行の発行年月日「20230502」
        exec_params = [
            {
                "report_name": report_name,
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                ]
            }
        ]
        ret = self.print_online_reports(case_name="オンライン", report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")

        # 5 帳票印刷画面: 認定請求却下通知書「ファイルを開く(O)」ボタンを押下

        # 6 認定請求却下通知書（PDF）: 表示
        # self.screen_shot("認定請求却下通知書（PDF）_6")

        # 7 認定請求却下通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 8 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 9 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_9")

        # 10 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_10")

        # 11 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 12 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_12")

        # 13 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))

        # 14 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_14")

        # 15 バッチ起動画面: 「決定一括出力処理_一覧」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3031", ""))

        # 16 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_16")

        # 17 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「2」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": case_data.get("kaishi_ketteibi", "")},
            {"title": "終了決定日", "type": "text", "value": case_data.get("shuryo_ketteibi", "")},
            {"title": "宛名コード", "type": "text", "value": case_data.get("atena_kodo", "")},
            {"title": "出力順", "type": "select", "value": case_data.get("shutsuryoku_jun", "")},
            {"title": "通知書区分", "type": "text", "value": tsuuchisho_kubun},
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 19 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_19")

        # 20 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 21 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_21")

        # 22 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 23 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_23")

        # 24 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 26 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_26")

        # 27 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="バッチ")

        # 28 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_28")

        # 29 ジョブ帳票履歴画面: 帳票名「通知書対象者一覧」のNoボタン押下

        # 30 通知書対象者一覧（PDF）: 表示
        # self.screen_shot("通知書対象者一覧（PDF）_30")

        # 31 通知書対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 32 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_32")

        # 33 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 34 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_34")

        # 35 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 36 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_36")

        # 37 バッチ起動画面: 「決定一括出力処理_通知書」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3032", ""))

        # 38 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_38")

        # 39 バッチ起動画面: 開始決定日「20230502」終了決定日「20230502」宛名コード「」出力順「証書番号順」選択通知書区分「2」発行年月日「20230502」
        params = [
            {"title": "開始決定日", "type": "text", "value": case_data.get("kaishi_ketteibi", "")},
            {"title": "終了決定日", "type": "text", "value": case_data.get("shuryo_ketteibi", "")},
            {"title": "宛名コード", "type": "text", "value": case_data.get("atena_kodo", "")},
            {"title": "出力順", "type": "select", "value": case_data.get("shutsuryoku_jun", "")},
            {"title": "通知書区分", "type": "text", "value": tsuuchisho_kubun},
            {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_39")

        # 40 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 41 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_41")

        # 42 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 43 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_43")

        # 44 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 45 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_45")

        # 46 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_46")

        # 47 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 48 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_48")

        # 49 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="バッチ")

        # 50 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_50")

        # 51 ジョブ帳票履歴画面: 「認定請求却下通知書」のNoボタン押下

        # 52 認定請求却下通知書（PDF）: 表示
        # self.screen_shot("認定請求却下通知書（PDF）_52")

        # 53 認定請求却下通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 54 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_54")
