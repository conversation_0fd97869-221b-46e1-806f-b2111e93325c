import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181801(FukushiSiteTestCaseBase):
    """TestQAC050_181801"""

    def setUp(self):
        # case_data = self.test_data["TestQAC050_181801"]
        # sql_params = {
        #     "TARGET_GYOUMU_CODE": "QAC050",
        #     "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
        #     "TARGET_NENDO":"2022"
        # }
        # self.exec_sqlfile("Test_QAC050_181801.sql", params=sql_params)
        super().setUp()

    # 年齢到達した児童を持つ受給者を抽出できることを確認する。
    def test_QAC050_181801(self):
        """年齢到達対象抽出"""

        case_data = self.test_data["TestQAC050_181801"]
        atena_code = case_data.get("atena_code", "")
        taisho_ym_start = case_data.get("taisho_ym_start", "")
        taisho_ym_end = case_data.get("taisho_ym_end", "")
        shutsuryoku_jun = case_data.get("shutsuryoku_jun", "")
        shinsei_shubetsu = case_data.get("shinsei_shubetsu", "")
        shinsei_shubetsu2 = case_data.get("shinsei_shubetsu2", "")
        shinsei_riyuu = case_data.get("shinsei_riyuu", "")
        shinsei_riyuu2 = case_data.get("shinsei_riyuu2", "")
        kaitei_ymd = case_data.get("kaitei_ymd", "")

        # =====資格登録=====
        # self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.click_button_by_label("申請内容入力")
        # self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        # self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        # self.click_button_by_label("確定")
        # self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        # self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        # self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        # self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        # self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        # self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("2")
        # self.form_input_by_id(idstr="CmbZokugara", text="子")
        # self.form_input_by_id(idstr="RdoDokyo1", value="1")
        # self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        # self.form_input_by_id(idstr="KojiGaitou2", value="1")
        # self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        # self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        # self.form_input_by_id(idstr="RdoShogai2", value="1")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("3")
        # self.form_input_by_id(idstr="CmbZokugara", text="子")
        # self.form_input_by_id(idstr="RdoDokyo1", value="1")
        # self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        # self.form_input_by_id(idstr="KojiGaitou2", value="1")
        # self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        # self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        # self.form_input_by_id(idstr="RdoShogai2", value="1")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("福祉世帯情報")
        # self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        # self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        # self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
        # self.form_input_by_id(idstr="ChkFlg_4", value="1")
        # self.form_input_by_id(idstr="HoninCmb_2", text="子")
        # self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        # self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20230501")
        # self.form_input_by_id(idstr="HoninCmb_3", text="子")
        # self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        # self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("口座情報")
        # self.click_button_by_label("追加")
        # self.entry_kouza_info(
        #     start_ymd=kaitei_ymd,
        #     ginko_code="0001",
        #     shiten_code="001",
        #     kouza_shubetsu_text="普通",
        #     kouza_bango="1234567",
        #     koukai=True
        # )
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.return_click()
        #
        # self.form_input_by_id(idstr="TxtKaitei", value="202305")
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        # if (can_shintatsu_button):
        #     self.click_button_by_label("本庁進達入力")
        #     self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
        #     self.click_button_by_label("月額計算")
        #     self.click_button_by_label("登録")
        #     self.alert_ok()
        #     self.click_button_by_label("本庁進達結果入力")
        #     self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        #     self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
        #     self.click_button_by_label("月額計算")
        #     self.click_button_by_label("登録")
        #     self.alert_ok()
        # self.click_button_by_label("決定内容入力")
        # self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        # self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
        # self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        # self.form_input_by_id(idstr="TxtShoushoBango", value="18180101")
        #
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        #
        # #支払履歴入力(支払済にする)
        # self.click_button_by_label("修正")
        # self.open_common_buttons_area()
        # self.click_button_by_label("支払履歴")
        # self.click_button_by_label("6")
        # self.click_button_by_label("修正")
        # self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        # self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        # self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        # self.click_button_by_label("最新口座")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        #
        # self.click_button_by_label("5")
        # self.click_button_by_label("修正")
        # self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        # self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        # self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        # self.click_button_by_label("最新口座")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        #
        # # =====額改定(減額)登録=====
        # self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.click_button_by_label("申請内容入力")
        # self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu2", ""))
        # self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("shinsei_riyuu2", ""))
        # self.click_button_by_label("確定")
        # self.form_input_by_id(idstr="TxtShinseiYMD", value="20230501")
        # self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230429")
        # self.form_input_by_id(idstr="TxtKaitei", value="202306")
        # self.click_by_id("CmdNo2")
        # self.form_input_by_id(idstr="TxtHiGaitoYMD", value="20230531")
        # self.form_input_by_id(idstr="CmbHiGaitoJiyu", text="施設入所")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("福祉世帯情報")
        # self.form_input_by_id(idstr="HiGaitoYMDtxt_3", value="20230531")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        # if (can_shintatsu_button):
        #     self.click_button_by_label("本庁進達入力")
        #     self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230501")
        #     self.click_button_by_label("月額計算")
        #     self.click_button_by_label("登録")
        #     self.alert_ok()
        #     self.click_button_by_label("本庁進達結果入力")
        #     self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230501")
        #     self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
        #     self.click_button_by_label("月額計算")
        #     self.click_button_by_label("登録")
        #     self.alert_ok()
        # self.click_button_by_label("決定内容入力")
        # self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230501")
        # self.form_input_by_id(idstr="TxtKetteiYMD", value="20230501")
        # self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        # self.form_input_by_id(idstr="TxtShoushoBango", value="18180102")
        #
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        #
        # #支払履歴入力(支払済にする)
        # self.click_button_by_label("修正")
        # self.open_common_buttons_area()
        # self.click_button_by_label("支払履歴")
        # self.click_button_by_label("4")
        # self.click_button_by_label("修正")
        # self.form_input_by_id(idstr="TbxPayDate", value="20230901")
        # self.form_input_by_id(idstr="TbxDecisionDate", value="20230901")
        # self.form_input_by_id(idstr="CmbPaymentMethod", text="窓口")
        # self.click_button_by_label("最新口座")
        # self.click_button_by_label("登録")
        # self.alert_ok()


        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：年齢到達処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「年齢到達抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3091", ""))

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 対象開始年月「202403」対象終了年月「202403」出力順「証書番号順」選択
        params = [
            {"title": "対象開始年月", "type": "text", "value": taisho_ym_start},
            {"title": "対象終了年月", "type": "text", "value": taisho_ym_end},
            {"title": "出力順", "type": "select", "value": shutsuryoku_jun}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 19 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「年齢到達者一覧（額改定）」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 年齢到達者一覧（額改定）（PDF）: 表示
        # self.screen_shot("年齢到達者一覧（額改定）（PDF）_22")

        # 23 年齢到達者一覧（額改定）（PDF）: ×ボタン押下でPDFを閉じる

        # 24 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_24")

        # 25 ジョブ帳票履歴画面: 「年齢到達者一覧（消滅）」のNoボタン押下
        # self.click_button_by_label("年齢到達者一覧（消滅）")

        # 26 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 27 年齢到達者一覧（消滅）（PDF）: 表示
        # self.screen_shot("年齢到達者一覧（消滅）（PDF）_27")

        # 28 年齢到達者一覧（消滅）（PDF）: ×ボタン押下でPDFを閉じる

        # 29 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_29")
