import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181408(FukushiSiteTestCaseBase):
    """TestQAC050_181408"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181408"]
        super().setUp()

    # 不足書類の提出日の登録ができることを確認する。
    def test_QAC050_181408(self):
        """受付情報入力"""

        case_data = self.test_data["TestQAC050_181408"]
        atena_code = case_data.get("atena_code", "")
        pChkTeisyutsu_1 = case_data.get("pChkTeisyutsu_1", "")
        pTxtSyoruiYMD_1 = case_data.get("pTxtSyoruiYMD_1", "")
        pChkFubi_1 = case_data.get("pChkFubi_1", "")
        pChkTeisyutsu_3 = case_data.get("pChkTeisyutsu_3", "")
        pTxtSyoruiYMD_3 = case_data.get("pTxtSyoruiYMD_3", "")
        pChkFubi_3 = case_data.get("pChkFubi_3", "")
        atena_code_2 = case_data.get("atena_code_2", "")

        # ②適用除外事由届が提出済、かつ書類不備なし、かつ適用除外審査で「適用」にする場合 -s
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="提出書類管理")

        # 3 提出書類管理: 表示
        self.screen_shot("1_提出書類管理_3")

        # 4 提出書類管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 5 提出書類管理: 戸籍謄抄本の未提出にチェックオン 戸籍謄抄本の提出日「20230902」 戸籍謄抄本の不備にチェックオフ 所得証明書の未提出にチェックオン 所得証明書の提出日「20230902」 所得証明書の不備にチェックオフ
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value=pChkTeisyutsu_1)
        self.form_input_by_id(idstr="TxtSyoruiYMD_1", value=pTxtSyoruiYMD_1)
        self.form_input_by_id(idstr="ChkFubi_1", value=pChkFubi_1)
        self.form_input_by_id(idstr="ChkTeisyutsu_3", value=pChkTeisyutsu_3)
        self.form_input_by_id(idstr="TxtSyoruiYMD_3", value=pTxtSyoruiYMD_3)
        self.form_input_by_id(idstr="ChkFubi_3", value=pChkFubi_3)
        self.screen_shot("1_提出書類管理_5")

        # 6 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 7 児童扶養手当資格管理画面: 表示
        self.screen_shot("1_児童扶養手当資格管理画面_7")

        # 8 児童扶養手当資格管理画面: 児童のNoボタン押下
        self.click_button_by_label(case_data.get("jidou_tsuika_no", ""))

        # 9 支給対象児童入力画面: 表示
        self.screen_shot("1_支給対象児童入力画面_9")

        # 10 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 11 児童扶養手当資格管理画面: 表示
        self.screen_shot("1_児童扶養手当資格管理画面_11")

        # 12 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 13 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 14 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("1_児童扶養手当資格管理画面_14")
        # ②適用除外事由届が提出済、かつ書類不備なし、かつ適用除外審査で「適用」にする場合 -e

        # ③適用除外事由届が提出済、かつ書類不備なし、かつ適用除外審査で「適用除外」にする場合 -s
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code_2, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 児童扶養手当資格管理画面: 「提出書類管理」ボタン押下
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="提出書類管理")

        # 3 提出書類管理: 表示
        self.screen_shot("2_提出書類管理_3")

        # 4 提出書類管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 5 提出書類管理: 戸籍謄抄本の未提出にチェックオン 戸籍謄抄本の提出日「20230902」 戸籍謄抄本の不備にチェックオフ 所得証明書の未提出にチェックオン 所得証明書の提出日「20230902」 所得証明書の不備にチェックオフ
        self.form_input_by_id(idstr="ChkTeisyutsu_1", value=pChkTeisyutsu_1)
        self.form_input_by_id(idstr="TxtSyoruiYMD_1", value=pTxtSyoruiYMD_1)
        self.form_input_by_id(idstr="ChkFubi_1", value=pChkFubi_1)
        self.form_input_by_id(idstr="ChkTeisyutsu_3", value=pChkTeisyutsu_3)
        self.form_input_by_id(idstr="TxtSyoruiYMD_3", value=pTxtSyoruiYMD_3)
        self.form_input_by_id(idstr="ChkFubi_3", value=pChkFubi_3)
        self.screen_shot("2_提出書類管理_5")

        # 6 提出書類管理: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 7 児童扶養手当資格管理画面: 表示
        self.screen_shot("2_児童扶養手当資格管理画面_7")

        # 8 児童扶養手当資格管理画面: 児童のNoボタン押下
        self.click_button_by_label(case_data.get("jidou_tsuika_no", ""))

        # 9 支給対象児童入力画面: 表示
        self.screen_shot("2_支給対象児童入力画面_9")

        # 10 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 11 児童扶養手当資格管理画面: 表示
        self.screen_shot("2_児童扶養手当資格管理画面_11")

        # 12 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 13 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 14 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("2_児童扶養手当資格管理画面_14")
        # ③適用除外事由届が提出済、かつ書類不備なし、かつ適用除外審査で「適用除外」にする場合 -e