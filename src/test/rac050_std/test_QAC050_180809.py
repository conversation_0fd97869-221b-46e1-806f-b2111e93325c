import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180809(FukushiSiteTestCaseBase):
    """TestQAC050_180809"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180809"]
        super().setUp()

    # 児童扶養手当証書を出力できることを確認する。
    def test_QAC050_180809(self):
        """証書等作成"""

        case_data = self.test_data["TestQAC050_180809"]
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")
        date_start = case_data.get("date_start", "")
        date_end = case_data.get("date_end", "")
        address_code = case_data.get("address_code", "")
        output_order = case_data.get("output_order", "")
        option_select = case_data.get("option_select", "")
        date_release = case_data.get("date_release", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面:
        # 「児童扶養手当証書」行の印刷チェックボックス選択
        # 「児童扶養手当証書」行の発行年月日チェックボックス選択
        # 「児童扶養手当証書」行の発行年月日「20230602」
        exec_params = [
            {
                "report_name": report_name,
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"},
                ]
            }
        ]
        ret = self.print_online_reports(case_name="オンライン",report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_3")
        self.assert_message_area("プレビューを表示しました")

        # 4 帳票印刷画面: 「印刷」ボタン押下

        # 5 帳票印刷画面: 児童扶養手当証書「ファイルを開く(O)」ボタンを押下

        # 6 児童扶養手当証書（PDF）: 表示
        #self.screen_shot("児童扶養手当証書（PDF）_6")

        # 7 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # TODO I cannot check the back side of the PDF
        #self.screen_shot("児童扶養手当証書（PDF）_7")

        # 8 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab with ID: 児童扶養手当証書（PDF）

        # 9 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 10 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_10")

        # 11 メインメニュー画面: 表示
        self.do_login() 
        self.screen_shot("メインメニュー画面_11")

        # 12 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 13 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_13")

        # 14 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))
        
        # 15 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_15")

        # 16 バッチ起動画面: 「児童扶養手当証書受領書出力」のNoボタン押下
        # self.click_batch_job_button_by_label("児童扶養手当証書受領書出力")  # Instead of self.click_by_id("Sel1")

        # 17 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 開始決定日「20230602」終了決定日「20230602」宛名コード「」出力順「証書番号順」選択通知書区分「5」発行年月日「20230602」
        # TODO: ID in spec is No17のため、NG
        # params = [
        #     {"title": "開始決定日", "type": "text", "value": date_start},
        #     {"title": "終了決定日", "type": "text", "value": date_end},
        #     {"title": "宛名コード", "type": "text", "value": address_code},
        #     {"title": "出力順", "type": "text", "value": output_order},
        #     {"title": "選択通知書区分", "type": "text", "value": option_select},
        #     {"title": "発行年月日", "type": "text", "value": date_release}
        # ]
        # self.set_job_params(params)
        # self.screen_shot("バッチ起動画面_18")

        # 19 バッチ起動画面: 「処理開始」ボタン押下
        # exec_datetime = self.exec_batch_job()  # Button with ID: ExecuteButton

        # 20 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        # self.assert_message_base_header("ジョブを起動しました")
        # self.screen_shot("バッチ起動画面_20")

        # 21 バッチ起動画面: 「実行履歴」ボタン押下
        # self.click_job_exec_log()  # Button with ID: JobListButton

        # 22 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_22")

        # 23 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton
        # self.wait_job_finished(120,20)
        # self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 24 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # TODO unknown how to code this case
        # self.screen_shot("ジョブ実行履歴画面_25")

        # 26 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        # self.click_report_log()  # Button with ID: ReportListButton

        # 27 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_29")

        # # 28 ジョブ帳票履歴画面: 「検索」ボタン押下
        # self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 29 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_29")

        # 30 ジョブ帳票履歴画面: 「児童扶養手当証書受領書」のNoボタン押下

        # 31 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 32 児童扶養手当証書受領書（PDF）: 表示
        #self.screen_shot("児童扶養手当証書受領書（PDF）_32")

        # 33 児童扶養手当証書受領書（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab
        #self.screen_shot("児童扶養手当証書受領書（PDF）_33")

        # 34 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_34")

        # 35 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        # self.click_job_list()  # Button with ID: ExecListButton

        # 36 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_36")

        # 37 バッチ起動画面: 「処理一覧」ボタン押下
        # self.click_job_exec_log_search()  # Button with ID: ExecListButton

        # 38 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_38")
        
        # 39 バッチ起動画面: 「児童扶養手当証書一括出力」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3030", ""))

        # 40 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_40")

        # 41 バッチ起動画面: 開始決定日「20230602」終了決定日「20230602」宛名コード「」出力順「証書番号順」選択通知書区分「5」発行年月日「20230602」
        params = [
            {"title": "開始決定日", "type": "text", "value": date_start},
            {"title": "終了決定日", "type": "text", "value": date_end},
            {"title": "宛名コード", "type": "text", "value": address_code},
            {"title": "出力順", "type": "select", "value": output_order},
            {"title": "通知書区分", "type": "text", "value": option_select},
            {"title": "発行年月日", "type": "text", "value": date_release}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_41")

        # 42 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()  # Button with ID: ExecuteButton

        # 43 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_43")

        # 44 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()  # Button with ID: JobListButton

        # 45 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_45")

        # 46 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 47 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_47")

        # 48 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # TODO unknown how to code this case
        self.screen_shot("ジョブ実行履歴画面_48")

        # 49 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()  # Button with ID: ReportListButton

        # 50 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_50")

        # 51 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 52 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_52")

        # 53 ジョブ帳票履歴画面: 「児童扶養手当証書」のNoボタン押下

        # 54 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 55 児童扶養手当証書（PDF）: 表示
        #self.screen_shot("児童扶養手当証書（PDF）_55")

        # 56 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # TODO I cannot check the back side of the PDF
        #self.screen_shot("児童扶養手当証書（PDF）_56")

        # 57 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる
        # TODO: I don't know which function to close the pdf tab

        # 58 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()  # Button with ID: ExecListButton

        # 59 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_59")
