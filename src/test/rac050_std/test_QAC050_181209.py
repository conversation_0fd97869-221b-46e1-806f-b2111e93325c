import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181209(FukushiSiteTestCaseBase):
    """TestQAC050_181209"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181209"]
        # sql_params = {"TARGET_GYOUMU_CODE": "QAC050", "DELETE_ATENA_CODE": case_data.get("atena_code", "")}
        # self.exec_sqlfile("Test_QAC050_181209.sql", params=sql_params)
        super().setUp()

    # 障害等認定審査で障害等に該当しない場合、額改定（減額）の登録ができることを確認する。
    def test_QAC050_181209(self):
        """額改定処理"""

        case_data = self.test_data["TestQAC050_181209"]
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")
        self.wait_page_loaded(wait_timeout=10)

        # 2 児童扶養手当資格管理画面: 申請種別「額改定（減額）」申請理由「児童の障害非該当」
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("shinsei_riyu", ""))
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 4 児童扶養手当資格管理画面: 申請年月日「20230701」事由発生年月日「20230630」改定年月「202307」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("txt_shinsei_ymd", ""))
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=case_data.get("txt_jiyu_hassei_ymd", ""))
        self.form_input_by_id(idstr="TxtKaitei", value=case_data.get("txt_kaitei", ""))
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=case_data.get("txtpre_shikyu_kaishi_ymd", ""))

        self.screen_shot("児童扶養手当資格管理画面_4")

        # 5 児童扶養手当資格管理画面: 児童「1」Noボタン押下
        self.click_button_by_label(case_data.get("jidou_tsuika_no", ""))

        # 6 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_6")

        # 7 支給対象児童入力画面: 非該当年月日「20230630」非該当事由「対象児童障害非該当」選択
        self.form_input_by_id(idstr="TxtHiGaitoYMD", value=case_data.get("txt_hi_gaito_ymd", ""))
        self.form_input_by_id(idstr="CmbHiGaitoJiyu", text=case_data.get("cmb_hi_gaito_jiyu", ""))
        self.screen_shot("支給対象児童入力画面_7")

        # 8 支給対象児童入力画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 9 児童扶養手当資格管理画面: 「公的年金等停止額」ボタン押下
        self.click_button_by_label("公的年金等停止額")

        # 10 年金登録確認画面: 表示
        self.screen_shot("年金登録確認画面_10")

        # 11 年金登録確認画面: 「戻る」ボタン押下
        self.return_click()

        # 12 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_12")

        # 13 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 14 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 15 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_15")

        # 進達入力
        can_shintatsu_button = self.click_button_by_label(case_data.get("shintatsu_button", ""))
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value=case_data.get("txt_shintatsu1_ymd", ""))
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.wait_page_loaded(wait_timeout=10)
            self.assert_message_area("登録しました")
            self.click_button_by_label(case_data.get("shintatsu_kekka_button", ""))
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=case_data.get("shintatsu1_hantei_cmb", ""))
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.wait_page_loaded(wait_timeout=10)
            self.assert_message_area("登録しました")

        # 16 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 17 児童扶養手当資格管理画面: 決定年月日「20230702」決定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=case_data.get("txt_kettei_ymd", ""))
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=case_data.get("kettei_kekka_cmb", ""))
        self.screen_shot("児童扶養手当資格管理画面_17")

        # 18 児童扶養手当資格管理画面: 児童「1」Noボタン押下
        self.click_button_by_label(case_data.get("jidou_tsuika_no", ""))

        # 19 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_19")

        # 20 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 21 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_21")

        # 22 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 23 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 24 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_24")
