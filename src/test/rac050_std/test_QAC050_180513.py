import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180513(FukushiSiteTestCaseBase):
    """TestQAC050_180513"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180513"]
        super().setUp()

    # 内払い決定通知書を出力できることを確認する。
    def test_QAC050_180513(self):
        """内払調整結果通知書作成"""

        case_data = self.test_data["TestQAC050_180513"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 1 児童扶養手当資格管理画面: 「支払調整履歴」ボタン押下
        self.open_common_buttons_area()
        self.common_button_click("支払調整履歴")

        # 2 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_2")

        # 3 支払調整履歴画面: 該当者一覧 「1」Noボタン押下
        self.click_button_by_label("1")

        # 4 支払調整履歴画面: 過払年月日「20230710」発行年月日「20230702」
        # self.screen_shot("支払調整履歴画面_5")
        # NG -> no ID in spec
        self.form_input_by_id(idstr="TxtKabaraiYMD", value=case_data.get("txt_kabarai_ymd", ""))
        self.form_input_by_id(idstr="TxtHakkouYMD", value=case_data.get("txt_hakkou_ymd", ""))
        self.screen_shot("支払調整履歴画面_4")

        # 5 支払調整履歴画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")
        #self.exec_qsn_online_print(case_name="内払い決定通知書")
        self.pdf_output_and_download_no_alert(button_id="CmdInnsatuBTN", case_name="支払調整登録")

        # 6 支払調整履歴画面: 内払い決定通知書「ファイルを開く(O)」ボタンを押下
        # self.print_online_reports(case_name="ケース名")

        # 7 内払い決定通知書（PDF）: 表示
        # self.screen_shot("内払い決定通知書（PDF）_8")

        # 8 内払い決定通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 9 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_9")

        # 10 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 11 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_11")
