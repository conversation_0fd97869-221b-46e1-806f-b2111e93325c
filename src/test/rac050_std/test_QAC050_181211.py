import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181211(FukushiSiteTestCaseBase):
    """TestQAC050_181211"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181211"]
        # sql_params = {"TARGET_GYOUMU_CODE": "QAC050", "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
        #               "TARGET_NENDO":case_data.get("nendo", ""),"TARGET_SOUSYOTOKU":case_data.get("soushotoku", "")}
        # self.exec_sqlfile("Test_QAC050_181211.sql", params=sql_params)
        super().setUp()

    # 障害等認定審査で障害等に該当しない場合、資格喪失の登録ができることを確認する。
    def test_QAC050_181211(self):
        """資格喪失処理"""

        case_data = self.test_data["TestQAC050_181211"]
        atena_code = case_data.get("atena_code", "")

        # # =====資格登録=====
        # self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.click_button_by_label("申請内容入力")
        # self.form_input_by_id(idstr="ShinseiShubetsuCmb", text="認定請求")
        # self.form_input_by_id(idstr="ShinseiRiyuuCmb", text="離婚")
        # self.click_button_by_label("確定")
        # self.form_input_by_id(idstr="TxtShinseiYMD", value="20230401")
        # self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        # self.form_input_by_id(idstr="JyukyusyaKbnCmb", text="父または母")
        # self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value="20230325")
        # self.form_input_by_id(idstr="TorikimeariRBtn", value="1")
        # self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value="20230501")
        # # 一人目障害ありの児童追加
        # self.click_button_by_label("児童追加")
        # self.click_button_by_label("3")
        # self.form_input_by_id(idstr="CmbZokugara", text="子")
        # self.form_input_by_id(idstr="RdoDokyo1", value="1")
        # self.form_input_by_id(idstr="TxtKangoYMD", value="20230325")
        # self.form_input_by_id(idstr="KojiGaitou2", value="1")
        # self.form_input_by_id(idstr="TxtGaitoYMD", value="20230501")
        # self.form_input_by_id(idstr="CmbGaitoJiyu", text="離婚")
        # self.form_input_by_id(idstr="TxtJiyuYMD", value="20230325")
        # self.form_input_by_id(idstr="TxtToushoShikyuYM", value="20230501")
        # self.form_input_by_id(idstr="RdoShogai1", value="1")
        # self.form_input_by_id(idstr="CmbShinsaKekka", text="継続支給")
        # self.form_input_by_id(idstr="TxtShougaiNinteiKaishiYMD", value="20230501")
        # self.form_input_by_id(idstr="TxtShindanshouSakuseiYMD", value="20230501")
        # self.form_input_by_id(idstr="TxtJikaiShindanshouTeisyutsuKigenYMD", value="20240501")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("福祉世帯情報")
        # self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        # self.form_input_by_id(idstr="JukyuCmb_1", text="本人（父または母）")
        # self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20230501")
        # self.form_input_by_id(idstr="ChkFlg_2", value="1")
        # self.form_input_by_id(idstr="HoninCmb_3", text="子")
        # self.form_input_by_id(idstr="JukyuCmb_3", text="対象児童")
        # self.form_input_by_id(idstr="GaitoYMDtxt_3", value="20230501")
        # self.form_input_by_id(idstr="HoninCmb_4", text="子")
        # self.form_input_by_id(idstr="JukyuCmb_4", text="対象児童")
        # self.form_input_by_id(idstr="GaitoYMDtxt_4", value="20230501")
        # self.click_button_by_label("入力完了")
        # self.click_button_by_label("所得情報")
        # self.return_click()
        # self.find_common_buttons_open_button().click()
        # self.click_button_by_label("口座情報")
        # self.click_button_by_label("追加")
        # self.entry_kouza_info(
        #     start_ymd='20230501',
        #     ginko_code="0001",
        #     shiten_code="001",
        #     kouza_shubetsu_text="普通",
        #     kouza_bango="1234567",
        #     koukai=True
        # )
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.return_click()
        # self.form_input_by_id(idstr="TxtKaitei", value="202305")
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        # if (can_shintatsu_button):
        #     self.click_button_by_label("本庁進達入力")
        #     self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230401")
        #     self.click_button_by_label("月額計算")
        #     self.click_button_by_label("登録")
        #     self.alert_ok()
        #     self.assert_message_area("登録しました")
        #     self.click_button_by_label("本庁進達結果入力")
        #     self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        #     self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
        #     self.click_button_by_label("月額計算")
        #     self.click_button_by_label("登録")
        #     self.alert_ok()
        #     self.assert_message_area("登録しました")
        # self.click_button_by_label("決定内容入力")
        # self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230401")
        # self.form_input_by_id(idstr="TxtKetteiYMD", value="20230401")
        # self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        # self.form_input_by_id(idstr="TxtNinteiBango", value="1812493")
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()
        # self.assert_message_area("登録しました")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

        # 1 児童扶養手当資格管理画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")
        self.wait_page_loaded(wait_timeout=10)

        # 2 児童扶養手当資格管理画面: 申請種別「資格喪失」申請理由「児童の障害非該当」
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("shinsei_riyu", ""))
        self.screen_shot("児童扶養手当資格管理画面_2")

        # 3 児童扶養手当資格管理画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 4 児童扶養手当資格管理画面: 申請年月日「20230701」事由発生年月日「20230630」消滅年月日「20230630」
        # NG 消滅年月日
        self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("txt_shinsei_ymd_1", ""))
        self.form_input_by_id(idstr="TxtJiyuHasseiYMD", value=case_data.get("txt_jiyu_hassei_ymd_1", ""))
        self.form_input_by_id(idstr="TxtpreShikyuKaishiYMD", value=case_data.get("txtpre_shikyu_kaishi_ymd_1", ""))
        self.screen_shot("児童扶養手当資格管理画面_4")

        # 5 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 6 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 7 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_7")

        # 8 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        can_shintatsu_button = self.click_button_by_label(case_data.get("shintatsu_button", ""))
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value=case_data.get("txt_shintatsu1_ymd", ""))
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.wait_page_loaded(wait_timeout=10)
            self.assert_message_area("登録しました")
            self.click_button_by_label(case_data.get("shintatsu_kekka_button", ""))
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=case_data.get("shintatsu1_hantei_cmb", ""))
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.wait_page_loaded(wait_timeout=10)
            self.assert_message_area("登録しました")

        self.click_button_by_label("決定内容入力")

        # 9 児童扶養手当資格管理画面: 決定年月日「20230702」決定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=case_data.get("txt_kettei_ymd_1", ""))
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=case_data.get("kettei_kekka_cmb_1", ""))
        self.screen_shot("児童扶養手当資格管理画面_9")

        # 10 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 11 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 12 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_12")
