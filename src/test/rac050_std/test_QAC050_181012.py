import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181012(FukushiSiteTestCaseBase):
    """TestQAC050_181012"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181012"]
        super().setUp()

    # 支給停止解除通知書（年金版）を出力できることを確認する。
    def test_QAC050_181012(self):
        """支給停止解除通知書等作成"""

        case_data = self.test_data["TestQAC050_181012"]
        atena_code = case_data.get("atena_code", "")
        form_name_0 = case_data.get("form_name_0", "")
        form_name_1 = case_data.get("form_name_1", "")
        hakkouYMD = case_data.get("hakkouYMD", "")
        gyomuSelect = case_data.get("gyomuSelect", "")
        jigyoSelect = case_data.get("jigyoSelect", "")
        shoriKubunSelect = case_data.get("shoriKubunSelect", "")
        shoriBunruiSelect = case_data.get("shoriBunruiSelect", "")
        kaishiketteiYMD = case_data.get("kaishiketteiYMD", "")
        shuryoketteiYMD = case_data.get("shuryoketteiYMD", "")
        atenaCD = case_data.get("atenaCD", "")
        output_order = case_data.get("output_order", "")
        noti_category_5 = case_data.get("noti_category_5", "")
        hakkounengappi = case_data.get("hakkounengappi", "")
        kaishiketteiYMD_2 = case_data.get("kaishiketteiYMD_2", "")
        shuryoketteiYMD_2 = case_data.get("shuryoketteiYMD_2", "")
        atenaCD_2 = case_data.get("atenaCD_2", "")
        output_order_2 = case_data.get("output_order_2", "")
        noti_category_5_2 = case_data.get("noti_category_5_2", "")
        hakkounengappi_2 = case_data.get("hakkounengappi_2", "")
        kaishiketteiYMD_3 = case_data.get("kaishiketteiYMD_3", "")
        shuryoketteiYMD_3 = case_data.get("shuryoketteiYMD_3", "")
        atenaCD_3 = case_data.get("atenaCD_3", "")
        output_order_3 = case_data.get("output_order_3", "")
        noti_category_5_3 = case_data.get("noti_category_5_3", "")
        hakkounengappi_3 = case_data.get("hakkounengappi_3", "")
        kaishiketteiYMD_4 = case_data.get("kaishiketteiYMD_4", "")
        shuryoketteiYMD_4 = case_data.get("shuryoketteiYMD_4", "")
        atenaCD_4 = case_data.get("atenaCD_4", "")
        output_order_4 = case_data.get("output_order_4", "")
        noti_category_5_4 = case_data.get("noti_category_5_4", "")
        hakkounengappi_4 = case_data.get("hakkounengappi_4", "")
        preShinseiShubetsuCmb_1 = case_data.get("preShinseiShubetsuCmb_1", "")
        preShinseiRiyuuCmb_1 = case_data.get("preShinseiRiyuuCmb_1", "")
        preTxtShinseiYMD_1 = case_data.get("preTxtShinseiYMD_1", "")
        preTxtKaitei_1 = case_data.get("preTxtKaitei_1", "")
        preTxtShintatsu1YMD_1 = case_data.get("preTxtShintatsu1YMD_1", "")
        preShintatsu1HanteiCmb_1 = case_data.get("preShintatsu1HanteiCmb_1", "")
        preTxtKetteiYMD_1 = case_data.get("preTxtKetteiYMD_1", "")
        preKetteiKekkaCmb_1 = case_data.get("preKetteiKekkaCmb_1", "")
        preShinseiShubetsuCmb_2 = case_data.get("preShinseiShubetsuCmb_2", "")
        preShinseiRiyuuCmb_2 = case_data.get("preShinseiRiyuuCmb_2", "")
        preTxtShinseiYMD_2 = case_data.get("preTxtShinseiYMD_2", "")
        preTxtShoushoKoufuYMD_2 = case_data.get("preTxtShoushoKoufuYMD_2", "")
        preTxtShoushoHenpuYMD_2 = case_data.get("preTxtShoushoHenpuYMD_2", "")
        preTxtKaitei_2 = case_data.get("preTxtKaitei_2", "")
        preTxtShintatsu1YMD_2 = case_data.get("preTxtShintatsu1YMD_2", "")
        nenkinSagakuChkBox = case_data.get("nenkinSagakuChkBox", "")
        preShintatsu1HanteiCmb_2 = case_data.get("preShintatsu1HanteiCmb_2", "")
        preTxtKetteiYMD_2 = case_data.get("preTxtKetteiYMD_2", "")
        preKetteiKekkaCmb_2 = case_data.get("preKetteiKekkaCmb_2", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")

#        # 支給停止（申請種別：支給停止事由変更、申請理由：全額停止）と支給解除（申請種別：変更、申請理由：年金受給終了）する履歴を作成しておく
#        self.click_button_by_label("申請内容入力")
#        # 申請種別
#        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_1)
#        # 申請理由
#        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_1)
#        self.click_button_by_label("確定")
#        # 請求年月日
#        self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_1)
#        # 改定年月
#        self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_1)
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("本庁進達入力")
#        # 進達年月日
#        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_1)
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("本庁進達結果入力")
#        # 進達結果
#        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_1)
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("決定内容入力")
#        # 決定年月日
#        self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_1)
#        # 決定結果
#        self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_1)
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("申請内容入力")
#        # 申請種別
#        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=preShinseiShubetsuCmb_2)
#        # 申請理由
#        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=preShinseiRiyuuCmb_2)
#        self.click_button_by_label("確定")
#        # 請求年月日
#        self.form_input_by_id(idstr="TxtShinseiYMD", value=preTxtShinseiYMD_2)
#        # 証書交付年月日
#        self.form_input_by_id(idstr="TxtShoushoKoufuYMD", value=preTxtShoushoKoufuYMD_2)
#        # 証書返付年月日
#        self.form_input_by_id(idstr="TxtShoushoHenpuYMD", value=preTxtShoushoHenpuYMD_2)
#        # 改定年月
#        self.form_input_by_id(idstr="TxtKaitei", value=preTxtKaitei_2)
#        # 公的年金等停止額チェック
#        self.form_input_by_id(idstr="NenkinSagakuChkBox", value=nenkinSagakuChkBox)
#        self.click_button_by_label("公的年金等停止額")
#        self.click_button_by_label("追加")
#        self.click_button_by_label("計算")
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("本庁進達入力")
#        # 進達年月日
#        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=preTxtShintatsu1YMD_2)
#        # 公的年金等停止額チェック
#        self.form_input_by_id(idstr="NenkinSagakuChkBox", value=nenkinSagakuChkBox)
#        self.click_button_by_label("公的年金等停止額")
#        self.click_button_by_label("修正")
#        self.click_button_by_label("計算")
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("本庁進達結果入力")
#        # 進達結果
#        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=preShintatsu1HanteiCmb_2)
#        # 公的年金等停止額チェック
#        self.form_input_by_id(idstr="NenkinSagakuChkBox", value=nenkinSagakuChkBox)
#        self.click_button_by_label("公的年金等停止額")
#        self.click_button_by_label("修正")
#        self.click_button_by_label("計算")
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#        self.click_button_by_label("決定内容入力")
#        # 決定年月日
#        self.form_input_by_id(idstr="TxtKetteiYMD", value=preTxtKetteiYMD_2)
#        # 決定結果
#        self.form_input_by_id(idstr="KetteiKekkaCmb", text=preKetteiKekkaCmb_2)
#        # 公的年金等停止額チェック
#        self.form_input_by_id(idstr="NenkinSagakuChkBox", value=nenkinSagakuChkBox)
#        self.click_button_by_label("公的年金等停止額")
#        self.click_button_by_label("修正")
#        self.click_button_by_label("計算")
#        self.click_button_by_label("入力完了")
#        self.click_button_by_label("月額計算")
#        self.click_button_by_label("登録")
#        self.alert_ok()
#        self.assert_message_area("登録しました。")
#
        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面1_2")

        # 3 帳票印刷画面: 「支給停止解除通知書」行の印刷チェックボックス選択「支給停止解除通知書」行の発行年月日チェックボックス選択「支給停止解除通知書」行の発行年月日「20230602」
        exec_params = [
            {
                "report_name": form_name_0,
                "params":[
                    {"title": "発行年月日", "value":hakkouYMD,"is_no_print":"1"}
                ]
            }
        ]
        self.print_online_reports(report_param_list=exec_params, case_name="帳票印刷画面")
        self.screen_shot("帳票印刷画面1_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")

        # 5 帳票印刷画面: 支給停止解除通知書「ファイルを開く(O)」ボタンを押下

        # 6 支給停止解除通知書（PDF）: 表示
        # self.screen_shot("支給停止解除通知書（PDF）_6")

        # 7 支給停止解除通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("支給停止解除通知書（PDF）_7")

        # 8 支給停止解除通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 9 帳票印刷画面: 児童扶養手当証書「ファイルを開く(O)」ボタンを押下

        # 10 児童扶養手当証書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書（PDF）_10")

        # 11 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当証書（PDF）_11")

        # 12 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        self.return_click()

        # 1 児童扶養手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面2_2")

        # 3 帳票印刷画面: 「児童扶養手当証書」行の印刷チェックボックス選択「児童扶養手当証書」行の発行年月日チェックボックス選択「児童扶養手当証書」行の発行年月日「20230602」
        exec_params = [
            {
                "report_name": form_name_1,
                "params":[
                    {"title": "発行年月日", "value":hakkouYMD,"is_no_print":"1"}
                ]
            }
        ]
        self.print_online_reports(report_param_list=exec_params, case_name="帳票印刷画面")
        self.screen_shot("帳票印刷画面2_3")

        # 13 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 14 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_14")

        # 15 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_15")

        # 16 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 17 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：通知書一括処理
        self.form_input_by_id(idstr="GyomuSelect", text=gyomuSelect)
        self.form_input_by_id(idstr="JigyoSelect", text=jigyoSelect)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=shoriKubunSelect)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=shoriBunruiSelect)

        # 19 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_19")

#③出荷 -s
#        # 20 バッチ起動画面: 「児童扶養手当証書受領書出力」のNoボタン押下
#        self.click_batch_job_button_by_label("児童扶養手当証書受領書出力")
#
#        # 21 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_21")
#
#        # 22 バッチ起動画面: 開始決定日「20230602」終了決定日「20230602」宛名コード「」出力順「証書番号順」選択通知書区分「4」発行年月日「20230602」
#        params = [
#            {"title": "開始決定日", "type": "text", "value": kaishiketteiYMD},
#            {"title": "終了決定日", "type": "text", "value": shuryoketteiYMD},
#            {"title": "宛名コード", "type": "text", "value": atenaCD},
#            {"title": "出力順", "type": "select", "value": output_order},
#            {"title": "通知書区分", "type": "text", "value": noti_category_5},
#            {"title": "発行年月日", "type": "text", "value": hakkounengappi}
#        ]
#        self.set_job_params(params)
#        self.screen_shot("バッチ起動画面_22")
#        # 23 バッチ起動画面: 「処理開始」ボタン押下
#        exec_datetime = self.exec_batch_job()
#        # 24 バッチ起動画面: 表示
#        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
#        self.assert_message_base_header("ジョブを起動しました")
#        self.screen_shot("バッチ起動画面_24")
#        # 25 バッチ起動画面: 「実行履歴」ボタン押下
#        self.click_job_exec_log()
#        # 26 ジョブ実行履歴画面: 表示
#        self.screen_shot("ジョブ実行履歴画面_26")
#        # 27 ジョブ実行履歴画面: 「検索」ボタン押下
#        self.wait_job_finished(120,20)
#        self.assert_job_normal_end(exec_datetime=exec_datetime)
#        # 28 ジョブ実行履歴画面: 表示
#        # self.screen_shot("ジョブ実行履歴画面_28")
#        # 29 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
#        self.screen_shot("ジョブ実行履歴画面_31")
#        # 30 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
#        self.click_report_log()
#        # 31 ジョブ帳票履歴画面: 表示
#        self.screen_shot("ジョブ帳票履歴画面_31")
#        # 32 ジョブ帳票履歴画面: 「検索」ボタン押下
#        self.get_job_report_pdf(exec_datetime=exec_datetime)
#        # 33 ジョブ帳票履歴画面: 表示
#        # self.screen_shot("ジョブ帳票履歴画面_33")
#        # 34 ジョブ帳票履歴画面: 「児童扶養手当証書受領書」のNoボタン押下
#        # 35 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
#        # 36 児童扶養手当証書受領書（PDF）: 表示
#        # self.screen_shot("児童扶養手当証書受領書（PDF）_36")
#        # 37 児童扶養手当証書受領書（PDF）: ×ボタン押下でPDFを閉じる
#        # self.screen_shot("児童扶養手当証書受領書（PDF）_37")
#        # 38 ジョブ帳票履歴画面: 表示
#        self.screen_shot("ジョブ帳票履歴画面_38")
#        # 39 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
#        self.click_job_list()
#        # 40 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_40")
#        # 41 バッチ起動画面: 「処理一覧」ボタン押下
#        self.click_job_exec_log_search()
#        # 42 バッチ起動画面: 表示
#        self.screen_shot("バッチ起動画面_42")
#③出荷 -e

        # 43 バッチ起動画面: 「児童扶養手当証書一括出力」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3030", ""))

        # 44 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_44")

        # 45 バッチ起動画面: 開始決定日「20230602」終了決定日「20230602」宛名コード「」出力順「証書番号順」選択通知書区分「4」発行年月日「20230602」
        params = [
            {"title": "開始決定日", "type": "text", "value": kaishiketteiYMD_2},
            {"title": "終了決定日", "type": "text", "value": shuryoketteiYMD_2},
            {"title": "宛名コード", "type": "text", "value": atenaCD_2},
            {"title": "出力順", "type": "select", "value": output_order_2},
            {"title": "通知書区分", "type": "text", "value": noti_category_5_2},
            {"title": "発行年月日", "type": "text", "value": hakkounengappi_2}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_45")

        # 46 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 47 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_47")

        # 48 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 49 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_49")

        # 50 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 51 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_51")

        # 52 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_52")

        # 53 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 54 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_54")

        # 55 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 56 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_56")

        # 57 ジョブ帳票履歴画面: 「児童扶養手当証書」のNoボタン押下

        # 58 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 59 児童扶養手当証書（PDF）: 表示
        # self.screen_shot("児童扶養手当証書（PDF）_59")

        # 60 児童扶養手当証書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("児童扶養手当証書（PDF）_60")

        # 61 児童扶養手当証書（PDF）: ×ボタン押下でPDFを閉じる

        # 62 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_62")

        # 63 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 64 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_64")

        # 65 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 66 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_66")

        # 67 バッチ起動画面: 「決定一括出力処理_減額適用関係一覧」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3033", ""))

        # 68 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_68")

        # 69 バッチ起動画面: 開始決定日「20230602」終了決定日「20230602」宛名コード「」出力順「証書番号順」選択通知書区分「5」発行年月日「20230602」
        params = [
            {"title": "開始決定日", "type": "text", "value": kaishiketteiYMD_3},
            {"title": "終了決定日", "type": "text", "value": shuryoketteiYMD_3},
            {"title": "宛名コード", "type": "text", "value": atenaCD_3},
            {"title": "出力順", "type": "select", "value": output_order_3},
            {"title": "通知書区分", "type": "text", "value": noti_category_5_3},
            {"title": "発行年月日", "type": "text", "value": hakkounengappi_3}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_69")

        # 70 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 71 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_71")

        # 72 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 73 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_73")

        # 74 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 75 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_75")

        # 76 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_76")

        # 77 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 78 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_78")

        # 79 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 80 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_80")

        # 81 ジョブ帳票履歴画面: 「通知書対象者一覧」のNoボタン押下

        # 82 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 83 通知書対象者一覧（PDF）: 表示
        # self.screen_shot("通知書対象者一覧（PDF）_83")

        # 84 通知書対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 85 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_85")

        # 86 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 87 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_87")

        # 88 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 89 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_89")

        # 90 バッチ起動画面: 「決定一括出力処理_減額適用関係_通知書」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3034", ""))

        # 91 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_91")

        # 92 バッチ起動画面: 開始決定日「20230602」終了決定日「20230602」宛名コード「」出力順「証書番号順」選択通知書区分「5」発行年月日「20230602」
        params = [
            {"title": "開始決定日", "type": "text", "value": kaishiketteiYMD_4},
            {"title": "終了決定日", "type": "text", "value": shuryoketteiYMD_4},
            {"title": "宛名コード", "type": "text", "value": atenaCD_4},
            {"title": "出力順", "type": "select", "value": output_order_4},
            {"title": "通知書区分", "type": "text", "value": noti_category_5_4},
            {"title": "発行年月日", "type": "text", "value": hakkounengappi_4}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_92")

        # 93 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 94 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_94")

        # 95 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 96 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_96")

        # 97 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 98 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_98")

        # 99 ジョブ帳票履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ帳票履歴画面_99")

        # 100 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 101 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_101")

        # 102 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 103 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_103")

        # 104 ジョブ帳票履歴画面: 「支給停止解除通知書」のNoボタン押下

        # 105 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 106 支給停止解除通知書（PDF）: 表示
        # self.screen_shot("支給停止解除通知書（PDF）_106")

        # 107 支給停止解除通知書（PDF）: 2ページ目表示
        # Assert: 裏面が出力されていることを確認する。
        # self.screen_shot("支給停止解除通知書（PDF）_107")

        # 108 支給停止解除通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 109 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_109")
