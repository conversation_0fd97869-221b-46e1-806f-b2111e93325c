import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180411(FukushiSiteTestCaseBase):
    """TestQAC050_180411"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180411"]
        super().setUp()

    # 内払い調整の登録ができることを確認する。
    def test_QAC050_180411(self):
        """内払額調整"""

        case_data = self.test_data["TestQAC050_180411"]
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # self.do_login()
        # 1 児童扶養手当資格管理画面: 「修正」ボタン押下
        self.click_button_by_label("修正")  # Button with ID: CmdShuusei

        # 2 児童扶養手当資格管理画面: 「支払調整履歴画面」ボタン押下
        self.open_common_buttons_area()
        self.common_button_click("支払調整履歴")

        # 3 支払調整履歴画面: 表示
        self.screen_shot("支払調整履歴画面_3")

        # 4 支払調整履歴画面: 該当者一覧 「1」Noボタン押下
        self.click_button_by_label("1")

        # 5 支払調整登録: 表示
        self.screen_shot("支払調整登録_5")

        # 6 支払調整登録: 「修正」ボタン押下
        self.click_button_by_label("修正")  # Button with ID: CmdShusei

        # 7 支払調整登録: 調整債権区分「全額調整」チェック返納予定額「0」「計算」ボタン押下
        self.form_input_by_id(idstr="RadioZengakuC", value=case_data.get("radio_zengaku_c", ""))
        self.form_input_by_id(idstr="TxtHYoteiGaku", value=case_data.get("txt_h_yotei_gaku", ""))
        self.click_button_by_label("計算")

        # 8 支払調整登録: 調整額「10000」「差引支払額計算」ボタン押下
        self.form_input_by_id(idstr="TxtKChoseiGaku", value=case_data.get("txt_k_chosei_gaku", ""))
        self.click_button_by_label("期別調整額設定") 
        self.click_button_by_label("差引支払額計算")  # Button with ID: CmdSSGKeisan

        # 9 支払調整登録: 「登録」ボタン押下
        self.click_button_by_label("登録")  # Button with ID: CmdTouroku
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)
        self.screen_shot("支払調整登録_9")

        # 10 支払調整登録: 「戻る」ボタン押下
        #self.return_click()

        # 11 支払調整履歴画面: 表示
        #self.screen_shot("支払調整履歴画面_11")

        # 12 支払調整履歴画面: 「戻る」ボタン押下
        self.return_click()

        # 13 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_13")

        # 14 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")  # Button with ID: CmdGetsugakuKeisan

        # 15 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")  # Button with ID: CmdTouroku
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 16 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_16")
