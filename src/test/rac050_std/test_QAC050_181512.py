import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_181512(FukushiSiteTestCaseBase):
    """TestQAC050_181512"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_181512"]
        super().setUp()

    # 未払支給者に対して支払通知書を出力できることを確認する。
    def test_QAC050_181512(self):
        """支払通知書作成"""

        case_data = self.test_data["TestQAC050_181512"]

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()  # Button with ID: CmdProcess4_1 instead of self.click_button_by_label("バッチ起動")

        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：支払処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))

        # 5 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 「支払通知書データ作成」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3063", ""))

        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: 出力順「証書番号順」選択発行年月日「20230902」
        params = [
            {"title": "出力順", "type": "select", "value": case_data.get("shutsuryoku_jun", "")},
            {"title": "発行年月日", "type": "text", "value": case_data.get("hakkou_nengetsubi", "")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_8")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()  # Button with ID: ExecuteButton

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()  # Button with ID: JobListButton

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # TODO unknown how to code this case
        self.screen_shot("ジョブ実行履歴画面_15")

        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()  # Button with ID: ReportListButton

        # 17 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_17")

        # 18 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 19 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_19")

        # 20 ジョブ帳票履歴画面: 「支給内訳書_全件」のNoボタン押下

        # 21 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 22 支給内訳書_全件（PDF）: 表示
        # self.screen_shot("支給内訳書_全件（PDF）_22")

        # 23 支給内訳書_全件（PDF）: ×ボタン押下でPDFを閉じる

        # 24 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_24")

        # 25 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()  # Button with ID: ExecListButton

        # 26 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_26")

        # 27 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()  # Button with ID: SearchButton

        # 28 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_28")

        # 29 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：支払処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))

        # 30 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_30")

        # 31 バッチ起動画面: 「支払通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj3064", ""))  # Button with ID: Sel1

        # 32 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_32")

        # 33 バッチ起動画面: 出力区分「0」開始頁「000001」終了頁「999999」
        params = [
            {"title": "出力区分", "type": "text", "value": case_data.get("shutsuryoku_kubun", "")},
            {"title": "開始頁", "type": "text", "value": case_data.get("kaishi_peiji", "")},
            {"title": "終了頁", "type": "text", "value": case_data.get("shuuryou_peiji", "")}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_33")

        # 34 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()  # Button with ID: ExecuteButton

        # 35 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_35")

        # 36 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()  # Button with ID: JobListButton

        # 37 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_37")

        # 38 ジョブ実行履歴画面: 「検索」ボタン押下
        # self.assert_job_normal_end(exec_datetime=exec_datetime)  # Button with ID: SearchButton
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 39 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_39")

        # 40 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        # TODO unknown how to code this case
        self.screen_shot("ジョブ実行履歴画面_40")

        # 41 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()  # Button with ID: ReportListButton

        # 42 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_42")

        # 43 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)  # Button with ID: SearchButton

        # 44 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_44")

        # 45 ジョブ帳票履歴画面: 「未支払_支払通知書」のNoボタン押下

        # 46 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 47 未支払_支払通知書（PDF）: 表示
        # self.screen_shot("未支払_支払通知書（PDF）_47")

        # 48 未支払_支払通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 49 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_49")
