import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC050_180507(FukushiSiteTestCaseBase):
    """TestQAC050_180507"""

    def setUp(self):
        case_data = self.test_data["TestQAC050_180507"]
        super().setUp()

    # 転出届を提出した住民に対し決定登録ができることを確認する。またバッチで市外転出した住民に対し自動で決定登録ができることを確認する。
    def test_QAC050_180507(self):
        """転出処理"""

        case_data = self.test_data["TestQAC050_180507"]
        atena_code = case_data.get("atena_code", "")
        atena_code2 = case_data.get("atena_code2", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC050")
        # 進達関連
        can_shintatsu_button = self.click_button_by_label(case_data.get("shintatsu_button", ""))
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsu1YMD", value=case_data.get("txt_shintatsu1_ymd", ""))
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.wait_page_loaded(wait_timeout=10)
            self.assert_message_area("登録しました")
            self.click_button_by_label(case_data.get("shintatsu_kekka_button", ""))
            self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text=case_data.get("shintatsu1_hantei_cmb", ""))
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
            self.wait_page_loaded(wait_timeout=10)
            self.assert_message_area("登録しました")

        # 1 児童扶養手当資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 2 児童扶養手当資格管理画面: 決定年月日「20230702」決定結果「決定」
        self.form_input_by_id(idstr="TxtKetteiYMD", value=case_data.get("txt_kettei_ymd", ""))
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=case_data.get("kettei_kekka_cmb", ""))

        # 3 児童扶養手当資格管理画面: 児童のNoボタン押下
        self.click_by_id("CmdNo1")

        # 4 支給対象児童入力画面: 表示
        self.screen_shot("支給対象児童入力画面_4")

        # 5 支給対象児童入力画面: 「戻る」ボタン押下
        self.return_click()

        # 6 児童扶養手当資格管理画面: 表示
        self.screen_shot("児童扶養手当資格管理画面_6")

        # 7 児童扶養手当資格管理画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 8 児童扶養手当資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.wait_page_loaded(wait_timeout=10)

        # 9 児童扶養手当資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました。 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("児童扶養手当資格管理画面_9")

        # 10 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_10")

        # # 2人目の情報入力
        # self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code2, gyoumu_code="QAC050")
        # # 進達関連
        # can_shintatsu_button = self.click_button_by_label("本庁進達入力")
        # if (can_shintatsu_button):
        #     self.click_button_by_label("本庁進達入力")
        #     self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20230701")
        #     self.click_button_by_label("月額計算")
        #     self.click_button_by_label("登録")
        #     self.alert_ok()
        #     self.assert_message_area("登録しました")
        #     self.click_button_by_label("本庁進達結果入力")
        #     self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20230701")
        #     self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="決定")
        #     self.click_button_by_label("月額計算")
        #     self.click_button_by_label("登録")
        #     self.alert_ok()
        #     self.assert_message_area("登録しました")

        # self.click_button_by_label("決定内容入力")
        # self.form_input_by_id(idstr="TxtKetteiYMD", value="20230702")
        # self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        # self.click_button_by_label("月額計算")
        # self.click_button_by_label("登録")
        # self.alert_ok()


        # 11 メインメニュー画面: 「バッチ起動」ボタン押下
        self.do_login()
        self.batch_kidou_click()

        # 12 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_12")

        # 13 バッチ起動画面: 業務：児童事業：児童扶養手当処理区分：月次処理分類：住記異動処理
        self.form_input_by_id(idstr="GyomuSelect", text=case_data.get("gyomu_select", ""))
        self.form_input_by_id(idstr="JigyoSelect", text=case_data.get("jigyo_select", ""))
        self.form_input_by_id(idstr="ShoriKubunSelect", text=case_data.get("shori_kubun_select", ""))
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=case_data.get("shori_bunrui_select", ""))

        # 14 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_14")

        # 15 バッチ起動画面: 「住記異動者自動消滅_抽出」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj2303", ""))

        # 16 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_16")

        # 17 バッチ起動画面: 対象開始年月日「20230701」対象終了年月日「20230701」出力順「証書番号順」選択
        params = [
            {"title": "対象開始年月日", "type": "text", "value": case_data.get("taishou_kaishi_nengappi", "")},
            {"title": "対象終了年月日", "type": "text", "value": case_data.get("taishou_shuuryou_nengappi", "")},
            {"title": "出力順", "type": "select", "value": case_data.get("shutsuryoku_jun", "")},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_17")

        # 18 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 19 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_19")

        # 20 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 21 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_21")

        # 22 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 23 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_23")

        # 24 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 26 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_26")

        # 27 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 28 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_28")

        # 29 ジョブ帳票履歴画面: 「自動消滅一覧（消滅）」のNoボタン押下
        # self.click_batch_job_button_by_label("自動消滅一覧（消滅）")

        # 30 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 31 自動消滅一覧（消滅）（PDF）: 表示
        # self.screen_shot("自動消滅一覧（消滅）（PDF）_33")

        # 32 自動消滅一覧（消滅）（PDF）: ×ボタン押下でPDFを閉じる
        # self.screen_shot("自動消滅一覧（消滅）（PDF）_34")

        # 33 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_33")

        # 34 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 35 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_35")

        # 36 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 37 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_37")

        # 38 バッチ起動画面: 「住記異動者自動消滅_更新」のNoボタン押下
        self.click_batch_job_button_by_label(case_data.get("batch_job_racj2304", ""))

        # 39 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_39")

        # 40 バッチ起動画面: 決定年月日「20230702」
        params = [
            {"title": "決定年月日", "type": "text", "value": case_data.get("kettei_nengappi", "")},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_40")

        # 41 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 42 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_42")

        # 43 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 44 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_44")

        # 45 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 46 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_46")

        # 47 ジョブ実行履歴画面: 処理結果一覧のNo.1の状態が「正常終了」となったらエビデンス取得
        self.screen_shot("ジョブ実行履歴画面_47")
