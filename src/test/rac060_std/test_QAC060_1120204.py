import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC060_1120204(FukushiSiteTestCaseBase):
    """TestQAC060_1120204"""

    def setUp(self):
        case_data = self.test_data["TestQAC060_1120204"]
        super().setUp()

    # 資格喪失通知書を出力できることを確認する。
    def test_QAC060_1120204(self):
        """資格喪失通知書作成"""

        case_data = self.test_data["TestQAC060_1120204"]
        atena_code = case_data.get("atena_code", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAC060")

        # 1 特別児童扶養手当受給者台帳画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 2 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_2")

        # 3 帳票印刷画面: 「特別児童扶養手当資格喪失通知書」行の印刷チェックボックス選択「特別児童扶養手当資格喪失通知書」行の発行年月日チェックボックス選択「特別児童扶養手当資格喪失通知書」行の発行年月日「20230702」
        exec_params = [
            {
                "report_name": case_data.get("report_name",""),
                "params":[
                    {"title": "発行年月日", "value":hakkou_ymd,"is_no_print":"1"}
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_11")

        # 4 帳票印刷画面: 「印刷」ボタン押下

        # 5 帳票印刷画面: 特別児童扶養手当資格喪失通知書「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック
        self.assert_message_area("プレビューを表示しました")

        # 6 特別児童扶養手当資格喪失通知書（PDF）: 表示
        # self.screen_shot("特別児童扶養手当資格喪失通知書（PDF）_5")

        # 7 特別児童扶養手当資格喪失通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 8 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 9 特別児童扶養手当受給者台帳画面: 表示
        self.screen_shot("特別児童扶養手当受給者台帳画面_9")

        # 10 メインメニュー画面: 表示
        self.do_login()
        self.screen_shot("メインメニュー画面_10")

        # 11 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 12 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_12")

        # 13 バッチ起動画面: 業務：障害事業：特別児童扶養手当処理区分：月次処理処理分類：通知書出力
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="通知書出力")

        # 14 バッチ起動画面: 「資格喪失通知書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("資格喪失通知書出力処理")

        # 15 バッチ起動画面: 開始決定日「20230702」終了決定日「20230702」出力順「証書番号順」選択発行年月日「20230702」
        params = [
            {"title": "開始決定日", "type": "text", "value": "20230702"},
            {"title": "終了決定日", "type": "text", "value": "20230702"},
            {"title": "出力順", "type": "select", "value": "証書番号順"},
            {"title": "発行年月日", "type": "text", "value": "20230702"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_15")

        # 16 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 17 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 18 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_18")

        # 19 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 20 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_20")

        # 21 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 22 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_22")

        # 23 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 24 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_24")

        # 25 ジョブ帳票履歴画面: 「特別児童扶養手当資格喪失通知書」のNoボタン押下
        # self.click_button_by_label("特別児童扶養手当資格喪失通知書")

        # 26 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        # self.click_button_by_label("ファイルを開く")

        # 27 特別児童扶養手当資格喪失通知書（PDF）: 表示
        # self.screen_shot("特別児童扶養手当資格喪失通知書（PDF）_27")

        # 28 特別児童扶養手当資格喪失通知書（PDF）: ×ボタン押下でPDFを閉じる

        # 29 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_29")
