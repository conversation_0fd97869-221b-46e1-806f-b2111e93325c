import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC060_1120306(FukushiSiteTestCaseBase):
    """TestQAC060_1120306"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 所得状況届が３年未提出となり、時効となる対象者の一覧、および資格喪失の一括履歴作成ができることを確認する。
    def test_QAC060_1120306(self):
        """時効対象者の資格喪失履歴更新"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        shinseiShubetsuCmb = case_data.get("shinseiShubetsuCmb", "")
        shinseiRiyuuCmb = case_data.get("shinseiRiyuuCmb", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        # self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 3 個人検索画面: 表示
        # self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        # self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「特別児童扶養手当」ボタン押下
        self.click_button_by_label("特別児童扶養手当")

        # 8 "特別児童扶養手当 資格管理画面": 表示
        # self.screen_shot("特別児童扶養手当 資格管理画面_8")

        # 9 "特別児童扶養手当 資格管理画面": 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 "特別児童扶養手当 資格管理画面": "申請種別「認定請求」選択 申請理由「新規」選択"
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinseiShubetsuCmb)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinseiRiyuuCmb)

        # 11 "特別児童扶養手当 資格管理画面": 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 "特別児童扶養手当 資格管理画面": "申請日「20210110」 担当所管区「第一区」選択 誓約有無「チェック」"
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20210110")
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text="第一区")
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value="1")

        # 13 "特別児童扶養手当 資格管理画面": 「児童追加」ボタン押下
        self.click_button_by_label("児童追加")

        # 14 世帯員検索画面: 表示
        # self.screen_shot("世帯員検索画面_14")

        # 15 世帯員検索画面: 世帯員一覧「2」ボタン押下
        self.click_button_by_label("2")

        # 16 支給対象児童入力画面: 表示
        # self.screen_shot("支給対象児童入力画面_16")

        # 17 支給対象児童入力画面: "続柄「長男」を選択 児童該当日「20210201」 児童該当事由「児童増（身体障害者手帳）」選択 児童同居別居区分「同居」チェック 児童総合障害等級「２級」選択 福祉行政報告例用障害分類「外部障害」選択 児童障害分類「上肢の機能障害」選択 児童有期認定年月「20240731」 児童障害等級「２級」選択 児童診断書様式「身体障害者手帳」選択"
        self.form_input_by_id(idstr="CmbZokugara", text="長男")
        self.form_input_by_id(idstr="TxtGaitouYMD", value="20210201")
        self.form_input_by_id(idstr="CmbGaitouJiyu", text="児童増（身体障害者手帳）")
        self.form_input_by_id(idstr="DoukyoBekkyoRBtn1", value="1")
        self.form_input_by_id(idstr="CmbShougaiToukyu", text="２級")
        self.form_input_by_id(idstr="CmbShougaiBunrui", text="外部障害")
        self.form_input_by_id(idstr="CmbByomei1", text="上肢の機能障害")
        self.form_input_by_id(idstr="TxtYuukiNinteiYMD1", value="20240731")
        self.form_input_by_id(idstr="CmbJidoShougaiToukyu1", text="２級")
        self.form_input_by_id(idstr="CmbJidoShindanshoYoshiki1", text="身体障害者手帳")

        # 18 支給対象児童入力画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 19 "特別児童扶養手当 資格管理画面": 表示
        # self.screen_shot("特別児童扶養手当 資格管理画面_19")

        # 20 "特別児童扶養手当 資格管理画面": 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報")

        # 21 福祉世帯情報画面: 表示
        # self.screen_shot("福祉世帯情報画面_21")

        # 22 福祉世帯情報画面: "1に対して 本人から見た続柄「本人」選択 受給者との関係「本人」 該当日「20210201」"
        self.form_input_by_id(idstr="HoninCmb_1", text="本人")
        self.form_input_by_id(idstr="JukyuCmb_1", text="本人")
        self.form_input_by_id(idstr="GaitoYMDtxt_1", value="20210201")

        # 23 福祉世帯情報画面: "2に対して 本人から見た続柄「子」選択 受給者との関係「対象児童」 該当日「20210201」"
        self.form_input_by_id(idstr="HoninCmb_2", text="子")
        self.form_input_by_id(idstr="JukyuCmb_2", text="対象児童")
        self.form_input_by_id(idstr="GaitoYMDtxt_2", value="20210201")

        # 24 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 25 "特別児童扶養手当 資格管理画面": 表示
        # self.screen_shot("特別児童扶養手当 資格管理画面_25")

        # 26 "特別児童扶養手当 資格管理画面": 開始年月「202102」
        self.form_input_by_id(idstr="TxtKaitei", value="202102")

        # 27 "特別児童扶養手当 資格管理画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 28 "特別児童扶養手当 資格管理画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 29 "特別児童扶養手当 資格管理画面": 表示 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        # self.screen_shot("特別児童扶養手当 資格管理画面_29")

        # 30 "特別児童扶養手当 資格管理画面": 「進達入力」ボタン押下
        self.click_button_by_label("進達入力")

        # 31 "特別児童扶養手当 資格管理画面": "進達日「20210202」 進達判定年月日「20210202」 進達結果「該当」"
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value="20210202")
        self.form_input_by_id(idstr="TxtShintatsu1HanteiYMD", value="20210202")
        self.form_input_by_id(idstr="Shintatsu1HanteiCmb", text="該当")

        # 32 "特別児童扶養手当 資格管理画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 33 "特別児童扶養手当 資格管理画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 34 "特別児童扶養手当 資格管理画面": 表示	「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        # self.screen_shot("特別児童扶養手当 資格管理画面_34")

        # 35 "特別児童扶養手当 資格管理画面": 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 36 "特別児童扶養手当 資格管理画面": 「現況情報」ボタン押下
        self.click_button_by_label("現況情報")

        # 37 現況情報画面: 表示
        # self.screen_shot("現況情報画面_37")

        # 38 現況情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 39 現況情報画面: 表示
        # self.screen_shot("現況情報画面_39")

        # 40 現況情報画面: "対象年度「令和3年」 発行年月日「20210801」"
        self.form_input_by_id(idstr="CmbTaisyoNendo", text="令和3年")
        self.form_input_by_id(idstr="TxtHakkouYMD", value="20210801")

        # 41 現況情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 42 "特別児童扶養手当 資格管理画面": 表示
        # self.screen_shot("特別児童扶養手当 資格管理画面_42")

        # 43 "特別障害者手当 資格管理画面": 「現況情報」ボタン押下
        self.click_button_by_label("現況情報")

        # 44 現況情報画面: 表示

        # 45 現況情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 46 現況情報画面: 表示
        # self.screen_shot("現況情報画面_46")

        # 47 現況情報画面: "対象年度「令和4年」 発行年月日「20220801」"
        self.form_input_by_id(idstr="CmbTaisyoNendo", text="令和4年")
        self.form_input_by_id(idstr="TxtHakkouYMD", value="20220801")

        # 48 現況情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 49 "特別児童扶養手当 資格管理画面": 表示

        # 50 "特別児童扶養手当 資格管理画面": 「現況情報」ボタン押下
        self.click_button_by_label("現況情報")

        # 51 現況情報画面: 表示

        # 52 現況情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 53 現況情報画面: 表示
        # 54 現況情報画面: "対象年度「令和5年」 発行年月日「20230801」"
        self.form_input_by_id(idstr="CmbTaisyoNendo", text="令和5年")
        self.form_input_by_id(idstr="TxtHakkouYMD", value="20230801")

        # 55 現況情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 56 "特別児童扶養手当 資格管理画面": 表示

        # 57 "特別児童扶養手当 資格管理画面": "判定日「20210202」 判定結果「決定」"
        self.form_input_by_id(idstr="TxtKetteiYMD", value="20210202")
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")

        # 58 "特別児童扶養手当 資格管理画面": 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 59 "特別児童扶養手当 資格管理画面": 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 60 "特別児童扶養手当 資格管理画面": 表示	「登録しました」のメッセージチェック
        self.assert_message_area("登録しました")
        # self.screen_shot("特別児童扶養手当 資格管理画面_60")

        self.do_login()

        # 61 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_61")

        # 62 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")

        # 63 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_63")

        # 64 バッチ起動画面: "業務：障害 事業：特別児童扶養手当 処理区分：年次処理 処理分類：現況時効者処理"
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="年次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="現況時効者処理")

        # 65 バッチ起動画面: 「現況届未提出時効者抽出処理」のNoボタン押下
        self.click_batch_job_button_by_label("現況届未提出時効者抽出処理")

        # 66 バッチ起動画面: "現況年度「令和5年」選択 発行年月日「20231101」 提出期限「20231110」 出力順「証書番号順」選択"
        params = [
            {"title": "現況年度", "type": "select", "value": "令和5年"},
            {"title": "抽出開始年月日", "type": "text", "value": "20231101"},
            {"title": "抽出終了年月日", "type": "text", "value": "20231110"},
            {"title": "申請種別", "type": "select", "value": "証書番号順"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_66")

        # 67 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 68 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 69 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_69")

        # 70 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 71 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_71")

        # 72 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 73 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_73")

        # 74 バッチ帳票履歴: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 75 ジョブ帳票履歴画面: 表示
        # self.screen_shot("ジョブ帳票履歴画面_75")

        # 76 バッチ帳票履歴: 「時効対象者一覧」のNoボタン押下
        # self.click_batch_job_button_by_label("時効対象者一覧")

        # 77 バッチ帳票履歴: 「ファイルを開く」ボタン押下
        # self.click_button_by_label("ファイルを開く")

        # 78 時効対象者一覧（PDF）: 表示

        # 79 時効対象者一覧（PDF）: ×ボタン押下でPDFを閉じる

        # 80 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_80")

        # 81 ジョブ帳票履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 82 バッチ起動画面: 表示
        # self.screen_shot("バッチ起動画面_82")

        # 83 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 84 バッチ起動画面: "業務：障害 事業：特別児童扶養手当 処理区分：年次処理 処理分類：現況時効者処理"
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="特別児童扶養手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="年次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="現況時効者処理")

        # 85 バッチ起動画面: 「現況届未提出時効者更新処理」のNoボタン押下
        self.click_batch_job_button_by_label("現況届未提出時効者更新処理")

        # 86 バッチ起動画面: "申請年月日「20231201」 決定年月日「20231201」 消滅年月日「20210731」"
        params = [
            {"title": "申請年月日", "type": "text", "value": "20231201"},
            {"title": "決定年月日", "type": "text", "value": "20231201"},
            {"title": "消滅年月日", "type": "text", "value": "20210731"}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_86")  # TODO: 申請年月日が見当たりません。, NG

        # 87 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 88 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 89 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_89")

        # 90 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 91 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_91")

        self.do_login()
        # 92 メインメニュー画面: 表示

        # 93 メインメニュー画面: 「申請資格管理」ボタン押下
        self.click_button_by_label("申請資格管理")

        # 94 個人検索画面: 表示

        # 95 個人検索画面: "事業固有コード検索 業務「障害」 事業「特別児童扶養手当」 固有コード種別「証書番号」 固有コード「」※備考参照"
        self.form_input_by_id(idstr="CmdGyomu", text="障害")
        self.form_input_by_id(idstr="CmdJigyo", text="特別児童扶養手当")
        self.form_input_by_id(idstr="CmdShubetsu", text="証書番号")
        self.form_input_by_id(idstr="koyuCd", text="")

        # 96 個人検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 97 受給状況画面: 表示

        # 98 受給状況画面: 「特別障害者手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC060")

        # 99 "特別児童扶養手当 資格管理画面": 表示
        self.screen_shot("特別児童扶養手当 資格管理画面_99")
