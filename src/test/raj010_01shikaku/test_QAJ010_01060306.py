import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01060306(FukushiSiteTestCaseBase):
    """TestQAJ010_01060306"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01060306"]
        super().setUp()
    
    # 支給（給付）決定通知書兼利用者負担額減額・免除等決定通知書を出力できることを確認する。
    def test_QAJ010_01060306(self):
        """決定通知書出力"""
        
        case_data = self.test_data["TestQAJ010_01060306"]
        atena_code = case_data.get("atena_code", "")
        hakkoTxt = case_data.get("hakkoTxt", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAJ010")
        # 2 障害福祉サービス申請管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        
        # 3 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_3")
        
        # 4 帳票印刷画面: 「支給決定通知書」行の印刷チェックボックス選択「支給決定通知書」行の発行年月日チェックボックス選択発行年月日「20230601」入力
        exec_params = [
            {"report_name": case_data.get("report_name",""),
             "params":[
                 {"title": "交付日", "value":hakkoTxt},
                {"title": "文書番号", "value":"11111"}
                ]
            }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_4")
        
        # 5 帳票印刷画面: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")
        
        # 6 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        
        # 7 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_7")
        
        # 8 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 9 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        
        # 10 障害福祉サービス申請管理画面: 表示
        self.screen_shot("障害福祉サービス申請管理画面_10")
        
