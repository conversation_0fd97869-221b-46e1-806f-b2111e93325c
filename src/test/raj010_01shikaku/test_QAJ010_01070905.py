import time
import datetime
from datetime import timedelta
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01070905(FukushiSiteTestCaseBase):
    """TestQAJ010_01070905"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01070905"]
        super().setUp()
    
    # 支給変更対象者抽出編集処理ができることを確認する
    def test_QAJ010_01070905(self):
        """支給変更対象者抽出編集処理"""

        case_data = self.test_data["TestQAJ010_01070905"]
        date = datetime.date.today()
        today = format(date, '%Y%m%d')

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")
        
        # 2 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")
        
        # 3 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_3")
        
        # 4 バッチ起動画面: 業務「障害」選択
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        
        # 5 バッチ起動画面: 事業「障害児支援」選択
        self.form_input_by_id(idstr="JigyoSelect", text="障害者総合支援")
        
        # 6 バッチ起動画面: 処理区分「随時処理」選択
        self.form_input_by_id(idstr="ShoriKubunSelect", text="随時処理")
        
        # 7 バッチ起動画面: 処理分類「支給変更決定通知書・受給者証出力」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="支給変更決定通知書・受給者証出力")
        
        # 8 バッチ起動画面:支給変更対象者抽出編集処理　ボタン押下
        self.find_element(By.ID,"Sel1").click()
        self.screen_shot("バッチ起動画面_8")
        
        # 9 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_9")
        
        # 10 バッチ起動画面: 「処理区分」、「交付日」、「出力順序」、「サービス名称基準年月」入力
        # ジョブパラ定義
        params = [
            {"title":"障害区分", "type": "select", "value":case_data.get("syougaiKbn")},
            {"title":"決定日＜はじめ＞", "type": "text", "value": case_data.get("ketteiYMD_s")},
            {"title":"決定日＜終わり＞", "type": "text", "value": case_data.get("ketteiYMD_e")},
            {"title":"受給者証番号＜はじめ＞", "type": "text", "value": case_data.get("jukyusya_s")},
            {"title":"受給者証番号＜終わり＞", "type": "text", "value": case_data.get("jukyusya_e")},
            {"title":"宛名番号", "type": "text", "value": case_data.get("atena_code")},
            {"title":"処理区分", "type": "text", "value":case_data.get("syorikubun")},
            {"title":"交付日", "type": "text", "value": case_data.get("koufuYMD")},
            {"title":"出力順序", "type": "select", "value":case_data.get("syutsuryoku")},
            {"title":"サービス名称基準年月", "type": "select","value":case_data.get("service_ymd")}
        ]
        # ジョブパラに入力
        self.set_job_params(params)
        self.form_input_by_id(idstr="UQZGC402_PrintSelect", text="ファイルアウト")
        self.form_input_by_id(idstr="UQZGC402_chkPrinter", value="0")
        
        # 11 バッチ起動画面: 「処理開始」ボタン押下
        # ジョブ実行(実行した日時を保持しておく)
        exec_datetime = self.exec_batch_job()
        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        
        # 12 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.screen_shot("バッチ起動画面_12")
        
        # 13 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 14 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_14")
        
        # 15 ジョブ実行履歴画面: No.1 支給決定対象者抽出編集処理  の状態が「正常終了」となったらエビデンス取得
        # 処理が終わるまで待機する
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_15")
        
        # 16 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()
        # 今回の処理で作成したPDFのDL（戻値はDLしたファイル数）
        report_dl_count = self.get_job_report_pdf(exec_datetime=exec_datetime)
        self.return_click()