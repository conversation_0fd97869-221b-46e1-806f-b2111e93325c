DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE WR$$JICHITAI_CODE$$QA..QAJ資格履歴            　　　　　　  WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援申請決定管理              WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援決定サービス区分管理　　　WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援決定サービス詳細　　　　　WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援負担額詳細　　　　　　　　WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ住基世帯履歴　　　　　　　　　　　WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援負担額管理　　　　　　　　WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援決定サービス種類管理　　　WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAZ受給状況　　　　　　　　　　　　　WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$'　and 業務コード='QAJ030'

DELETE WR$$JICHITAI_CODE$$QA..QAJサービス利用計画実績管理            WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$' AND 業務コード = 'QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJサービス利用計画申請　　　　　　　　WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$' AND 業務コード = 'QAJ030'

DELETE WR$$JICHITAI_CODE$$QA..QAJ同行援護アセスメント票情報          WHERE 宛名コード = '$$RAJ501_ATENA_CODE$$' AND 業務コード = 'QAJ030'

DELETE WR$$JICHITAI_CODE$$QA..QAJ減免申請            WHERE 本人宛名コード = '$$RAJ501_ATENA_CODE$$' AND 業務コード = 'QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ収入資産管理        WHERE 本人宛名コード = '$$RAJ501_ATENA_CODE$$' AND 業務コード = 'QAJ030'

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END