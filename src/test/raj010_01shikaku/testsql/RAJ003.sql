DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

--DELETE WR$$JICHITAI_CODE$$QA..QAJ審査会マスタ              WHERE 審査会コード = '000000XX'
DELETE WR$$JICHITAI_CODE$$QA..QAJ審査会審査委員割当      　WHERE 委員コード = '000001'
DELETE WR$$JICHITAI_CODE$$QA..QAJ審査情報                        WHERE 宛名コード = '$$RAJ003_ATENA_CODE$$'
--DELETE WR$$JICHITAI_CODE$$QA..QAJ審査会マスタ           　WHERE 開催年月日 =  CONVERT(NVARCHAR, GETDATE(), 112) 

DELETE WR$$JICHITAI_CODE$$QA..QAJ資格履歴        where 宛名コード='$$RAJ003_ATENA_CODE$$'  and 業務コード='QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAJ審査情報                        WHERE 宛名コード = '$$RAJ003_ATENA_CODE$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ一次判定結果                    WHERE 宛名コード = '$$RAJ003_ATENA_CODE$$' and 業務コード='QAJ010' 
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援決定サービス区分管理    where 宛名コード='$$RAJ003_ATENA_CODE$$'  and 業務コード='QAJ010' 
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援決定サービス詳細　　　　where 宛名コード='$$RAJ003_ATENA_CODE$$'  and 業務コード='QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援申請決定管理　　　　　　where 宛名コード='$$RAJ003_ATENA_CODE$$'  and 業務コード='QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援負担額詳細　　　　　　　where 宛名コード='$$RAJ003_ATENA_CODE$$'  and 業務コード='QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAZ受給状況　　　　　　　　　　　　where 宛名コード='$$RAJ003_ATENA_CODE$$'  and 業務コード='QAJ010'

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END