import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAH006001(FukushiSiteTestCaseBase):
    """TESTQAH006001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAH006.sql", params=atena_list)
        super().setUp()
    
    def test_case_qah006_001(self):
        """test_case_qah006_001"""
        driver = None
        test_data = self.test_data
        self.do_login()

        #メインメニュー・健康管理押下
        self.find_element(By.ID,"CmdProcess33_1").send_keys(Keys.ENTER)

        self.find_element(By.ID,"SetaiCD").send_keys("")
        self.find_element(By.ID,"SetaiCD").send_keys(test_data.get("atena_code"))
        self.find_element(By.ID,"Kensaku").click()
        self.find_element(By.ID,"NoBtn1").click()

        self.save_screenshot_migrate(driver, "QAH006-006-2-9" , True)

        self.find_element(By.ID,"CmdTokutei4").click()

        self.find_element(By.ID,"CmdShogamen").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-10" , True)

        self.find_element(By.ID,"CmdTransit").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-11" , True)
        
        #操作対象をサブ画面に切り替える
        self.driver.switch_to.frame(0)
        self.find_element(By.ID,"CmdCommon2").click()
        #操作対象をメイン画面に切り替える
        self.driver.switch_to.default_content()

        self.save_screenshot_migrate(driver, "QAH006-006-2-13" , True)

        self.find_element(By.ID,"SetaiCD").send_keys("")
        self.find_element(By.ID,"SetaiCD").send_keys(test_data.get("atena_code"))
        self.find_element(By.ID,"Kensaku").click()
        self.find_element(By.ID,"NoBtn1").click()

        self.save_screenshot_migrate(driver, "QAH006-006-2-18" , True)

        self.find_element(By.ID,"CmKenkouShinsa3").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-20" , True)

        self.find_element(By.ID,"CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-21" , True)

        self.find_element(By.ID,"CmbFukushi").send_keys(u"第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20240401")
        self.find_element(By.ID,"TxtJushinBasho").click()
        self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")
        self.find_element(By.ID,"CmbShudanKobetsu").send_keys(u"集団")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("1")
        self.find_element(By.ID,"01001002").click()
        self.find_element(By.ID,"01001002").send_keys("")
        self.find_element(By.ID,"01001002").send_keys("1")
        self.find_element(By.ID,"01001003").click()
        self.find_element(By.ID,"01001003").send_keys("")
        self.find_element(By.ID,"01001003").send_keys("1")
        self.find_element(By.ID,"01001004").click()
        self.find_element(By.ID,"01001004").send_keys("")
        self.find_element(By.ID,"01001004").send_keys("1")
        self.find_element(By.ID,"01001005").send_keys(u"国保")
        self.find_element(By.ID,"01001006").send_keys(u"特定健康診査")
        self.find_element(By.ID,"01001007").send_keys(u"補助あり")
        self.find_element(By.ID,"01001008").click()
        self.find_element(By.ID,"01001008").send_keys("")
        self.find_element(By.ID,"01001008").send_keys("202404")
        self.find_element(By.ID,"01002001").click()
        self.find_element(By.ID,"01002001").send_keys("")
        self.find_element(By.ID,"01002001").send_keys("1")
        self.find_element(By.ID,"01002002").click()
        self.find_element(By.ID,"01002002").send_keys("")
        self.find_element(By.ID,"01002002").send_keys("1")
        self.find_element(By.ID,"01002003").click()
        self.find_element(By.ID,"01002003").send_keys("")
        self.find_element(By.ID,"01002003").send_keys("1")
        self.find_element(By.ID,"01002004").click()
        self.find_element(By.ID,"01002004").send_keys("")
        self.find_element(By.ID,"01002004").send_keys("1")
        self.find_element(By.ID,"01002005").send_keys(u"実測")
        self.find_element(By.ID,"01002006").click()
        self.find_element(By.ID,"01002006").send_keys("")
        self.find_element(By.ID,"01002006").send_keys("1")
        self.find_element(By.ID,"01003001").send_keys(u"特記すべきことあり")
        self.find_element(By.ID,"01003002").click()
        self.find_element(By.ID,"01003002").send_keys("")
        self.find_element(By.ID,"01003002").send_keys("1")
        self.find_element(By.ID,"01003003").send_keys("特記すべきことあり")
        self.find_element(By.ID,"01003004").click()
        self.find_element(By.ID,"01003004").send_keys("")
        self.find_element(By.ID,"01003004").send_keys("1")
        self.find_element(By.ID,"01003005").send_keys("特記すべきことあり")
        self.find_element(By.ID,"01003006").click()
        self.find_element(By.ID,"01003006").send_keys("")
        self.find_element(By.ID,"01003006").send_keys("1")
        self.find_element(By.ID,"01004001").click()
        self.find_element(By.ID,"01004001").send_keys("")
        self.find_element(By.ID,"01004001").send_keys("1")
        self.find_element(By.ID,"01004002").click()
        self.find_element(By.ID,"01004002").send_keys("")
        self.find_element(By.ID,"01004002").send_keys("1")
        self.find_element(By.ID,"01004003").send_keys(u"食後10時間未満")
        self.find_element(By.ID,"01005001").click()
        self.find_element(By.ID,"01005001").send_keys("")
        self.find_element(By.ID,"01005001").send_keys("1")
        self.find_element(By.ID,"01005002").click()
        self.find_element(By.ID,"01005002").send_keys("")
        self.find_element(By.ID,"01005002").send_keys("1")
        self.find_element(By.ID,"01005003").click()
        self.find_element(By.ID,"01005003").send_keys("")
        self.find_element(By.ID,"01005003").send_keys("1")
        self.find_element(By.ID,"01006001").click()
        self.find_element(By.ID,"01006001").send_keys("")
        self.find_element(By.ID,"01006001").send_keys("1")
        self.find_element(By.ID,"01006002").click()
        self.find_element(By.ID,"01006002").send_keys("")
        self.find_element(By.ID,"01006002").send_keys("1")
        self.find_element(By.ID,"01006003").click()
        self.find_element(By.ID,"01006003").send_keys("")
        self.find_element(By.ID,"01006003").send_keys("1")
        self.find_element(By.ID,"01007001").click()
        self.find_element(By.ID,"01007001").send_keys("")
        self.find_element(By.ID,"01007001").send_keys("1")
        self.find_element(By.ID,"01008001").click()
        self.find_element(By.ID,"01008001").send_keys("")
        self.find_element(By.ID,"01008001").send_keys("1")
        self.find_element(By.ID,"01008002").click()
        self.find_element(By.ID,"01008002").send_keys("")
        self.find_element(By.ID,"01008002").send_keys("1")
        self.find_element(By.ID,"01009001").send_keys(u"－")
        self.find_element(By.ID,"01009002").send_keys(u"－")
        self.find_element(By.ID,"01010001").click()
        self.find_element(By.ID,"01010001").send_keys("")
        self.find_element(By.ID,"01010001").send_keys("1")
        self.find_element(By.ID,"01010002").click()
        self.find_element(By.ID,"01010002").send_keys("")
        self.find_element(By.ID,"01010002").send_keys("1")
        self.find_element(By.ID,"01010003").click()
        self.find_element(By.ID,"01010003").send_keys("")
        self.find_element(By.ID,"01010003").send_keys("1")
        self.find_element(By.ID,"01010004").click()
        self.find_element(By.ID,"01010004").send_keys("")
        self.find_element(By.ID,"01010004").send_keys("1")
        self.find_element(By.ID,"01011001").send_keys("異常所見あり")
        self.find_element(By.ID,"01011002").click()
        self.find_element(By.ID,"01011002").send_keys("")
        self.find_element(By.ID,"01011002").send_keys("1")
        self.find_element(By.ID,"01011003").click()
        self.find_element(By.ID,"01011003").send_keys("")
        self.find_element(By.ID,"01011003").send_keys("1")
        self.find_element(By.ID,"01012001").send_keys("異常なし")
        self.find_element(By.ID,"01012002").send_keys("0")
        self.find_element(By.ID,"01012003").send_keys("0")
        self.find_element(By.ID,"01012004").click()
        self.find_element(By.ID,"01012004").send_keys("")
        self.find_element(By.ID,"01012004").send_keys("1")
        self.find_element(By.ID,"01012005").click()
        self.find_element(By.ID,"01012005").send_keys("")
        self.find_element(By.ID,"01012005").send_keys("1")
        self.find_element(By.ID,"01013001").send_keys(u"基準該当")
        self.find_element(By.ID,"01013002").send_keys(u"積極的支援")
        self.find_element(By.ID,"01013003").click()
        self.find_element(By.ID,"01013003").send_keys("")
        self.find_element(By.ID,"01013003").send_keys("1")
        self.find_element(By.ID,"01013004").click()
        self.find_element(By.ID,"01013004").send_keys("")
        self.find_element(By.ID,"01013004").send_keys("1")
        self.find_element(By.ID,"01014001").send_keys("服薬あり")
        self.find_element(By.ID,"01014002").send_keys("服薬あり")
        self.find_element(By.ID,"01014003").send_keys("服薬あり")
        self.find_element(By.ID,"01014004").send_keys("はい")
        self.find_element(By.ID,"01014005").send_keys("はい")
        self.find_element(By.ID,"01014006").send_keys("はい")
        self.find_element(By.ID,"01014007").send_keys("はい")
        self.find_element(By.ID,"01014008").send_keys("はい")
        self.find_element(By.ID,"01014009").send_keys("はい")
        self.find_element(By.ID,"01014010").send_keys("はい")
        self.find_element(By.ID,"01014011").send_keys("はい")
        self.find_element(By.ID,"01014012").send_keys("はい")
        self.find_element(By.ID,"01014013").send_keys("はい")
        self.find_element(By.ID,"01014014").send_keys("速い")
        self.find_element(By.ID,"01014015").send_keys("はい")
        self.find_element(By.ID,"01014016").send_keys("はい")
        self.find_element(By.ID,"01014017").send_keys("はい")
        self.find_element(By.ID,"01014018").send_keys("毎日")
        self.find_element(By.ID,"01014019").send_keys("1合未満")
        self.find_element(By.ID,"01014020").send_keys("はい")
        self.find_element(By.ID,"01014021").send_keys("意思なし")
        self.find_element(By.ID,"01014022").send_keys("はい")
        self.find_element(By.ID,"01015001").click()
        self.find_element(By.ID,"01015001").send_keys("")
        self.find_element(By.ID,"01015001").send_keys("202404")
        self.find_element(By.ID,"01016001").click()
        self.find_element(By.ID,"01016001").send_keys("")
        self.find_element(By.ID,"01016001").send_keys("1")
        self.find_element(By.ID,"01016002").click()
        self.find_element(By.ID,"01016002").send_keys("")
        self.find_element(By.ID,"01016002").send_keys("1")
        self.find_element(By.ID,"01017001").click()
        self.find_element(By.ID,"01017001").send_keys("")
        self.find_element(By.ID,"01017001").send_keys("20240401")
        self.find_element(By.ID,"01017002").click()
        self.find_element(By.ID,"01017002").send_keys("")
        self.find_element(By.ID,"01017002").send_keys("20240401")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-24" , True)

        self.find_element(By.ID,"btnCommon0").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-26" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-28" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-30" , True)

        self.find_element(By.ID,"CmdHokenShidouKanri3").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-32" , True)

        self.find_element(By.ID,"CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-33" , True)

        self.find_element(By.ID,"CmbFukushi").send_keys(u"第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20240401")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys(u"利用")
        self.find_element(By.ID,"01001002").click()
        self.find_element(By.ID,"01001002").send_keys("")
        self.find_element(By.ID,"01001002").send_keys(u"積極的支援")

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-35" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-37" , True)

        self.find_element(By.ID,"CmdHyoujiKirikae").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-39" , True)

        self.find_element(By.ID,"CmdHokenShidouKekka3").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-41" , True)

        self.find_element(By.ID,"CmdModori").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-42" , True)

        self.find_element(By.ID,"CmdSusumi").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-43" , True)

        self.find_element(By.ID,"Btn_QAH_Calendar0").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-44" , True)
        
        self.driver.switch_to.frame(0)
        self.find_element(By.CSS_SELECTOR, "tr:nth-child(4) > .TD2").click()
        self.driver.switch_to.default_content()

        self.save_screenshot_migrate(driver, "QAH006-006-2-45" , True)

        self.find_element(By.ID,"CmdTouNendo").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-46" , True)

        self.find_element(By.ID,"SelectZissekiKubun").send_keys(u"初回")

        self.find_element(By.ID,"CmdZissekiAdd").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-49" , True)

        self.find_element(By.ID,"CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-50" , True)

        self.find_element(By.ID,"CmbFukushi").send_keys(u"第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20240401")
        self.find_element(By.ID,"TxtJushinBasho").click()
        self.find_element(By.ID,"TxtJushinBasho").send_keys("")
        self.find_element(By.ID,"TxtJushinBasho").send_keys("0000000001")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("6")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        
        self.save_screenshot_migrate(driver, "QAH006-006-2-52" , True)

        self.find_element(By.ID,"span_CmdGyomuKirikae2").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-53" , True)

        self.find_element(By.ID,"span_CmdGyomuKirikae3").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-54" , True)

        self.find_element(By.ID,"span_CmdGyomuKirikae4").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-55" , True)

        self.find_element(By.ID,"span_CmdGyomuKirikae5").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-56" , True)
        
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-58" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-60" , True)

        self.find_element(By.ID,"CmdHyoujiKirikae").click()

        self.find_element(By.ID,"CmdJyogai").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-62" , True)

        self.find_element(By.ID,"CmdTsuika").click()
        self.find_element(By.ID,"CmbJyogaiJiyu").send_keys(u"除外事由1")
        self.find_element(By.ID,"CmbKaijyoJiyu").send_keys(u"解除事由1")
        self.find_element(By.ID,"TxtJyogaiYMD").click()
        self.find_element(By.ID,"TxtJyogaiYMD").send_keys("")
        self.find_element(By.ID,"TxtJyogaiYMD").send_keys("20240501")
        self.find_element(By.ID,"TxtKaijyoYMD").click()
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("")
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("20240601")

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-65" , True)

        self.find_element(By.ID,"CmdShuusei").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-66" , True)

        self.find_element(By.ID,"TxtJyogaiYMD").click()
        self.find_element(By.ID,"TxtKaijyoYMD").click()
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("")
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("20240605")
        
        self.find_element(By.ID,"span_CmdShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-68" , True)

        self.find_element(By.ID,"span_CmdShuusei").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-69" , True)

        self.find_element(By.ID,"TxtKaijyoYMD").click()
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("")
        self.find_element(By.ID,"TxtKaijyoYMD").send_keys("20240605")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-71" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-73" , True)

        self.find_element(By.ID,"CmdJyogai").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-75" , True)
        
        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-76" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-78" , True)

        self.find_element(By.ID,"CmdJyushinken").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-80" , True)

        self.find_element(By.ID,"CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-81" , True)

        self.find_element(By.ID,"CmbFukushi").send_keys("第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20240401")
      
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("1")
        self.find_element(By.ID,"01001002").send_keys(u"特定健康診査")
        self.find_element(By.ID,"01001003").click()
        self.find_element(By.ID,"01001003").send_keys("")
        self.find_element(By.ID,"01001003").send_keys("20240401")
        self.find_element(By.ID,"01001004").click()
        self.find_element(By.ID,"01001004").send_keys("")
        self.find_element(By.ID,"01001004").send_keys("1")
        self.find_element(By.ID,"01002001").click()
        self.find_element(By.ID,"01002001").send_keys("")
        self.find_element(By.ID,"01002001").send_keys("1")
        self.find_element(By.ID,"01003001").click()
        self.find_element(By.ID,"01003001").send_keys("")
        self.find_element(By.ID,"01003001").send_keys("20240401")

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-83" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-85" , True)

        self.find_element(By.ID,"CmdRiyouken").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-87" , True)
        
        self.find_element(By.ID,"CmdTsuika").click()

        self.save_screenshot_migrate(driver, "QAH006-006-2-88" , True)

        self.find_element(By.ID,"CmbFukushi").send_keys(u"第一区")
        self.find_element(By.ID,"TxtJushinYMD").click()
        self.find_element(By.ID,"TxtJushinYMD").send_keys("")
        self.find_element(By.ID,"TxtJushinYMD").send_keys("20240401")
        self.find_element(By.ID,"01001001").click()
        self.find_element(By.ID,"01001001").send_keys("")
        self.find_element(By.ID,"01001001").send_keys("1")
        self.find_element(By.ID,"01002001").click()
        self.find_element(By.ID,"01002001").send_keys("")
        self.find_element(By.ID,"01002001").send_keys("1")
        self.find_element(By.ID,"01003001").click()
        self.find_element(By.ID,"01003001").send_keys("")
        self.find_element(By.ID,"01003001").send_keys("1")
        self.find_element(By.ID,"01004001").click()
        self.find_element(By.ID,"01004001").send_keys("")
        self.find_element(By.ID,"01004001").send_keys("20240401")
        self.find_element(By.ID,"01005001").click()
        self.find_element(By.ID,"01005001").send_keys("")
        self.find_element(By.ID,"01005001").send_keys("20240401")
        self.find_element(By.ID,"01006001").send_keys(u"負担なし")
        self.find_element(By.ID,"01006002").click()
        self.find_element(By.ID,"01006002").send_keys("")
        self.find_element(By.ID,"01006002").send_keys("1")
        self.find_element(By.ID,"01006003").click()
        self.find_element(By.ID,"01006003").send_keys("")
        self.find_element(By.ID,"01006003").send_keys("1")
        self.find_element(By.ID,"01006004").click()
        self.find_element(By.ID,"01006004").send_keys("")
        self.find_element(By.ID,"01006004").send_keys("1")
        self.find_element(By.ID,"01006005").click()
        self.find_element(By.ID,"01006005").send_keys("")
        self.find_element(By.ID,"01006005").send_keys("1")
        self.find_element(By.ID,"01007001").click()
        self.find_element(By.ID,"01007001").send_keys("")
        self.find_element(By.ID,"01007001").send_keys("1")
        self.find_element(By.ID,"01008001").click()
        self.find_element(By.ID,"01008001").send_keys("")
        self.find_element(By.ID,"01008001").send_keys("1")
        self.find_element(By.ID,"01009001").send_keys(u"特定健康診査")
        self.find_element(By.ID,"01010001").send_keys(u"積極的支援")
        self.find_element(By.ID,"01011001").click()
        self.find_element(By.ID,"01011001").send_keys("")
        self.find_element(By.ID,"01011001").send_keys("20240401")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-90" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-92" , True)

        self.find_element(By.ID,"CmdHyouka").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-94" , True)

        self.find_element(By.ID,"BtnTaiJyu").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-95" , True)

        self.find_element(By.ID,"BtnBMI").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-96" , True)

        self.find_element(By.ID,"BtnSyuShikuKi").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-97" , True)

        self.find_element(By.ID,"BtnKakuCyoKi").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-98" , True)

        self.find_element(By.ID,"CmdModori").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-99" , True)

        self.find_element(By.ID,"CmdSusumi").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-100" , True)

        self.find_element(By.ID,"Btn_QAH_Calendar0").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-102" , True)
        
        self.driver.switch_to.frame(0)
        self.find_element(By.CSS_SELECTOR, "tr:nth-child(4) > .TD2").click()
        self.driver.switch_to.default_content()

        self.find_element(By.ID,"CmdTouNendo").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-103" , True)

        self.find_element(By.ID,"CmdKomento").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-105" , True)

        self.find_element(By.ID,"CmdTsuika").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-106" , True)

        self.find_element(By.ID,"CmbComentoShurui").send_keys(u"改善点(食事)")
        self.find_element(By.ID,"TxtComento").click()
        self.find_element(By.ID,"TxtComento").send_keys("")
        self.find_element(By.ID,"TxtComento").send_keys("1")

        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-108" , True)

        self.find_element(By.ID,"CmdShuusei").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-109" , True)

        self.find_element(By.ID,"TxtComento").click()
        self.find_element(By.ID,"TxtComento").send_keys("")
        self.find_element(By.ID,"TxtComento").send_keys("2")

        
        self.find_element(By.ID,"CmdShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-111" , True)

        self.find_element(By.ID,"CmdShuusei").click()
        self.find_element(By.ID,"TxtComento").click()
        self.find_element(By.ID,"TxtComento").send_keys("")
        self.find_element(By.ID,"TxtComento").send_keys("3")

        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-114" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-116" , True)

        self.find_element(By.ID,"CmdKomento").click()
        
        self.save_screenshot_migrate(driver, "QAH006-006-2-118" , True)

        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAH006-006-2-119" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-121" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-123" , True)

        self.find_element(By.ID,"CmdPrintOut").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-125" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-127" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-129" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-130", True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAH006-006-2-131", True)	

    # テスト後に実行したい場合はこちら
    def tearDown(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAH006.sql", params=atena_list)
        super().tearDown()