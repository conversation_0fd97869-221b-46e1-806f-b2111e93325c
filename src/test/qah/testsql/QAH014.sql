DECLARE @更新実行フラグ INT
SET @更新実行フラグ = 1 -- 更新するとき 1 に書き換える

BEGIN TRAN

DELETE FROM WR$$JICHITAI_CODE$$QA..QAH通知履歴情報 where 自治体コード='$$JICHITAI_CODE$$' and 業務コード='QAH101' and 区分='01' and 検診区分='1' and 年度=CASE WHEN SUBSTRING(CONVERT(VARCHAR,GETDATE(),112),5,2) IN ('01','02','03') THEN CONVERT(VARCHAR,CONVERT(INT, SUBSTRING(CONVERT(VARCHAR,GETDATE(),112),1,4))-1) ELSE SUBSTRING(CONVERT(VARCHAR,GETDATE(),112),1,4) END and 抽出区分コード='QAHJ9002' and 通知履歴番号=1
insert into WR$$JICHITAI_CODE$$QA..QAH通知履歴情報 values('$$JICHITAI_CODE$$','QAH101','01','1','QAH100','00000','','','','',CASE WHEN SUBSTRING(CONVERT(VARCHAR,GETDATE(),112),5,2) IN ('01','02','03') THEN CONVERT(VARCHAR,CONVERT(INT, SUBSTRING(CONVERT(VARCHAR,GETDATE(),112),1,4))-1) ELSE SUBSTRING(CONVERT(VARCHAR,GETDATE(),112),1,4) END,'$$SQL_ATENA_CODE$$','QAHJ9002',1,'QAHB9002','20240401','00000000','00000000','0000000001','','$$SQL_ATENA_CODE$$','0','0','9501','9501',getdate(),getdate(),'QAHG_CUD')

IF @更新実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END