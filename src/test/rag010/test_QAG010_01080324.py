import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG010_01080324(FukushiSiteTestCaseBase):
    """TestQAG010_01080324"""
    
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code")
        params = {"DELETE_ATENA_CODE": atena_code}
        self.exec_sqlfile("RAG010_01080104.sql", params=params)
        super().setUp()
        
    # 年金照会（個別照会）の依頼情報作成ができることを確認する。
    def test_QAG010_01080324(self):
        """年金照会（個別照会）"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        gyomu_select = case_data.get("gyomu_select", "")
        jigyo_select = case_data.get("jigyo_select", "")
        shoriKubun_select = case_data.get("shoriKubun_select", "")
        shoriBunrui_select = case_data.get("shoriBunrui_select", "")

        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害事業：自立支援医療(更生医療)処理区分：年金照会抽出処理分類：年金照会抽出
        self.form_input_by_id(idstr="GyomuSelect", text=gyomu_select)
        self.form_input_by_id(idstr="JigyoSelect", text=jigyo_select)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=shoriKubun_select)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=shoriBunrui_select)
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 「更生医療_年金照会抽出_一括」のNoボタン押下
        self.click_batch_job_button_by_label("更生医療_年金照会抽出_一括")

         # 5 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 6 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 7 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_7")

        # 8 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 11 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_11")

        # 12 ジョブ帳票履歴画面: 「検索」ボタン押下        
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 13 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_13")

        # 14 ジョブ帳票履歴画面: 「照会対象者一覧」のNoボタン押下

        # 15 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 16 帳票（PDF）: 表示
        self.screen_shot("帳票（PDF）_16")

        # 17 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 18 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_18")

        # 19 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 20 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 21 バッチ起動画面: 「年金情報照会用データ登録処理 」のNoボタン押下
        self.click_batch_job_button_by_label("年金情報照会用データ登録処理")

        # 22 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 23 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 24 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_24")

        # 25 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 26 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_26")
