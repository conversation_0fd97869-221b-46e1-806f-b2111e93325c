import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG010_01080203(FukushiSiteTestCaseBase):
    """TestQAG010_01080203"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 変更申請した住民に対し決定登録ができることを確認する。
    def test_QAG010_01080203(self):
        """認定結果の登録_決定_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG010")
        # 1 自立支援医療(更生医療)資格管理画面: 「決定内容入力」ボタン押下
        self.click_by_id(idstr="CmdButton1_1")
        self.click_button_by_label("決定内容入力")

        # 2 自立支援医療(更生医療)資格管理画面: 表示
        self.screen_shot("自立支援医療(更生医療)資格管理画面_2")

        # 3 自立支援医療(更生医療)資格管理画面: 決定日「○○」入力決定結果「決定」選択
        #変更（記載事項）の住民↓
        self.form_input_by_id(idstr="TxtKetteiYMD", value=case_data.get("KetteiYMD_kisaihenko", ""))
        #再交付の住民↓
        # self.form_input_by_id(idstr="TxtKetteiYMD", value=case_data.get("KetteiYMD_saikofu", ""))
        #資格損失の住民↓
        # self.form_input_by_id(idstr="TxtKetteiYMD", value=case_data.get("KetteiYMD_shikakusoshitsu", ""))

        self.form_input_by_id(idstr="KetteiKekkaCmb", text=case_data.get("KetteiKekka", ""))
        self.form_input_by_id(idstr="TxtShinseiYMD", value="20230801")
        # 4 自立支援医療(更生医療)資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 5 自立支援医療(更生医療)資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(更生医療)資格管理画面_5")
