{"sql_params": {}, "": {"atena_code": "100700000100005"}, "TestQAG010_01080101": {"atena_code": "100700000100005", "report_name": "自立支援医療費支給認定申請書", "hakkou_ymd": "20240801"}, "TestQAG010_01080121": {"atena_code": "100700000100005", "report_name": "自立支援医療費支給認定申請書", "hakkou_ymd": "20240801"}, "TestQAG010_01080301": {"atena_code": "100700000100005", "report_name": "自立支援医療費支給認定申請書", "hakkou_ymd": "20240801"}, "TestQAG010_01080102": {"atena_code": "100700000100005", "shinsei_ymd": "20230701", "ShinseiShubetsu": "新規", "ShinseiRiyuu": "新規申請", "tanto_shokatsuku": "第一区", "tanto_shisho": "012", "uketsuke_basho": "0000000001", "tanto_basho": "0000000001", "shotoku_hantei_nendo": "令和05年", "kofu_hoho": "0000000001", "kanyu_jokyo1": "保険加入", "hokenshurui1": "国民健康保険", "hokenshurui2": "健康保険等", "txt_kanji_meisho": "健", "txt_kanji_meisho2": "医", "txt_kanji_meisho3": "薬", "txt_kanji_meisho4": "訪", "fuyousha": "扶養者", "txt_kigo": "１２３", "txt_bango": "４５６７", "txt_kojin_shiki_bango": "１２", "shikaku_shutoku_ymd": "20230701", "shozaichi_1": "1", "tensuhyo": "0000000001", "tensuhyo2": "0000000004", "tensuhyo3": "0000000006", "hoshin": "0000000001", "Seiho_Begin_ymd": "20230701", "Seiho_End_ymd": "", "renrakuatena": "100100000100011", "iryoukikan_shozaichi_kubun_2": "1", "iryoukikan_shozaichi_kubun_3": "1", "iryoukikan_shozaichi_kubun_4": "1"}, "TestQAG010_01080104": {"atena_code": "100700000100005", "gyomu_select": "障害", "jigyo_select": "自立支援医療(更生)", "shoriKubun_select": "年金照会抽出", "shoriBunrui_select": "年金照会抽出（0000052000）"}, "TestQAG010_01080103": {"atena_code": "100700000100005", "shokai_cmb_insert": "追加", "shokai_insert": "日本年金機構"}, "TestQAG010_01080105": {"atena_code": "100700000100005", "shintatsu1_ymd": "20230701", "hantei_ymd": "20230701", "hantei_yotei_ji": "10", "hantei_yotei_fun": "00"}, "TestQAG010_01080106": {"atena_code": "100700000100005", "report_name1": "判定依頼書", "report_name2": "調査書", "hakkou_ymd": "20230701"}, "TestQAG010_01080107": {"atena_code": "100700000100005", "gyomu_select": "障害", "jigyo_select": "自立支援医療(更生)", "shoriKubun_select": "年金照会抽出", "shoriBunrui_select": "年金照会抽出（0000052000）"}, "TestQAG010_01080108": {"atena_code": "100700000100005"}, "TestQAG010_01080109": {"atena_code": "100700000100005", "shintatsu_hantei1_ymd": "20230701", "shintatsu_hantei1": "決定"}, "TestQAG010_01080110": {"atena_code": "100700000100005", "kettei_ymd": "20230701", "kettei_kekka": "却下", "kettei_riyu": "要件非該当のため", "area_kyakka_riyu": "却下理由入力テスト★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★"}, "TestQAG010_01080111": {"atena_code": "100700000100005", "report_name": "却下通知書", "hakkou_ymd_shinki": "20230701", "hakkou_ymd_sainintei": "20240701", "hakkou_ymd_henko_jikofutan": "20240801", "hakkou_ymd_henko_iryokikan": "20240901"}, "TestQAG010_01080201": {"atena_code": "100700000100005", "hakkou_ymd": "20230801"}, "TestQAG010_01080202": {"atena_code": "100700000100005", "ShinseiShubetsu": "変更", "ShinseiRiyuu": "受診者に関する事項変更", "ShinseiYMD": "20230801", "JiyuHasseiYMD": "20230801"}, "TestQAG010_01080203": {"atena_code": "100700000100005", "KetteiYMD_kisaihenko": "20230801", "KetteiYMD_saikofu": "20241001", "KetteiYMD_shikakusoshitsu": "20241101", "KetteiKekka": "決定"}, "TestQAG010_01080204": {"atena_code": "100700000100005", "report_name": "自立支援医療受給者証（表面）", "hakkou_ymd": "20230801"}, "TestQAG010_01080205": {"atena_code": "100700000100005", "hakkou_ymd": "20230801"}, "TestQAG010_01080336": {"atena_code": "100700000100005", "hakkou_ymd": "20230801"}, "TestQAG010_01080347": {"atena_code": "100700000100005", "hakkou_ymd": "20230801"}, "TestQAC020_1050105": {"atena_code": "100700000100005", "hakkou_ymd": "20230801"}, "TestQAC020_1050106": {"atena_code": "100700000100005", "hakkou_ymd": "20230801"}, "TestQAC020_1050107": {"atena_code": "100700000100005", "hakkou_ymd": "20230801"}, "TestQAG010_01080112": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080312": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080317": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080330": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080334": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080302": {"atena_code": "100700000100005", "ShinseiShubetsu": "再認定", "ShinseiRiyuu": "判定あり", "shinsei_ymd": "20240701", "ShotokuHanteiNendo": "令和06年", "Tokuteihuyou": "0", "kaishi_ymd": "20240701", "shuryo_ymd": "20250630"}, "TestQAG010_01080304": {"atena_code": "100700000100005", "gyomu_select": "障害", "jigyo_select": "自立支援医療(更生)", "shoriKubun_select": "年金照会抽出", "shoriBunrui_select": "年金照会抽出（0000052000）"}, "TestQAG010_01080303": {"atena_code": "100700000100005", "shokai_cmb_insert": "追加", "shokai_insert": "日本年金機構"}, "TestQAG010_01080305": {"atena_code": "100700000100005", "shintatsu1_ymd": "20240701", "hantei_ymd": "20240701", "hantei_yotei_ji": "10", "hantei_yotei_fun": "00"}, "TestQAG010_01080306": {"atena_code": "100700000100005", "report_name1": "判定依頼書", "report_name2": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080307": {"atena_code": "100700000100005", "gyomu_select": "障害", "jigyo_select": "自立支援医療(更生)", "shoriKubun_select": "年金照会抽出", "shoriBunrui_select": "年金照会抽出（0000052000）"}, "TestQAG010_01080308": {"atena_code": "100700000100005"}, "TestQAG010_01080309": {"atena_code": "100700000100005", "shintatsu_hantei1_ymd": "20240701", "shintatsu_hantei1": "決定"}, "TestQAG010_01080310": {"atena_code": "100700000100005", "kettei_ymd": "20240701", "kettei_kekka": "却下", "kettei_riyu": "要件非該当のため", "area_kyakka_riyu": "却下理由入力テスト★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★"}, "TestQAG010_01080311": {"atena_code": "100700000100005", "report_name": "却下通知書", "hakkou_ymd": "20240701"}, "TestQAG010_01080313": {"atena_code": "100700000100005", "report_name": "支給認定取消通知書", "hakkou_ymd": "20241101"}, "TestQAG010_01080314": {"atena_code": "100700000100005", "kettei_ymd": "20240701", "kaishi_ymd": "20240701", "shuryo_ymd": "20250630", "tokureikaishi_ymd": "", "tokureishuryo_ymd": "", "tekiyo_ymd": "20240701"}, "TestQAG010_01080315": {"atena_code": "100700000100005", "report_name": "自立支援医療受給者証（表面）", "hakkou_ymd": "20240701"}, "TestQAG010_01080316": {"atena_code": "100700000100005", "report_name": "自己負担上限額管理票", "hakkou_ymd": "20240701"}, "test_QAG010_01080317": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080318": {"atena_code": "100700000100005", "report_name": "認定決定通知書", "hakkou_ymd": "20240701"}, "TestQAG010_01080319": {"atena_code": "100700000100005", "report_name": "支給認定決定のお知らせ", "hakkou_ymd": "20240701"}, "TestQAG010_01080322": {"atena_code": "100700000100005", "ShinseiShubetsu": "変更", "ShinseiRiyuu": "自己負担限度額変更", "shinsei_ymd": "20240801", "DayEnd": "20240630"}, "TestQAG010_01080324": {"atena_code": "100700000100005", "gyomu_select": "障害", "jigyo_select": "自立支援医療(更生)", "shoriKubun_select": "年金照会抽出", "shoriBunrui_select": "年金照会抽出（0000052000）"}, "TestQAG010_01080323": {"atena_code": "100700000100005", "shokai_cmb_insert": "追加", "shokai_insert": "日本年金機構"}, "TestQAG010_01080325": {"atena_code": "100700000100005", "gyomu_select": "障害", "jigyo_select": "自立支援医療(更生)", "shoriKubun_select": "年金照会抽出", "shoriBunrui_select": "年金照会抽出（0000052000）"}, "TestQAG010_01080326": {"atena_code": "100700000100005"}, "TestQAG010_01080327": {"atena_code": "100700000100005", "kettei_ymd": "20240801", "kettei_kekka": "却下", "kettei_riyu": "要件非該当のため", "area_kyakka_riyu": "却下理由入力テスト★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★"}, "TestQAG010_01080328": {"atena_code": "100700000100005", "report_name": "却下通知書", "hakkou_ymd": "20240801"}, "TestQAG010_01080329": {"atena_code": "100700000100005", "report_name": "支給認定取消通知書", "hakkou_ymd": "20241101"}, "TestQAG010_01080331": {"atena_code": "100700000100005", "kettei_ymd": "20240801", "kaishi_ymd": "20240701", "shuryo_ymd": "20250630", "tokureikaishi_ymd": "", "tokureishuryo_ymd": "", "tekiyo_ymd": "20240801"}, "TestQAG010_01080332": {"atena_code": "100700000100005", "report_name": "自立支援医療受給者証（表面）", "hakkou_ymd": "20240801"}, "TestQAG010_01080333": {"atena_code": "100700000100005", "report_name": "自己負担上限額管理票", "hakkou_ymd": "20240801"}, "TestQAG010_01080335": {"atena_code": "100700000100005", "report_name": "支給認定決定のお知らせ", "hakkou_ymd": "20240801"}, "TestQAG010_01080338": {"atena_code": "100700000100005", "ShinseiShubetsu": "変更", "ShinseiRiyuu": "指定医療機関変更", "Shozaichi": "1", "tensuhyo": "0000000001", "tensuhyo2": "0000000004", "tensuhyo3": "0000000006", "txt_kanji_meisho": "テスト病院", "txt_kanji_meisho2": "薬局テスト２", "txt_kanji_meisho3": "訪問看護テスト２", "shinsei_ymd": "20240901", "kaishi_ymd": "20240701", "shuryo_ymd": "20240831", "iryoukikan_shozaichi_kubun_2": "1", "iryoukikan_shozaichi_kubun_3": "1", "iryoukikan_shozaichi_kubun_4": "1"}, "TestQAG010_01080339": {"atena_code": "100700000100005", "kettei_ymd": "20240901", "kettei_kekka": "却下", "kettei_riyu": "要件非該当のため", "area_kyakka_riyu": "却下理由入力テスト★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★"}, "TestQAG010_01080340": {"atena_code": "100700000100005", "report_name": "却下通知書", "hakkou_ymd": "20240901"}, "TestQAG010_01080341": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240901"}, "TestQAG010_01080342": {"atena_code": "100700000100005", "report_name": "支給認定取消通知書", "hakkou_ymd": "20241101"}, "TestQAG010_01080343": {"atena_code": "100700000100005", "kettei_ymd": "20240901", "kaishi_ymd": "20240701", "shuryo_ymd": "20250630", "tokureikaishi_ymd": "", "tokureishuryo_ymd": "", "tekiyo_ymd": "20240901"}, "TestQAG010_01080344": {"atena_code": "100700000100005", "report_name": "自立支援医療受給者証（表面）", "hakkou_ymd": "20240901"}, "TestQAG010_01080345": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080346": {"atena_code": "100700000100005", "report_name": "支給認定決定のお知らせ", "hakkou_ymd": "20240901"}, "TestQAG010_01080401": {"atena_code": "100700000100005", "hakkou_ymd": "20241001"}, "TestQAG010_01080402": {"atena_code": "100700000100005", "ShinseiShubetsu": "再交付", "ShinseiRiyuu": "紛失", "ShinseiYMD": "20241001"}, "TestQAG010_01080403": {"atena_code": "100700000100005", "KetteiYMD": "20241001", "KetteiKekka": "決定"}, "TestQAG010_01080404": {"atena_code": "100700000100005", "report_name": "自立支援医療受給者証（表面）", "hakkou_ymd": "20241001"}, "TestQAG010_01080501": {"atena_code": "100700000100005"}, "TestQAG010_01080502": {"jukyusha_code": "0600148", "ymd_1": "202409"}, "TestQAG010_01080503": {"atena_code": "100700000100005", "ymd_1": "202409"}, "TestQAG010_01080504": {"atena_code": "100700000100005", "ymd_1": "202409"}, "TestQAG010_01080505": {"atena_code": "100700000100005", "ymd_1": "202409"}, "TestQAG010_01080506": {"atena_code": "100700000100005", "ymd_1": "202409", "slect_1": "後期高齢"}, "TestQAG010_01080507": {"atena_code": "100700000100005", "ymd_1": "202409", "slect_1": "後期高齢"}, "TestQAG010_01080508": {"atena_code": "100700000100005", "ymd_1": "202409", "slect_1": "後期高齢"}, "TestQAG010_01080509": {"atena_code": "100700000100005", "ymd_1": "202409", "slect_1": "後期高齢"}, "TestQAG010_01080510": {"kensaku_ym_start": "", "jukyusha_no_end": "202409", "atena_code": "100700000100144"}, "TestQAG010_01080511": {"jukyusha_no": "0", "iryo_kikan": "0", "atena_code": "100700000100144"}, "TestQAG010_01080701": {"atena_code": "100700000100005", "report_name": "自立支援医療受給者証返還届出力", "hakkou_ymd": "20241101"}, "TestQAG010_01080704": {"atena_code": "100700000100005", "report_name": "支給認定取消通知書", "hakkou_ymd": "20241101"}, "TestQAG010_01080702": {"atena_code": "100700000100005", "ShinseiShubetsu": "資格喪失", "ShinseiRiyuu": "死亡", "shinsei_ymd": "20241101", "soushitsu_ymd": "20241101"}, "TestQAG010_01080703": {"atena_code": "100700000100005", "KetteiYMD": "20241101", "KetteiKekka": "決定"}, "TestQAG010_01089801": {"atena_code": "100700000100005", "item10": "1234", "item11": "1234"}, "TestQAG010_01080113": {"atena_code": "100700000100005", "kettei_ymd": "20230701", "kettei_kekka": "決定", "kaishi_ymd": "20230701", "shuryo_ymd": "20240630", "tokureikaishi_ymd": "", "tokureishuryo_ymd": "", "tekiyo_ymd": "20230701", "kofu_ymd": "20230701"}, "TestQAG010_01080114": {"atena_code": "100700000100005", "report_name": "自立支援医療受給者証（表面）", "hakkou_ymd_shinki": "20230701", "hakkou_ymd_kisaihenko": "20230801", "hakkou_ymd_sainintei": "20240701", "hakkou_ymd_henko_jikofutan": "20240801", "hakkou_ymd_henko_iryokikan": "20240901", "hakkou_ymd_saikofu": "20241001"}, "TestQAG010_01080115": {"atena_code": "100700000100005", "report_name": "自己負担上限額管理票", "hakkou_ymd_shinki": "20230701", "hakkou_ymd_sainintei": "20240701", "hakkou_ymd_henko_jikofutan": "20240801"}, "TestQAG010_01080116": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080117": {"atena_code": "100700000100005", "report_name": "認定決定通知書", "hakkou_ymd_shinki": "20230701", "hakkou_ymd_sainintei": "20240701", "hakkou_ymd_henko_jikofutan": "20240801", "hakkou_ymd_henko_iryokikan": "20240901"}, "TestQAG010_01080118": {"atena_code": "100700000100005", "report_name": "支給認定決定のお知らせ", "hakkou_ymd_shinki": "20230701", "hakkou_ymd_sainintei": "20240701", "hakkou_ymd_henko_jikofutan": "20240801", "hakkou_ymd_henko_iryokikan": "20240901"}, "TestQAG010_01080122": {"atena_code": "100700000100005", "shinsei_ymd": "20230701", "ShinseiShubetsu": "新規", "ShinseiRiyuu": "新規申請", "tanto_shokatsuku": "第一区", "tanto_shisho": "012", "uketsuke_basho": "0000000001", "tanto_basho": "0000000001", "shotoku_hantei_nendo": "令和05年", "kofu_hoho": "0000000001", "kanyu_jokyo1": "保険加入", "hokenshurui1": "国民健康保険", "hokenshurui2": "健康保険等", "txt_kanji_meisho": "健", "txt_kanji_meisho2": "医", "txt_kanji_meisho3": "薬", "txt_kanji_meisho4": "訪", "fuyousha": "扶養者", "txt_kigo": "１２３", "txt_bango": "４５６７", "txt_kojin_shiki_bango": "１２", "shikaku_shutoku_ymd": "20230701", "shozaichi_1": "1", "tensuhyo": "0000000001", "tensuhyo2": "0000000004", "tensuhyo3": "0000000006", "hoshin": "0000000001", "Seiho_Begin_ymd": "20230701", "Seiho_End_ymd": "", "renrakuatena": "100100000100011", "iryoukikan_shozaichi_kubun_2": "1", "iryoukikan_shozaichi_kubun_3": "1", "iryoukikan_shozaichi_kubun_4": "1"}, "TestQAG010_01080124": {"atena_code": "100700000100005", "gyomu_select": "障害", "jigyo_select": "自立支援医療(更生)", "shoriKubun_select": "年金照会抽出", "shoriBunrui_select": "年金照会抽出（0000052000）"}, "TestQAG010_01080123": {"atena_code": "100700000100005", "shokai_cmb_insert": "追加", "shokai_insert": "日本年金機構"}, "TestQAG010_01080125": {"atena_code": "100700000100005", "shintatsu1_ymd": "20230701", "hantei_ymd": "20230701", "hantei_yotei_ji": "10", "hantei_yotei_fun": "00"}, "TestQAG010_01080126": {"atena_code": "100700000100005", "report_name1": "判定依頼書", "report_name2": "調査書", "hakkou_ymd": "20230701"}, "TestQAG010_01080127": {"atena_code": "100700000100005", "gyomu_select": "障害", "jigyo_select": "自立支援医療(更生)", "shoriKubun_select": "年金照会抽出", "shoriBunrui_select": "年金照会抽出（0000052000）"}, "TestQAG010_01080128": {"atena_code": "100700000100005"}, "TestQAG010_01080129": {"atena_code": "100700000100005", "shintatsu_hantei1_ymd": "20230701", "shintatsu_hantei1": "決定"}, "TestQAG010_01080130": {"atena_code": "100700000100005", "kettei_ymd": "20230701", "kettei_kekka": "却下", "kettei_riyu": "要件非該当のため", "area_kyakka_riyu": "却下理由入力テスト★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★"}, "TestQAG010_01080131": {"atena_code": "100700000100005", "report_name": "却下通知書", "hakkou_ymd": "20230701"}, "TestQAG010_01080132": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080133": {"atena_code": "100700000100005", "kettei_ymd": "20230701", "kettei_kekka": "決定", "kaishi_ymd": "20230701", "shuryo_ymd": "20240630", "tokureikaishi_ymd": "", "tokureishuryo_ymd": "", "tekiyo_ymd": "20230701", "kofu_ymd": "20230701"}, "TestQAG010_01080134": {"atena_code": "100700000100005", "report_name": "自立支援医療受給者証（表面）", "hakkou_ymd": "20230701"}, "TestQAG010_01080135": {"atena_code": "100700000100005", "report_name": "自己負担上限額管理票", "hakkou_ymd": "20230701"}, "TestQAG010_01080136": {"atena_code": "100700000100005", "report_name": "調査書", "hakkou_ymd": "20240701"}, "TestQAG010_01080137": {"atena_code": "100700000100005", "report_name": "認定決定通知書", "hakkou_ymd": "20230701"}, "TestQAG010_01080138": {"atena_code": "100700000100005", "report_name": "支給認定決定のお知らせ", "hakkou_ymd": "20230701"}, "TestQAG010_01089901": {"atena_code": "100700000100005"}, "TestQAG010_01089902": {"atena_code": "100700000100005"}, "TestQAG010_01089903": {"atena_code": "100700000100005"}, "TestQAG010_01089904": {"atena_code": "100700000100005"}, "TestQAG010_01089905": {"item1": "202401", "item2": "202703"}, "TestQAG010_01089906": {"atena_code": "100700000100005", "gyomu_select": "障害", "jigyo_select": "自立支援医療(更生)", "shoriKubun_select": "バッチ処理", "shoriBunrui_select": "月次処理", "chiho_nm": "住記異動リスト出力処理", "chiho_nm1": "住記異動自動喪失処理", "ymd_1": "20220101", "ymd_2": "20251001"}, "TestQAG010_01089908": {"iryoukikan_code": "0111234567", "yukokikan_kaishi": "20240101", "shitei_ymd": "20240101"}, "TestQAG010_01089909": {"hokensya_code": "99999999", "kanji_name": "保", "tekiyou_kaishi_ymd": "20240101"}}