import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC030_1050202(FukushiSiteTestCaseBase):
    """TestQAC030_1050202"""

    def setUp(self):
        case_data = self.test_data["TestQAC030_1050202"]
        super().setUp()

    # 資格履歴より所得状況届が出力できることを確認する。
    def test_QAC030_1050202(self):
        """所得状況届出力"""

        case_data = self.test_data["TestQAC030_1050202"]
        atena_code = case_data.get("atena_code", "")
        report_name = case_data.get("report_name", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「経過的福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC030")

        # 8 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_8")

        # 9 経過的福祉手当資格管理画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 10 帳票印刷画面: 表示
        self.switch_online_report_type("申請書")
        self.screen_shot("帳票印刷画面_10")

        # 11 帳票印刷画面: 「経過的福祉手当　所得状況届」行の印刷チェックボックス選択
        exec_params = [
            {
                "report_name": report_name,
                 "params": [
                     {"title": "宛名コード", "value": atena_code},
                 ]
             }
        ]
        ret = self.print_online_reports(report_param_list=exec_params)
        self.screen_shot("帳票印刷画面_11")

        # 12 帳票印刷画面: 「印刷」ボタン押下

        # 13 帳票印刷画面: 経過的福祉手当　所得状況届「ファイルを開く(O)」ボタンを押下
        # Assert: 「プレビューを表示しました」のメッセージチェック

        # 14 経過的福祉手当　所得状況届（PDF）: 表示
        # self.screen_shot("経過的福祉手当　所得状況届（PDF）_14")

        # 15 経過的福祉手当　所得状況届（PDF）: ×ボタン押下でPDFを閉じる

        # 16 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 17 経過的福祉手当資格管理画面: 表示
        self.screen_shot("経過的福祉手当資格管理画面_17")
