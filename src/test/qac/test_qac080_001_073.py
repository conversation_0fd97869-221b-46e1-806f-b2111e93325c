import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAC080001073(FukushiSiteTestCaseBase):
    """TESTQAC080001073"""
        
    def test_case_qac080_001_073(self):
        """test_case_qac080_001_073"""
        driver = None
        test_data = self.common_test_data
        
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("case_common_qac080_atena_code16"), gyoumu_code="QAC080")
        self.save_screenshot_migrate(driver, "QAC080-045-74-1", True)
        self.find_element(By.ID,"CmdShinsei").click()
        #self.find_element(By.ID,"ShinseiShubetsuCmb").click()
        #self.select_Option(driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"認定請求")
        self.find_element(By.ID,"ShinseiShubetsuCmb").send_keys("新規")
        # time.sleep(2)
        #self.find_element(By.ID,"ShinseiShubetsuCmb").click()

        #self.find_element(By.ID,"ShinseiRiyuuCmb").send_keys("出生")
        #self.find_element(By.ID,"CmdKakutei").click()
        self.find_element(By.ID,"ShinseiRiyuuCmb").send_keys("出生")
        self.find_element(By.ID,"CmdKakutei").click()
        #self.find_element(By.ID,"ShinseiRiyuuCmb").click()
        #self.select_Option(driver,self.find_element(By.ID,"ShinseiRiyuuCmb"),"出生")
        #self.find_element(By.ID,"CmdKakutei").click()
        #self.find_element(By.ID,"ShinseiRiyuuCmb").click()
        self.find_element(By.ID,"CmdKakutei").click()
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("")
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("20210701")

        self.select_Option(driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")
        # self.find_element(By.ID,"btnCommon16").click()
        # 住所管理ボタン押下
        self.find_element(By.ID,"btnCommon14").click()
        # self.assertEqual(u" ",self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-045-74-6", True)
        self.find_element(By.ID,"CmdInsert").click()
        self.find_element(By.ID,"TxtLeftZip").send_keys("")
        self.find_element(By.ID,"TxtLeftZip").send_keys("123")
        self.find_element(By.ID,"TxtRightZip").send_keys("")
        self.find_element(By.ID,"TxtRightZip").send_keys("4567")
        self.find_element(By.ID,"TxtJusho").send_keys("")
        self.find_element(By.ID,"TxtJusho").send_keys(u"アイネス県アイネス区福祉住所")
        self.find_element(By.ID,"TxtKataGaki").send_keys("")
        self.find_element(By.ID,"TxtKataGaki").send_keys(u"福祉方書－０００３００２８")
        self.find_element(By.ID,"TxtkanaShimei").send_keys("")
        self.find_element(By.ID,"TxtkanaShimei").send_keys(u"ジテ　タロウ")
        self.find_element(By.ID,"TxtShimei").send_keys("")
        self.find_element(By.ID,"TxtShimei").send_keys(u"児手　太郎")

        self.find_element(By.ID,"CmdSubmit").click()
        alert_texts = self.alert_ok_many()
        self.assertEqual(u"更新します。よろしいですか？", alert_texts[0])
        self.assertEqual(u"住所が市内住所検索時と異なります。住所コード、番地等が正しいか確認して下さい。", alert_texts[1])
        self.save_screenshot_migrate(driver, "QAC080-045-74-9", True)

        self.find_element(By.ID,"CmdDelete").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-045-74-10", True)
        self.find_element(By.ID,"CmdKyojuKirikaeButton").click()
        self.find_element(By.ID,"CmdInsert").click()
        self.find_element(By.ID,"TxtLeftZip").send_keys("")
        self.find_element(By.ID,"TxtLeftZip").send_keys("123")
        self.find_element(By.ID,"TxtRightZip").send_keys("")
        self.find_element(By.ID,"TxtRightZip").send_keys("4567")
        self.find_element(By.ID,"TxtJusho").send_keys("")
        self.find_element(By.ID,"TxtJusho").send_keys(u"アイネス県アイネス区福祉住所")
        self.find_element(By.ID,"TxtKataGaki").send_keys("")
        self.find_element(By.ID,"TxtKataGaki").send_keys(u"福祉方書－０００３００２８")
        self.find_element(By.ID,"TxtkanaShimei").send_keys("")
        self.find_element(By.ID,"TxtkanaShimei").send_keys(u"ジテ　タロウ")
        self.find_element(By.ID,"TxtShimei").send_keys("")
        self.find_element(By.ID,"TxtShimei").send_keys(u"児手　太郎")

        self.find_element(By.ID,"CmdSubmit").click()
        alert_texts = self.alert_ok_many()
        self.assertEqual(u"更新します。よろしいですか？", alert_texts[0])
        self.assertEqual(u"住所が市内住所検索時と異なります。住所コード、番地等が正しいか確認して下さい。", alert_texts[1])
        self.save_screenshot_migrate(driver, "QAC080-045-74-14", True)

        self.find_element(By.ID,"CmdDelete").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-045-74-15", True)
        self.find_element(By.ID,"span_CmdTodokedeKirikaeButton").click()
        self.find_element(By.ID,"CmdInsert").click()
        self.find_element(By.ID,"TxtLeftZip").send_keys("")
        self.find_element(By.ID,"TxtLeftZip").send_keys("123")
        self.find_element(By.ID,"TxtRightZip").send_keys("")
        self.find_element(By.ID,"TxtRightZip").send_keys("4567")
        self.find_element(By.ID,"TxtJusho").send_keys("")
        self.find_element(By.ID,"TxtJusho").send_keys(u"アイネス県アイネス区福祉住所")
        self.find_element(By.ID,"TxtKataGaki").send_keys("")
        self.find_element(By.ID,"TxtKataGaki").send_keys(u"福祉方書－０００３００２８")
        self.find_element(By.ID,"TxtkanaShimei").send_keys("")
        self.find_element(By.ID,"TxtkanaShimei").send_keys(u"ジテ　タロウ")
        self.find_element(By.ID,"TxtShimei").send_keys("")
        self.find_element(By.ID,"TxtShimei").send_keys(u"児手　太郎")
        self.find_element(By.ID,"CmdSubmit").click()
        self.assertEqual(u"住所が市内住所検索時と異なります。住所コード、番地等が正しいか確認して下さい。", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-045-74-19", True)
        self.find_element(By.ID,"CmdDelete").click()
        self.save_screenshot_migrate(driver, "QAC080-045-74-20", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC080-045-74-21", True)
        self.find_element(By.ID,"GOBACK").click()
        #self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC080-045-74-22", True)