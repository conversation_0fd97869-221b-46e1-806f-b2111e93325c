import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAC050010001(FukushiSiteTestCaseBase):
    """TESTQAC050010001"""
        
    def test_case_qac050_010_001(self):
        """test_case_qac050_010_001"""
        driver = None
        test_data = self.common_test_data
        
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("case_common_qac050_atena_code3"), gyoumu_code="QAC050")
        self.save_screenshot_migrate(driver, "QAC050-011-11-1", True)
        self.find_element(By.ID,"CmdShinsei").click()
        self.find_element(By.ID,"ShinseiShubetsuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"認定請求")
        # time.sleep(2)
        # self.find_element(By.ID,"<PERSON>seiShubetsuCmb").click()

        self.find_element(By.ID,"CmdKakutei").click()
        self.find_element(By.ID,"ShinseiRiyuuCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"ShinseiRiyuuCmb"),"障害")
        # time.sleep(2)
        # self.find_element(By.ID,"CmdKakutei").click()
        #self.find_element(By.ID,"ShinseiRiyuuCmb").click()
        self.find_element(By.ID,"CmdKakutei").click()
        # self.find_element(By.XPATH,"//img[@onclick=\"_wr_calendar(document.getElementById('_wr_calendar0'), document.getElementById('TxtShinseiYMD'), 'type=ymd&MIRAI=OK&fmt=1');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=1 | ]]
        # self.find_element(By.ID,u"月").click()
        # self.select_Option(driver,self.find_element(By.ID,u"月")).select_by_visible_text("3")
        # self.find_element(By.ID,u"月").click()
        # self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和03年03月01日');\"]").click()
        self.find_element(By.ID,u"TxtShinseiYMD").send_keys("")
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("20210301")

        self.select_Option(driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")
        # time.sleep(2)
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]
        self.find_element(By.ID,"JyukyusyaKbnCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"JyukyusyaKbnCmb"),"父または母")
        # self.find_element(By.ID,"JyukyusyaKbnCmb").click()
        # self.find_element(By.XPATH,"//img[@onclick=\"_wr_calendar_ym(document.getElementById('_wr_calendar2'), document.getElementById('TxtKaitei'), 'type=YM&MIRAI=OK&fmt=1&top=1.3em&left=-10em');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=3 | ]]
        # self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和03年04月');\"]").click()
        self.find_element(By.ID,u"TxtKaitei").send_keys("")
        self.find_element(By.ID,"TxtKaitei").send_keys("令和03年04月")
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]
        self.find_element(By.ID,"CmdJidouTsuika").click()
        self.save_screenshot_migrate(driver, "QAC050-011-11-7", True)
        self.find_element(By.ID,"Sel2").click()
        # self.find_element(By.XPATH,"//img[@onclick=\"_wr_calendar(document.getElementById('_wr_calendar3'), document.getElementById('TxtJiyuYMD'), 'type=ymd&MIRAI=OK&fmt=1');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=3 | ]]
        # self.find_element(By.ID,u"月").click()
        # self.select_Option(driver,self.find_element(By.ID,u"月")).select_by_visible_text("4")
        # self.find_element(By.ID,u"月").click()
        # self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和03年04月01日');\"]").click()
        self.find_element(By.ID,u"TxtJiyuYMD").send_keys("")
        self.find_element(By.ID,"TxtJiyuYMD").send_keys("令和03年04月01日")
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]
        # self.find_element(By.XPATH,"//img[@onclick=\"_wr_calendar(document.getElementById('_wr_calendar4'), document.getElementById('TxtToushoShikyuYM'), 'type=ymd&MIRAI=OK&fmt=1');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=4 | ]]
        # self.find_element(By.ID,u"月").click()
        # self.select_Option(driver,self.find_element(By.ID,u"月")).select_by_visible_text("4")
        # self.find_element(By.ID,u"月").click()
        # self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和03年04月01日');\"]").click()
        self.find_element(By.ID,u"TxtToushoShikyuYM").send_keys("")
        self.find_element(By.ID,"TxtToushoShikyuYM").send_keys("令和03年04月01日")
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]
        self.find_element(By.ID,"CmbGaitoJiyu").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbGaitoJiyu"),"障害")
        # time.sleep(2)
        # self.find_element(By.ID,"CmbGaitoJiyu").click()
        self.find_element(By.ID,"CmdInputCompleteButton").click()
        # self.find_element(By.ID,"btnCommon7").click()
        # self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"CmdGetsugakuKeisan").click()
        self.find_element(By.ID,"btnCommon3").click()
        self.save_screenshot_migrate(driver, "QAC050-011-11-14", True)
        self.find_element(By.ID,"CmdNo0").click()
        self.save_screenshot_migrate(driver, "QAC050-011-11-16", True)
        self.find_element(By.ID,"CmdInput").click()

        self.find_element(By.ID,"CmdRegist").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.find_element(By.XPATH,"//button[@id='GOBACK']/u").click()
        self.save_screenshot_migrate(driver, "QAC050-011-11-19", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC050-011-11-22", True)
        self.find_element(By.ID,"CmdGetsugakuKeisan").click()

        self.find_element(By.ID,"TxtpreShikyuKaishiYMD").click()
        self.find_element(By.ID,"TxtpreShikyuKaishiYMD").send_keys("20210401")

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        time.sleep(1)

        # 本庁進達入力ボタン押下
        self.find_element(By.ID,"span_CmdShintatsu1").click()
        time.sleep(1)

        self.find_element(By.ID,"TxtShintatsu1YMD").send_keys("")
        self.find_element(By.ID,"TxtShintatsu1YMD").send_keys("令和03年03月15日")

        self.find_element(By.ID,"TxtpreShikyuKaishiYMD").click()
        self.find_element(By.ID,"TxtpreShikyuKaishiYMD").send_keys("20210401")

        self.find_element(By.ID,"CmdGetsugakuKeisan").click()

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        time.sleep(1)

        # 本庁進達結果入力ボタン押下
        self.find_element(By.ID,"span_CmdShintatsu1Kekka").click()

        self.select_Option(driver,self.find_element(By.ID,"Shintatsu1HanteiCmb"),"決定")

        self.find_element(By.ID,"TxtpreShikyuKaishiYMD").click()
        self.find_element(By.ID,"TxtpreShikyuKaishiYMD").send_keys("20210401")

        self.find_element(By.ID,"CmdGetsugakuKeisan").click()

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        time.sleep(1)

        self.find_element(By.ID,"CmdKettei").click()
        self.save_screenshot_migrate(driver, "QAC050-011-11-26", True)
        # self.find_element(By.XPATH,"//img[@onclick=\"_wr_calendar(document.getElementById('_wr_calendar2'), document.getElementById('TxtKetteiYMD'), 'type=ymd&MIRAI=OK&fmt=1');\"]").click()
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | index=3 | ]]
        # self.find_element(By.ID,u"月").click()
        # self.select_Option(driver,self.find_element(By.ID,u"月")).select_by_visible_text("4")
        # self.find_element(By.ID,u"月").click()
        # self.find_element(By.XPATH,u"//td[@onclick=\"button_click('令和03年04月01日');\"]").click()
        self.find_element(By.ID,u"TxtKetteiYMD").send_keys("")
        self.find_element(By.ID,"TxtKetteiYMD").send_keys("令和03年04月01日")
        # ERROR: Caught exception [ERROR: Unsupported command [selectFrame | relative=parent | ]]
        self.find_element(By.ID,"KetteiKekkaCmb").click()
        self.select_Option(driver,self.find_element(By.ID,"KetteiKekkaCmb"),"決定")
        # time.sleep(2)
        # self.find_element(By.ID,"KetteiKekkaCmb").click()

        self.find_element(By.ID,u"TxtpreShikyuKaishiYMD").click()
        self.find_element(By.ID,"TxtpreShikyuKaishiYMD").send_keys("20210401")

        self.find_element(By.ID,"CmdGetsugakuKeisan").click()

        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        time.sleep(1)

        self.find_element(By.ID,"CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAC050-011-11-30", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC050-011-11-31", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAC050-011-11-32", True)