import time
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01010302402(FukushiSiteTestCaseBase):
    """TESTQAK01010302402"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10302_402.sql", params=atena_list)
        super().setUp()    

    def test_case_qak010_10302_402(self):
        """test_case_qak010_10302_402"""

        driver = None
        case_data = self.test_data[self.__class__.__name__]
        atena_code = case_data.get("宛名番号", "")

        # ログイン
        self.do_login()
        #「賦課管理」ボタン押下
        self.click_button_by_label("賦課管理")
        # 「宛名番号」テキストボックス入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        # 「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("10302-402-06")