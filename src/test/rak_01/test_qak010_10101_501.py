import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01010101501(FukushiSiteTestCaseBase):
    """TESTQAK01010101501"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10101_501.sql", params=atena_list)
        super().setUp()

    def test_case_qak010_10101_501(self):
        """test_case_qak010_10101_501"""
        driver = None
        case_data = self.test_data[self.__class__.__name__]
        atenaCD = case_data.get("宛名番号", "")
        # juusho_cd3 = case_data.get("住所コード3", "")
        # juusho_cd4 = case_data.get("住所コード4", "")
        juusho_pattern = case_data.get("住所パターン", "")
        banchi = case_data.get("番地", "")
        gou = case_data.get("号", "")
        gou_edaban = case_data.get("号枝番", "")
        gou_ko_edaban = case_data.get("号小枝番", "")
        yuubin_no1 = case_data.get("郵便番号1", "")
        yuubin_no2 = case_data.get("郵便番号2", "")
        startYMD = case_data.get("有効期間_開始", "")
        kanaShimei_Uji = case_data.get("氏（フリガナ）", "")
        kanaShimei_Na = case_data.get("名（フリガナ）", "")
        shimei_Uji = case_data.get("氏", "")
        shimei_Na = case_data.get("名", "")

        # ログイン
        self.do_login()

        # 後期高齢者医療　資格
        # →「資格管理」ボタン押下
        self.click_button_by_label("資格管理")

        # 「宛名番号」テキストボックス入力
        self.form_input_by_id(idstr="AtenaCD", value=atenaCD)

        # 「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("10101-501-06")

        # 「住所管理」ボタン押下
        self.click_button_by_label("住所管理")
        self.screen_shot("10101-501-08")

        # 「用途」コンボボックス
        # →「共通」を選択
        self.form_input_by_id(idstr="CmbYouto", text="共通")

        # 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 「市内」ボタン押下
        self.click_button_by_label("市内(N)")

        # 以下項目を入力
        # ・「住所コード」
        # ・「住所パターン」
        # ・「番地」
        # ・「号」
        # ・「号枝番」
        # ・「号小枝番」
        # ・「郵便番号1」
        # ・「郵便番号2」
        # 「検索」ボタン押下

        # self.form_input_by_id(idstr="JushoCode3", value=juusho_cd3)
        # self.form_input_by_id(idstr="JushoCode4", value=juusho_cd4)
        self.form_input_by_id(idstr="P", text=juusho_pattern)
        self.form_input_by_id(idstr="Banchi", value=banchi)
        self.form_input_by_id(idstr="Go", value=gou)
        self.form_input_by_id(idstr="Goedaban", value=gou_edaban)
        self.form_input_by_id(idstr="Gokoedaban", value=gou_ko_edaban)
        self.form_input_by_id(idstr="Yubin1", value=yuubin_no1)
        self.form_input_by_id(idstr="Yubin2", value=yuubin_no2)

        # 「入力決定」ボタン押下
        self.click_button_by_label("入力決定(Enter)")

        # 以下項目を入力
        # ・「有効期間 開始」
        # ・「氏（フリガナ）」
        # ・「名（フリガナ）」
        # ・「氏」
        # ・「名」
        self.form_input_by_id(idstr="TxtStartYMD", value=startYMD)
        self.form_input_by_id(idstr="TxtKanaShimei_Uji", value=kanaShimei_Uji)
        self.form_input_by_id(idstr="TxtKanaShimei_Na", value=kanaShimei_Na)
        self.form_input_by_id(idstr="TxtShimei_Uji", value=shimei_Uji)
        self.form_input_by_id(idstr="TxtShimei_Na", value=shimei_Na)

        # 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        self.assert_message_base_header("登録しました。")
        self.screen_shot("10101-501-18")

        # 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("10101-501-20")

        # 「戻る」ボタン押下
        self.return_click()

        # 「戻る」ボタン押下
        self.return_click()