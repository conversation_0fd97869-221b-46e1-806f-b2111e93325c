
from base.fukushi_case import FukushiSiteTestCaseBase
from selenium.webdriver.common.by import By

class Test_QSNF341(FukushiSiteTestCaseBase):
   """Test_QSNF341"""
   def test_case_001(self):
        """test_case_001"""  
        driver = None
        test_data = self.common_test_data
           
        self.do_login()
        self.click_button_by_label("収納管理")
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '養護老人ホーム入所']").click()
        self.driver.find_element(By.ID, "CmdKakutei").click()
        self.driver.find_element(By.ID, "CmdButton1").click()
        self.driver.find_element(By.ID, "AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qsnf341_atena_code"))            
        self.driver.find_element(By.ID, "Kensaku").click()
        self.driver.find_element(By.ID, "Sel1").click()
        self.find_common_buttons()
        self.common_button_click(button_text="確定督手延滞金入力")
        
        self.screen_shot("QSNF341_1",caption="QSNF341_画面初期化")
        # TODO(ri_hou) 一覧データがなくなる            
      #   self.driver.find_element(By.ID, "CmdShusei").click()
      #   self.screen_shot("QSNF341_2",caption="QSNF341_修正")         
      #   self.driver.find_element(By.ID, "TxtKakuteiEntaikin_12").click()
      #   self.driver.find_element(By.ID, "TxtKakuteiEntaikin_12").send_keys("100")
      #   self.driver.find_element(By.ID, "TxtKakuteiTokute_12").click()
      #   self.driver.find_element(By.ID, "TxtKakuteiTokute_12").send_keys("0")
      #   self.driver.find_element(By.ID, "CmdTouroku").click()
      #   assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
      #   self.driver.switch_to.alert.accept()
      #   self.screen_shot("QSNF341_3",caption="QSNF341_登録ボタン押下_insert")           
      #   self.driver.find_element(By.ID, "CmdShusei").click()
      #   self.driver.find_element(By.ID, "TxtKakuteiEntaikin_12").click()
      #   self.driver.find_element(By.ID, "TxtKakuteiEntaikin_12").send_keys("200")
      #   self.driver.find_element(By.ID, "CmdTouroku").click()
      #   assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
      #   self.driver.switch_to.alert.accept()
      #   self.screen_shot("QSNF341_4",caption="QSNF341_登録ボタン押下_update")              
      #   self.driver.find_element(By.ID, "span_CmdShusei").click()
      #   self.driver.find_element(By.ID, "TxtKakuteiEntaikin_12").click()
      #   self.driver.find_element(By.ID, "TxtKakuteiEntaikin_12").send_keys("")        
      #   self.driver.find_element(By.ID, "TxtKakuteiTokute_12").click()
      #   self.driver.find_element(By.ID, "TxtKakuteiTokute_12").send_keys("")           
      #   self.driver.find_element(By.ID, "span_CmdTouroku").click()
      #   assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
      #   self.driver.switch_to.alert.accept()
      #   self.screen_shot("QSNF341_5",caption="QSNF341_登録ボタン押下_delete")              
      #   self.driver.find_element(By.ID, "span_CmdShusei").click()
      #   self.driver.find_element(By.ID, "span_CmdShoki").click() 
      #   self.screen_shot("QSNF341_6",caption="QSNF341_初期表示")
