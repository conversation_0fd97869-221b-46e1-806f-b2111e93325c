import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QATF012(FukushiSiteTestCaseBase):
    """Test_QATF012"""

    def test_case_001(self):
        """test_case_001"""
        driver = None
        self.do_login()
        self.driver.find_element(By.ID, "CmdProcess5_3").click()
        self.save_screenshot_migrate(driver, "QATF012_01_画面初期化" , True)

        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '高齢']").click()
        self.driver.find_element(By.ID, "CmbJigyo").click()
        dropdown = self.driver.find_element(By.ID, "CmbJigyo")
        dropdown.find_element(By.XPATH, "//option[. = '交付券2']").click()
        self.driver.find_element(By.ID, "CmbRenban").click()
        dropdown = self.driver.find_element(By.ID, "CmbRenban")
        dropdown.find_element(By.XPATH, "//option[. = '交付内容１']").click()
        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.save_screenshot_migrate(driver, "QATF012_02_検索ボタン押下" , True)

        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "Radio1_1").click()

        if self.exist_item(item_type="select", item_id="CmbShokanku"):
            self.driver.find_element(By.ID, "CmbShokanku").click()
            dropdown = self.driver.find_element(By.ID, "CmbShokanku")
            dropdown.find_element(By.XPATH, "//option[. = '全区']").click()
            self.driver.find_element(By.ID, "TxtNengetsuSta").click()

        self.driver.find_element(By.ID, "TxtNengetsuSta").send_keys("平成31年01月")
        self.driver.find_element(By.ID, "span_CmdKensaku").click()
        self.save_screenshot_migrate(driver, "QATF012_03_検索ボタン押下" , True)
        self.driver.find_element(By.ID, "GOBACK").click()
        self.save_screenshot_migrate(driver, "QATF012_04_「戻る」ボタン押下" , True)
        self.driver.find_element(By.ID, "span_CmdShokiHyoji").click()
        self.save_screenshot_migrate(driver, "QATF012_05_「初期表示」ボタン押下" , True)
  
