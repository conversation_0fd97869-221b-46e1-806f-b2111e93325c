import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAR010050(FukushiSiteTestCaseBase):
    """TestQAR010050"""
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAR010-050.sql", params=atena_list)
        super().setUp()

    def test_case_qar010_050(self):
        """test_case_qar010_050"""
        driver = None
        test_data = self.common_test_data
        self.do_login()

        self.click_button_by_label("収納管理")
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmbGyomu")))
        #self.find_element(By.ID,"CmbGyomu").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbGyomu"),"高齢")
        self.find_element(By.ID,"CmbGyomu").send_keys("高齢")
        # WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "CmbJigyo")))
        #self.find_element(By.ID,"CmbJigyo").click()
        #self.select_Option(driver,self.find_element(By.ID,"CmbJigyo"),"養護老人ホーム入所")
        self.find_element(By.ID,"CmbJigyo").send_keys("養護老人ホーム入所")
        self.find_element(By.ID,"CmdKakutei").click()
        self.click_button_by_label("納付書バーコード読取")
        self.find_element(By.ID,"TxtBarCode").click()
        self.find_element(By.ID,"TxtBarCode").send_keys("")
        self.find_element(By.ID,"TxtBarCode").send_keys("12")
        self.find_element(By.ID,"TxtRyoshuYMD").click()
        self.find_element(By.ID,"TxtRyoshuYMD").send_keys("")
        self.find_element(By.ID,"TxtRyoshuYMD").send_keys("20210301")
        self.find_element(By.ID,"TxtShunoYMD").click()
        self.find_element(By.ID,"TxtShunoYMD").send_keys("")
        self.find_element(By.ID,"TxtShunoYMD").send_keys("20210302")
        self.find_element(By.ID,"CmdKeshikomi").click()
        self.find_element(By.ID,"TxtBarCode").click()
        self.find_element(By.ID,"TxtBarCode").send_keys("")
        self.find_element(By.ID,"TxtBarCode").send_keys("13")
        self.find_element(By.ID,"span_CmdKeshikomi").click()
        self.save_screenshot_migrate(driver, "QAR010-050-09", True)
        self.find_element(By.ID,"span_CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAR010-050-11", True)
        self.find_element(By.ID,"TxtBarCode").click()
        self.find_element(By.ID,"TxtBarCode").send_keys("")
        self.find_element(By.ID,"TxtBarCode").send_keys("12")
        self.find_element(By.ID,"TxtRyoshuYMD").click()
        self.find_element(By.ID,"TxtRyoshuYMD").send_keys("")
        self.find_element(By.ID,"TxtRyoshuYMD").send_keys("20210301")
        self.find_element(By.ID,"TxtShunoYMD").click()
        self.find_element(By.ID,"TxtShunoYMD").send_keys("")
        self.find_element(By.ID,"TxtShunoYMD").send_keys("20210302")
        self.find_element(By.ID,"span_CmdKeshikomi").click()
        self.find_element(By.ID,"TxtBarCode").click()
        self.find_element(By.ID,"TxtBarCode").send_keys("")
        self.find_element(By.ID,"TxtBarCode").send_keys("13")
        self.find_element(By.ID,"CmdKeshikomi").click()
        self.save_screenshot_migrate(driver, "QAR010-050-15", True)
        # self.accept_next_alert = True
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAR010-050-17", True)
        self.find_element(By.ID,"GOBACK").click()
        self.click_button_by_label("受入データ修正")
        self.find_element(By.ID,"Sel4").click()
        self.save_screenshot_migrate(driver, "QAR010-050-21", True)
        self.find_element(By.ID,"span_CmdCheck").click()
        self.save_screenshot_migrate(driver, "QAR010-050-23", True)
        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()
        # 消込処理
        self.click_button_by_label("消込処理")

        self.find_element(By.ID,"item4").click()
        self.find_element(By.ID,"item4").send_keys("")
        self.find_element(By.ID,"item4").send_keys("20210302")
        self.find_element(By.ID,"span_ExecuteButton").click()
        self.assertEqual(u"処理を開始します。よろしいですか？", self.alert_ok())
        time.sleep(10)
        self.find_element(By.ID,"GOBACK").click()

        self.click_button_by_label("収納情報管理")
        self.find_element(By.ID,"AtenaCD").click()
        self.find_element(By.ID,"AtenaCD").send_keys("")
        self.find_element(By.ID,"AtenaCD").send_keys(test_data.get("qar010_atena_code3",""))
        # time.sleep(10)
        self.find_element(By.ID,"span_Kensaku").click()
        self.save_screenshot_migrate(driver, "QAR010-050-36", True)
        self.find_common_buttons()
        self.common_button_click(button_text="納付履歴")
        
        self.save_screenshot_migrate(driver, "QAR010-050-38", True)
        self.find_element(By.ID,"Sel7").click()
        self.save_screenshot_migrate(driver, "QAR010-050-40", True)
        # self.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # self.save_screenshot("..//evidence//" + "QAR010-050//" + "QAR010-050-40-2" +".png")
        self.find_element(By.ID,"span_CmdSyuSei").click()
        self.find_element(By.ID,"TextRyoSyuMonth").click()
        self.find_element(By.ID,"TextRyoSyuMonth").send_keys("")
        self.find_element(By.ID,"TextRyoSyuMonth").send_keys("20210228")
        # self.accept_next_alert = True
        self.find_element(By.ID,"CmdShokiHyoji").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAR010-050-44", True)
        # self.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # self.save_screenshot("..//evidence//" + "QAR010-050//" + "QAR010-050-44-2" +".png")
        self.find_element(By.ID,"CmdSyuSei").click()
        self.find_element(By.ID,"TextRyoSyuMonth").click()
        self.find_element(By.ID,"TextRyoSyuMonth").send_keys("")
        self.find_element(By.ID,"TextRyoSyuMonth").send_keys("20210228")
        # self.accept_next_alert = True
        self.find_element(By.ID,"CmdToroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAR010-050-48", True)
        # self.accept_next_alert = True
        self.find_element(By.ID,"span_CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAR010-050-50", True)
        self.find_element(By.ID,"Sel7").click()

        # self.accept_next_alert = True
        self.find_element(By.ID,"span_CmdSakujo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAR010-050-54", True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAR010-050-56", True)
