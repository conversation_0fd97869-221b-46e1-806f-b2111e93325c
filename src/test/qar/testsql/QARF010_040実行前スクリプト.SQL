DELETE WR$$JICHITAI_CODE$$QA..QSN充当内容
WHERE 業務コード = 'QAR010' and 福祉事務所コード='00000'  and 宛名コード = '$$QAR010_ATENACODE$$'
INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブ管理
(自治体コード, 福祉事務所コード, 業務コード, 処理区分, 処理分類, 処理連番, ジョブID, ジョブ名, ジョブ説明, 非表示フラグ, 実行レベル, 実行不可区分, 実行履歴表示区分, 実行確認キー警告レベル, プレビュージョブステップID, トレースレベル, デフォルトパラメータ種別, オンライン起動F, ジョブ起動制限F, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', '0000100001', '0000000001', '0000099999', 'QARJTST1       ', '戸籍調査交付依頼出力', '戸籍調査交付依頼出力のダミージョブ', '0', 0, '0000000001', '0', '0', 0, '0', '0', '0', '0', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');

INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブステップ管理
(自治体コード, 福祉事務所コード, 業務コード, ジョブID, ジョブステップID, ジョブステップ名, 実行プログラムID, 帳票ID, 出力分類区分, 帳票名, 印刷モード, 固定長パラメータ領域, テキストソートパラメータ, 非実行フラグ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', 'QARJTST1       ', 10, '検証処理', 'QAZBTST1       ', ' ', '0', ' ', ' ', ' ', ' ', '0', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');

INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブパラメータ管理
(自治体コード, 福祉事務所コード, 業務コード, ジョブID, パラメータID, パラメータ見出し, パラメータ名, パラメータ説明, パラメータキー, 初期値, 非表示フラグ, 必須項目, 実行確認キー, パラメータ区分, 桁数, 属性, 日付フォーマット番号, 福祉共通コード, 接続先識別コード, コンボ取得テーブル, コンボ取得SQL, リンク先遷移コマンド名, リンク先遷移パラメータ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', 'QARJTST1       ', 10, '自治体コード', '自治体コード', '自治体コード', '自治体コード', ' ', '1', '0', '0', '0000000001', 5, '0000000001', 0, '0000000000', '          ', ' ', ' ', ' ', ' ', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');
INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブパラメータ管理
(自治体コード, 福祉事務所コード, 業務コード, ジョブID, パラメータID, パラメータ見出し, パラメータ名, パラメータ説明, パラメータキー, 初期値, 非表示フラグ, 必須項目, 実行確認キー, パラメータ区分, 桁数, 属性, 日付フォーマット番号, 福祉共通コード, 接続先識別コード, コンボ取得テーブル, コンボ取得SQL, リンク先遷移コマンド名, リンク先遷移パラメータ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', 'QARJTST1       ', 20, '業務コード', '業務コード', '業務コード', '業務コード', ' ', '1', '0', '0', '0000000002', 6, '0000000001', 0, '0000000000', '          ', ' ', ' ', ' ', ' ', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');
INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブパラメータ管理
(自治体コード, 福祉事務所コード, 業務コード, ジョブID, パラメータID, パラメータ見出し, パラメータ名, パラメータ説明, パラメータキー, 初期値, 非表示フラグ, 必須項目, 実行確認キー, パラメータ区分, 桁数, 属性, 日付フォーマット番号, 福祉共通コード, 接続先識別コード, コンボ取得テーブル, コンボ取得SQL, リンク先遷移コマンド名, リンク先遷移パラメータ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', 'QARJTST1       ', 30, '福祉事務所コード', '福祉事務所コード', '福祉事務所コード', '福祉事務所コード', '00000', '1', '0', '0', '0000000008', 5, '0000000001', 0, 'ZD00000002', 'QAZ       ', ' ', 'SELECT SUBSTRING(コード区分,6,5) AS コード区分,コード略称 FROM QAZユーザーコードマスタ WHERE 業務コード = ''QAZ010'' AND ユーザーコード = ''ZD00000019'' ORDER BY 表示順序', ' ', ' ', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');
INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブパラメータ管理
(自治体コード, 福祉事務所コード, 業務コード, ジョブID, パラメータID, パラメータ見出し, パラメータ名, パラメータ説明, パラメータキー, 初期値, 非表示フラグ, 必須項目, 実行確認キー, パラメータ区分, 桁数, 属性, 日付フォーマット番号, 福祉共通コード, 接続先識別コード, コンボ取得テーブル, コンボ取得SQL, リンク先遷移コマンド名, リンク先遷移パラメータ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', 'QARJTST1       ', 40, '履歴番号', '履歴番号', '履歴番号', '履歴番号', ' ', '1', '0', '0', '0000000001', 10, '0000000001', 0, '0000000000', '          ', ' ', ' ', ' ', ' ', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');
INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブパラメータ管理
(自治体コード, 福祉事務所コード, 業務コード, ジョブID, パラメータID, パラメータ見出し, パラメータ名, パラメータ説明, パラメータキー, 初期値, 非表示フラグ, 必須項目, 実行確認キー, パラメータ区分, 桁数, 属性, 日付フォーマット番号, 福祉共通コード, 接続先識別コード, コンボ取得テーブル, コンボ取得SQL, リンク先遷移コマンド名, リンク先遷移パラメータ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', 'QARJTST1       ', 50, '発行年月日', '発行年月日', '発行年月日', '発行年月日', 'TODAY', '1', '0', '0', '0000000004', 12, '0000000001', 0, '0000000000', '          ', ' ', ' ', ' ', ' ', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');
INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブパラメータ管理
(自治体コード, 福祉事務所コード, 業務コード, ジョブID, パラメータID, パラメータ見出し, パラメータ名, パラメータ説明, パラメータキー, 初期値, 非表示フラグ, 必須項目, 実行確認キー, パラメータ区分, 桁数, 属性, 日付フォーマット番号, 福祉共通コード, 接続先識別コード, コンボ取得テーブル, コンボ取得SQL, リンク先遷移コマンド名, リンク先遷移パラメータ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', 'QARJTST1       ', 60, '文書記号', '文書記号', '全角15文字以内で入力', 'QAZ文書記号', ' ', '1', '0', '1', '0000000003', 15, '0000000003', 0, '0000000000', '          ', ' ', ' ', ' ', ' ', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');
INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブパラメータ管理
(自治体コード, 福祉事務所コード, 業務コード, ジョブID, パラメータID, パラメータ見出し, パラメータ名, パラメータ説明, パラメータキー, 初期値, 非表示フラグ, 必須項目, 実行確認キー, パラメータ区分, 桁数, 属性, 日付フォーマット番号, 福祉共通コード, 接続先識別コード, コンボ取得テーブル, コンボ取得SQL, リンク先遷移コマンド名, リンク先遷移パラメータ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', 'QARJTST1       ', 70, '文書番号', '文書番号', '半角10文字以内で入力', 'QAZ文書番号', ' ', '1', '0', '1', '0000000002', 10, '0000000002', 0, '0000000000', '          ', ' ', ' ', ' ', ' ', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');
INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブパラメータ管理
(自治体コード, 福祉事務所コード, 業務コード, ジョブID, パラメータID, パラメータ見出し, パラメータ名, パラメータ説明, パラメータキー, 初期値, 非表示フラグ, 必須項目, 実行確認キー, パラメータ区分, 桁数, 属性, 日付フォーマット番号, 福祉共通コード, 接続先識別コード, コンボ取得テーブル, コンボ取得SQL, リンク先遷移コマンド名, リンク先遷移パラメータ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', 'QARJTST1       ', 80, '枝番号', '枝番号', '半角10文字以内で入力', 'QAZ文書枝番号', ' ', '1', '0', '1', '0000000002', 10, '0000000002', 0, '0000000000', '          ', ' ', ' ', ' ', ' ', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');
INSERT INTO WR$$JICHITAI_CODE$$QZ.dbo.QZジョブパラメータ管理
(自治体コード, 福祉事務所コード, 業務コード, ジョブID, パラメータID, パラメータ見出し, パラメータ名, パラメータ説明, パラメータキー, 初期値, 非表示フラグ, 必須項目, 実行確認キー, パラメータ区分, 桁数, 属性, 日付フォーマット番号, 福祉共通コード, 接続先識別コード, コンボ取得テーブル, コンボ取得SQL, リンク先遷移コマンド名, リンク先遷移パラメータ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム)
VALUES('$$JICHITAI_CODE$$', '00000', 'QAR010', 'QARJTST1       ', 90, '対象者情報', '対象者情報', '対象者情報', '対象者情報', ' ', '1', '0', '0', '0000000099', 10000000, '0000000003', 0, '0000000000', '          ', ' ', ' ', ' ', ' ', 'TEST', 'TEST', '2015-04-10 00:00:00.000', '2015-04-10 00:00:00.000', '00000000');
