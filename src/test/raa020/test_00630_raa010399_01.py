import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TEST00630RAA01039901(FukushiSiteTestCaseBase):
    """TEST00630RAA01039901"""
    def setUp(self):
        self.exec_sqlfile("RAA010399-01.sql")
        super().setUp()

    #ここに操作したコードをコピーして貼り付ける
    def test_00630_raa010399_01(self):
        """test_00630_raa010399_01"""
        
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        syokannri_kubu = case_data.get("syokannri_kubu", "")
        taishou_ym_start = case_data.get("taishou_ym_start", "")
        taishou_ym_end = case_data.get("taishou_ym_end", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        
        # ログイン
        self.do_login()
        #1	メインメニュー画面	表示
        self.screen_shot("raa010399-01-01")
        #2	メインメニュー画面	「バッチ起動」ボタン押下
        self.batch_kidou_click()
        #3	バッチ起動画面	表示
        self.screen_shot("raa010399-01-03")
        #4	バッチ起動画面	業務「障害」選択
        self.form_input_by_id(idstr="GyomuSelect", text = "障害")
        #5	バッチ起動画面	事業「療育手帳」選択
        self.form_input_by_id(idstr="JigyoSelect", text = "療育手帳")
        #6	バッチ起動画面	処理区分「統計処理」選択
        self.form_input_by_id(idstr="ShoriKubunSelect", text = "統計処理")
        #7	バッチ起動画面	処理分類「統計処理」選択
        self.form_input_by_id(idstr="ShoriBunruiSelect", text = "統計処理")

        # 7	バッチ起動画面	「療育手帳統計履歴更新処理」のNoボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name) 
        params = [
            {"title": "所管区", "type": "select", "value": syokannri_kubu},
            {"title": "対象年月開始", "type": "text", "value": taishou_ym_start},
            {"title": "対象年月終了", "type": "text", "value": taishou_ym_end}
        ]
        self.set_job_params(params)
        #9	バッチ起動画面	表示
        self.screen_shot("raa010399-01-09")
        #10	バッチ起動画面	「処理開始」ボタン押下
        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        #11	バッチ起動画面	「実行履歴」ボタン押下
        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        #12	ジョブ実行履歴画面	表示
        self.screen_shot("raa010399-01-12")
        #13	ジョブ実行履歴画面	「検索」ボタン押下
        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        #14	ジョブ実行履歴画面	表示
        self.screen_shot("raa010399-01-14")
        
        # 「戻る」ボタン押下
        self.return_click()

        # メインメニュー画面表示(エビデンス取得)
        self.screen_shot("raa010399-01-16")

