import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG030_01090205(FukushiSiteTestCaseBase):
    """TestQAG030_01090205"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 変更決定通知書を出力できることを確認する。
    def test_QAG030_01090205(self):
        """変更決定通知書作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        report_name_input = case_data.get("report_name_input", "")
        hakko_ymd = case_data.get("hakko_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG030")
        # self.do_login()
        self.click_by_id(idstr="CmdButton1_1")
        self.click_button_by_label("印刷")

        # 2 自立支援医療(精神通院)資格管理画面: 「決定内容入力」ボタン押下

        # 3 帳票印刷画面: 「変更決定通知書」行の印刷チェックボックス選択「変更決定通知書」行の発行年月日チェックボックス選択発行年月日「○○」入力
        # self.print_online_reports(case_name="帳票印刷画面", report_name=case_data.get("report_name", ""), hakkou_ymd=case_data.get("hakkou_ymd", ""))
        self.click_by_id(idstr="insatsuChk_5")
        self.form_input_by_id(idstr="TxtHakkoYMD", value=case_data.get("hakkou_ymd", ""))
        self.click_button_by_label("一括反映")
        self.screen_shot("帳票印刷画面_3")

        # 4 帳票印刷画面: 「印刷」ボタン押下
        self.click_button_by_label("印刷")
        self.alert_ok()

        # 5 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下
        # Assert: メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        self.screen_shot("帳票印刷_5")

        # 6 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_6")

        # 7 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 8 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 9 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_9")
