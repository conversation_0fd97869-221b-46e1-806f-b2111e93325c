import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG030_01090313(FukushiSiteTestCaseBase):
    """TestQAG030_01090313"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 再認定申請した住民に対し決定登録ができることを確認する。
    def test_QAG030_01090313(self):
        """認定結果の登録_決定_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        kettei_ymd = case_data.get("kettei_ymd", "")
        kettei_kekka = case_data.get("kettei_kekka", "")

        self.do_login()
        
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG030")
        self.click_by_id(idstr="CmdButton1_1")
        # 1 自立支援医療(育成医療)資格管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_2")

        # 3 自立支援医療(育成医療)資格管理: 却下理由削除
        self.find_element_by_id("TxtAreaKyakkaRiyu").clear()

        # 4 自立支援医療(育成医療)資格管理: 決定日「20240701」入力決定結果「決定」選択
        self.form_input_by_id(idstr="TxtKetteiYMD", value=kettei_ymd)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=kettei_kekka)

        # 5 自立支援医療(育成医療)資格管理: 有効期間開始日「20240701」入力有効期間終了日「20250630」入力経過的特例有効期間開始日「（空白）」経過的特例有効期間終了日「（空白）」受給者証適用開始日「20240701」入力
        self.form_input_by_id(idstr="TxtKaishiYMD", value="20240701")
        self.form_input_by_id(idstr="TxtShuryoYMD", value="20250630")
        self.form_input_by_id(idstr="TxtKeikaTokureiKaishiYMD", value="")
        self.form_input_by_id(idstr="TxtKeikaTokureiShuryoYMD", value="")
        self.form_input_by_id(idstr="TxtJukyushashoTekiyoYMD", value="20240701")
        self.form_input_by_id(idstr="ChkOshiraseUmu_1", value="1")
        self.form_input_by_id(idstr="ChkOshiraseUmu_2", value="1")
        # 6 自立支援医療(育成医療)資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 7 自立支援医療(育成医療)資格管理: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(育成医療)資格管理_7")
