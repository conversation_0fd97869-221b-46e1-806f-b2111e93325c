import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG030_01090102(FukushiSiteTestCaseBase):
    """TestQAG030_01090102"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code")
        hogosha_code = case_data.get("hogosha_code")
        params = {"DELETE_ATENA_CODE": atena_code,"DELETE_HOGOSHA_CODE": hogosha_code}
        self.exec_sqlfile("RAG030.sql", params=params)
        super().setUp()

    # 18歳未満の対象者に対して、申請事由「新規」、申請理由「新規申請」の申請を登録できることを確認する。
    def test_QAG030_01090102(self):
        """新規申請情報登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        shinsei_ymd = case_data.get("shinsei_ymd", "")
        shintatsu_ymd = case_data.get("shintatsu_ymd", "")
        shintatsu_kekka = case_data.get("shintatsu_kekka", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG030")
        # 1 自立支援医療(育成医療)資格管理: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 2 自立支援医療(育成医療)資格管理: 申請事由「新規」選択申請理由「新規申請」選択
        time.sleep(2)
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=case_data.get("shinsei_riyu", ""))
        self.screen_shot("自立支援医療(育成医療)資格管理_2")

        # 3 自立支援医療(育成医療)資格管理: 「確定」ボタン押下
        self.click_button_by_label("確定")

        time.sleep(2)
        # 4 自立支援医療(育成医療)資格管理: 申請日「20230701」入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinsei_ymd)

        # 5 自立支援医療(育成医療)資格管理: 管理場所「○○」選択
        time.sleep(1)
        self.form_input_by_id(idstr="TantoShokatsukuCmb", text=case_data.get("kanri_basho", ""))

        # 6 自立支援医療(育成医療)資格管理: 担当支所「○○」選択
        # TODO Item with ID: NG
        time.sleep(1)
        self.form_input_by_id(idstr="TantoShiShoCmb", text=case_data.get("tanto_shisho", ""))

        # 7 自立支援医療(育成医療)資格管理: 職権「チェック」入力
        self.form_input_by_id(idstr="ShokkenChkBox", value="1")

        # 8 自立支援医療(育成医療)資格管理: 受付場所「○○」選択
        time.sleep(1)
        self.form_input_by_id(idstr="UketsukeBashoCmb", text=case_data.get("uketsuke_basho", ""))

        # 9 自立支援医療(育成医療)資格管理: 担当場所「○○」選択
        time.sleep(1)        
        self.form_input_by_id(idstr="TantoBashoCmb", text=case_data.get("tanto_basho", ""))

        # 10 自立支援医療(育成医療)資格管理: 所得判定年度「令和5年」を選択
        self.form_input_by_id(idstr="ShotokuHanteiNendoCmb", text="令和05年")

        # 11 自立支援医療(育成医療)資格管理: 交付方法「○○」選択
        self.form_input_by_id(idstr="KofuHohoCmb", text=case_data.get("kofu_hoho", ""))

        # 12 自立支援医療(育成医療)資格管理: 添付書類有無1「チェック」入力
        # TODO Item with ID: NG
        self.click_by_id(idstr="TempShoruiChkBox")

        # 13 自立支援医療(育成医療)資格管理: 受付番号「12345」入力
        self.form_input_by_id(idstr="TxtUketsukeBango", value="1234567890")

        # 14 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_14")

        # 15 自立支援医療(育成医療)資格管理: 「届出保険情報」ボタン押下
        self.click_button_by_label("届出保険情報")

        # 16 届出保険情報画面: 表示
        self.screen_shot("届出保険情報画面_16")

        # 17 届出保険情報画面: 「保険情報取得」ボタン押下
        self.click_button_by_label("保険情報取得")

        # 18 届出保険情報画面: 表示
        self.screen_shot("届出保険情報画面_18")

        # 19 届出保険情報画面: 保険者番号を削除
        self.find_element_by_id("TxtHokenshaBango").clear()

        # 20 届出保険情報画面: 加入状況「保険加入」選択
        self.form_input_by_id(idstr="CmbKanyuJokyo1", text="保険加入")

        # 21 届出保険情報画面: 保険の種類「健保組合」選択
        # TODO Item with ID: NG

        # 22 届出保険情報画面: 「保険者検索」ボタン押下
        self.click_button_by_label("保険者検索")

        # 23 保険者検索画面: 保険者漢字「○○」入力
        self.form_input_by_id(idstr="txtKanjiMeisho", value=case_data.get("hokensha_kanji", ""))

        # 24 保険者検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 25 保険者検索画面: 検索結果である保険者情報より「1」ボタン押下
        self.click_button_by_label("1")

        # 26 保険者検索画面: 扶養者区分「扶養者」選択
        self.form_input_by_id(idstr="CmbFuyousha", text="扶養者")

        # 27 保険者検索画面: 「被保険者検索」ボタン押下
        self.click_button_by_label("被保険者検索")

        # 28 保険者検索画面: 世帯員一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 29 保険者検索画面: 記号「１２３」入力
        self.form_input_by_id(idstr="TxtKigo", value="１２３")

        # 30 保険者検索画面: 番号「４５６７」入力
        self.form_input_by_id(idstr="TxtBango", value="４５６７")

        # 31 保険者検索画面: 個人番号「１２」入力
        self.form_input_by_id(idstr="TxtKojinShikiBango", value="１２")

        # 32 保険者検索画面: 資格取得日「20230701」入力
        self.form_input_by_id(idstr="TxtShikakuShutokuYMD", value=case_data.get("shikaku_shutoku_YMD", ""))

        # 33 保険者検索画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        self.screen_shot("保険者検索画面_33")

        # 34 保険者検索画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.alert_ok()

        # 35 自立支援医療(育成医療)資格管理: 「保険世帯作成」ボタン押下
        self.click_button_by_label("支給認定基準世帯作成")
        
        # 36 保険世帯情報画面: 表示
        self.screen_shot("保険世帯情報画面_36")

        # 37 保険世帯情報画面: 「保険情報」ボタン押下
        self.click_button_by_label("保険情報")

        # 38 保険情報一覧画面: 表示
        self.screen_shot("保険情報一覧画面_38")

        # 39 保険情報一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 40 保険世帯情報画面: 保険の種類「国民健康保険」選択
        # TODO Item with ID: NG
        self.form_input_by_id(idstr="CmbHokenKbn",value="0000000001")

        # 41 保険世帯情報画面: 「保険情報取得」ボタン押下
        self.click_button_by_label("保険情報取得")

        # 42 保険世帯情報画面: 表示
        self.screen_shot("保険世帯情報画面_42")

        # 43 保険世帯情報画面: 保険の種類「健康保険等」選択
        # TODO Item with ID: NG
        self.form_input_by_id(idstr="CmbHokenKbn",value="0000000002")
        self.click_button_by_label("保険情報取得")

        # 44 保険世帯情報画面: 1人目（本人）該当日「20230701」入力本人から見た続柄「本人」選択受給者との関係「本人」選択被用者区分「被扶養者」選択公的年金等の種類「障害年金」選択所得確定区分「確定」選択
        # TODO Item with ID: NG
        self.form_input_by_id(idstr="CmbHZoku_1",value='本人')
        self.form_input_by_id(idstr="CmbJKankei_1",value='本人')
        self.form_input_by_id(idstr="CmbKazeiKbn_1",value='課税')
        self.form_input_by_id(idstr="CmbNenkinSyurui_1",value='障害年金')
        self.form_input_by_id(idstr="CmbHiyosya_1",value='被扶養者')
        self.form_input_by_id(idstr="CmbShotokuKbn_1",value='確定')

        # 45 保険世帯情報画面: 2人目（父）該当日「20230701」入力本人から見た続柄「父」選択受給者との関係「保護者」選択被用者区分「被保険者」選択所得確定区分「確定」選択
        # TODO Item with ID: NG
        self.form_input_by_id(idstr="CmbHZoku_2",value='父')
        self.form_input_by_id(idstr="CmbJKankei_2",value='代表保護者')
        #self.form_input_by_id(idstr="CmbJKankei_2",value='保護者')
        self.form_input_by_id(idstr="CmbKazeiKbn_2",value='課税')
        self.form_input_by_id(idstr="CmbHiyosya_2",value='被保険者')
        self.form_input_by_id(idstr="CmbShotokuKbn_2",value='確定')
        # self.click_by_id(idstr="ChkDel_2")  

        # 46 保険世帯情報画面: 3人目（母）該当日「20230701」入力本人から見た続柄「母」選択受給者との関係「代表保護者」選択被用者区分「（空白）」所得確定区分「確定」選択
        # TODO Item with ID: NG
        self.form_input_by_id(idstr="CmbHZoku_3",value='母')
        self.form_input_by_id(idstr="CmbJKankei_3",value='保護者')
        self.form_input_by_id(idstr="CmbKazeiKbn_3",value='課税')
        self.form_input_by_id(idstr="CmbHiyosya_3",value='被保険者')
        self.form_input_by_id(idstr="CmbShotokuKbn_3",value='確定')
        # self.click_by_id(idstr="ChkDel_3")  
        
        
        # 47 保険世帯情報画面: 「所得情報」ボタン押下
        self.click_button_by_label("所得情報")

        # 48 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_48")

        # 49 住民税世帯情報画面: 対象者世帯一覧「1」ボタン押下
        self.click_button_by_label("1")

        # 50 住民税個人情報画面: 表示
        self.screen_shot("住民税個人情報画面_50")

        # 51 住民税個人情報画面: 「入力」ボタン押下
        self.click_button_by_label("入力")

        # 52 住民税個人情報画面: 特定扶養人数(入力)「0」入力
        # TODO Item with ID: NG
        self.form_input_by_id(idstr="Ctrl_7_3_6",value='0')

        # 53 住民税個人情報画面: 「控除廃止前想定税額 」ボタン押下
        self.click_button_by_label("控除廃止前想定税額 ")

        # 54 住民税個人情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 55 住民税個人情報画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する
        self.assert_message_area("登録しました")
        self.screen_shot("住民税個人情報画面_55")

        # 56 住民税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 57 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_57")

        # 58 住民税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 59 保険世帯情報画面: 「税情報取得」ボタン押下
        self.click_button_by_label("税情報取得")
      
        # 60 保険世帯情報画面: 「収入計算」ボタン押下
        self.click_button_by_label("収入計算")
        time.sleep(1)
        # 61 保険世帯情報画面: 表示
        self.screen_shot("保険世帯情報画面_61")

        # 62 保険世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.alert_ok()

        # 63 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_63")

        # 64 自立支援医療(育成医療)資格管理: 「世帯所得反映」ボタン押下
        # self.click_button_by_label("世帯所得反映")
        self.click_by_id(idstr="span_CmdSetaiShotokuHanei")

        # 65 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_65")

        # 66 自立支援医療(育成医療)資格管理: 特例世帯適用「チェック」入力高額療養費多数回該当「チェック」入力重度かつ継続「チェック」入力
        self.form_input_by_id(idstr="ChkTokuSetaiTekiyo", value="1")
        self.form_input_by_id(idstr="ChkKogakuRyoyoHi", value="1")
        self.form_input_by_id(idstr="ChkJyudo", value="1")

        # 67 自立支援医療(育成医療)資格管理: 「所得区分」ボタン押下
        self.click_button_by_label("所得区分")

        # 68 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_68")

        # 69 自立支援医療(育成医療)資格管理: 減免区分「０円」入力
        self.form_input_by_id(idstr="GenmenKubunCmb", text="０円")

        # 70 自立支援医療(育成医療)資格管理: 生保移行防止減免対象区分コード「チェック」入力
        self.form_input_by_id(idstr="ChkSeihoIkoBoshi", value="1")

        # 71 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_71")

        # 72 自立支援医療(育成医療)資格管理: 受診機関管理＞「追加」ボタン押下
        self.click_button_by_label("追加")

        # 73 自立支援医療(育成医療)資格管理: 「医療機関」ボタン押下
        self.click_by_id(idstr="span_CmdIryoKensaku_1")

        # 74 医療機関検索画面: 所在地区分「指定なし」選択点数表「医科」選択医療機関名漢字「○○」入力
        #self.form_input_by_id(idstr="Shozaichi_1", value="1")
        self.form_input_by_id(idstr="Shozaichi_"+str(case_data.get("iryoukikan_shozaichi_kubun_1", "")), value="1")
        # 点数表「医科」選択: # TODO Item with ID: NG
        #self.form_input_by_id(idstr="CmbTensuhyo",value="0000000001")
        self.form_input_by_id(idstr="CmbTensuhyo", text=case_data.get("iryokikan_shubetsu_1", ""))
        #self.form_input_by_id(idstr="TxtKanjiMeisho", value="テスト")
        self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("iryokikan_kanjimeisho_1", ""))
        self.click_by_id(idstr="RdoKanjiMeishoKanzen")

        # 75 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 76 医療機関検索画面: 検索結果である医療機関一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 77 自立支援医療(育成医療)資格管理: 認定決定お知らせ有無「チェック」入力
        self.form_input_by_id(idstr="ChkOshiraseUmu_1", value="1")

        # 78 自立支援医療(育成医療)資格管理: 有効開始日「20230701」入力有効終了日「20240630」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_1", value=case_data.get("iryoukikan_yukou_kaishi", ""))
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_1", value=case_data.get("iryoukikan_yukou_shuryou", ""))

        # 79 自立支援医療(育成医療)資格管理: 入通院区分「入院外」選択
        self.form_input_by_id(idstr="NyugaiKubunCmb_1", text="入院外")

        # 80 自立支援医療(育成医療)資格管理: 「医療機関」ボタン押下
        self.click_by_id(idstr="CmdJyushinKikanTsuika")
        self.click_by_id(idstr="span_CmdIryoKensaku_2")

        # 81 医療機関検索画面: 所在地区分「指定なし」選択点数表「調剤」選択医療機関名漢字「○○」入力
        #self.form_input_by_id(idstr="Shozaichi_1", value="1")
        #self.form_input_by_id(idstr="CmbTensuhyo",value="0000000004")
        # 点数表「医科」選択: # TODO Item with ID: NG
        #self.form_input_by_id(idstr="TxtKanjiMeisho", value="薬局")
        self.form_input_by_id(idstr="Shozaichi_"+str(case_data.get("iryoukikan_shozaichi_kubun_2", "")), value="1")
        self.form_input_by_id(idstr="CmbTensuhyo", text=case_data.get("iryokikan_shubetsu_2", ""))
        self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("iryokikan_kanjimeisho_2", ""))
        


        # 82 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 83 医療機関検索画面: 検索結果である医療機関一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 84 自立支援医療(育成医療)資格管理: 認定決定お知らせ有無「チェック」入力
        self.form_input_by_id(idstr="ChkOshiraseUmu_2", value="1")

        # 85 自立支援医療(育成医療)資格管理: 有効開始日「20230701」入力有効終了日「20240630」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_2", value=case_data.get("iryoukikan_yukou_kaishi", ""))
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_2", value=case_data.get("iryoukikan_yukou_shuryou", ""))

        # 86 自立支援医療(育成医療)資格管理: 入通院区分「入院外」選択
        self.form_input_by_id(idstr="NyugaiKubunCmb_2", text="入院外")
        self.click_by_id(idstr="CmdJyushinKikanTsuika")
        self.click_by_id(idstr="span_CmdIryoKensaku_3")

        # 87 医療機関検索画面: 所在地区分「指定なし」選択点数表「訪問看護」選択医療機関名漢字「○○」入力
        # self.form_input_by_id(idstr="Shozaichi_1", value="1")
        # # 点数表「医科」選択: # TODO Item with ID: NG
        # self.form_input_by_id(idstr="CmbTensuhyo",value="0000000006")
        # self.form_input_by_id(idstr="TxtKanjiMeisho", value="訪問")
        self.form_input_by_id(idstr="Shozaichi_"+str(case_data.get("iryoukikan_shozaichi_kubun_3", "")), value="1")
        self.form_input_by_id(idstr="CmbTensuhyo", text=case_data.get("iryokikan_shubetsu_3", ""))
        self.form_input_by_id(idstr="TxtKanjiMeisho", value=case_data.get("iryokikan_kanjimeisho_3", ""))


        # 88 医療機関検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 89 医療機関検索画面: 検索結果である医療機関一覧より「1」ボタン押下
        self.click_button_by_label("1")

        # 90 自立支援医療(育成医療)資格管理: 認定決定お知らせ有無「チェック」入力
        self.form_input_by_id(idstr="ChkOshiraseUmu_3", value="1")

        # 91 自立支援医療(育成医療)資格管理: 有効開始日「20230701」入力有効終了日「20240630」入力
        self.form_input_by_id(idstr="TxtJushinYukokikanKaishi_3", value=case_data.get("iryoukikan_yukou_kaishi", ""))
        self.form_input_by_id(idstr="TxtJushinYukokikanShuryo_3", value=case_data.get("iryoukikan_yukou_shuryou", ""))


        # 92 自立支援医療(育成医療)資格管理: 入通院区分「入院外」選択
        self.form_input_by_id(idstr="NyugaiKubunCmb_3", text="入院外")

        # 93 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_93")

        # 94 自立支援医療(育成医療)資格管理: 公費負担の対象となる障害「じん臓機能障害（透析）」選択
        self.form_input_by_id(idstr="ShogaiCmb", text="じん臓機能障害（透析）")

        # 95 自立支援医療(育成医療)資格管理: 医療の方針「○○」選択
        self.form_input_by_id(idstr="HoshinCmb", text=case_data.get("iryou_houshin", ""))


        # 96 自立支援医療(育成医療)資格管理: 医療の具体的方針「医療の具体的方針入力テスト４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★」入力
        self.form_input_by_id(idstr="TxtAreaHoshinShosai", value="医療の具体的方針入力テスト４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★")

        # 97 自立支援医療(育成医療)資格管理: 特定疾病療養受給者証の有無「チェック」入力手術予定日理学療法の有無「チェック」入力入院日数「123」入力通院日数「123」入力医療費概算額「1234567」入力補装具の有無「チェック」入力補装具名「補装具名入力テスト★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★」
        self.form_input_by_id(idstr="ChkShippei", value="1")
        self.form_input_by_id(idstr="TxtShujutsuYMD", value=case_data.get("shujutsu_YMD", ""))
        self.form_input_by_id(idstr="ChkRigakuRyohoUmu", value="1")
        self.form_input_by_id(idstr="TxtNyuinNissu", value="123")
        self.form_input_by_id(idstr="TxtTsuinNissu", value="123")
        self.form_input_by_id(idstr="TxtIryohiGaisangaku", value="1234567")
        self.form_input_by_id(idstr="ChkHosoguUmu", value="1")
        self.form_input_by_id(idstr="TxtHosoguMei", value="補装具名入力テスト★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★１２３４５６７８９★")

        # 98 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_98")

        # 99 自立支援医療(育成医療)資格管理: 「住記情報」ボタン押下
        self.click_button_by_label("住記情報")

        # 100 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_100")

        # 101 世帯一覧画面: 世帯一覧「1」Noボタン押下
        self.click_button_by_label("1")

        # 102 住記情報画面: 表示
        self.screen_shot("住記情報画面_102")

        # 103 住記情報画面: 「戻る」ボタン押下
        self.return_click()

        # 104 世帯一覧画面: 表示
        self.screen_shot("世帯一覧画面_104")

        # 105 世帯一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 106 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_106")

        # 107 自立支援医療(育成医療)資格管理: 「所得情報」ボタン押下
        self.click_button_by_label("所得情報")

        # 108 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_108")

        # 109 住民税世帯情報画面: 対象者世帯一覧「1」ボタン押下
        self.click_button_by_label("1")

        # 110 住民税個人情報画面: 表示
        self.screen_shot("住民税個人情報画面_110")

        # 111 住民税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 112 住民税世帯情報画面: 表示
        self.screen_shot("住民税世帯情報画面_112")

        # 113 住民税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 114 自立支援医療(育成医療)資格管理: 「生活保護情報」ボタン押下
        self.click_button_by_label("生活保護情報")

        # 115 生活保護情報画面: 表示
        self.screen_shot("生活保護情報画面_115")

        # 116 世帯一覧画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 117 生活保護情報画面: ケース番号「12345」入力 マスタ区分「入力」選択   生保開始日「20230701」入力生保喪失日「（空白）」
        self.form_input_by_id(idstr="TbxCase", value="12345")
        self.form_input_by_id(idstr="CmbMstKbn", text="入力")
        self.form_input_by_id(idstr="TbxDayBegin", value=case_data.get("seikatsuhogo_begin_YMD", ""))
        self.form_input_by_id(idstr="TbxDayEnd", value="")

        # 118 生活保護情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 119 生活保護情報画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("生活保護情報画面_119")

        # 120 生活保護情報画面: 「戻る」ボタン押下
        self.return_click()

        # 121 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_121")

        # 122 自立支援医療(育成医療)資格管理: 「手帳情報」ボタン押下
        self.click_button_by_label("手帳情報")

        # 123 手帳情報画面: 表示
        self.screen_shot("手帳情報画面: 表示_123")

        # 124 手帳情報画面: 「戻る」ボタン押下
        self.return_click()

        # 125 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_125")

        # 126 自立支援医療(育成医療)資格管理: 「住所管理」ボタン押下
        self.click_button_by_label("住所管理")

        # 127 住所管理画面: 表示
        self.screen_shot("住所管理画面_127")

        # 128 住所管理画面: 「送付先」ボタン押下
        self.click_button_by_label("送付先住所")

        # 129 住所管理画面: 「追加」ボタン押下
        self.click_button_by_label("追加")
        
        # self.click_by_id(idstr="RadioJigyo")
        # 130 住所管理画面: 「住記情報索引」ボタン押下
        self.click_button_by_label("住記情報索引")
        self.alert_ok()
        self.form_input_by_id(idstr="TxtKanaShimei_Uji", value="ケンショウ")
        self.form_input_by_id(idstr="TxtKanaShimei_Na", value="メグミ")
        self.form_input_by_id(idstr="TxtShimei_Uji", value="検証")
        self.form_input_by_id(idstr="TxtShimei_Na", value="めぐみ")
        self.screen_shot("住所管理画面_130")

        # 131 住所管理画面: 「登録」ボタン押下
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_base_header("登録しました。")

        # 132 住所管理画面: 表示
        self.screen_shot("住所管理画面_132")

        # 133 住所管理画面: 「戻る」ボタン押下
        self.return_click()

        # 134 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_134")

        # 135 自立支援医療(育成医療)資格管理: 「連絡先管理」ボタン押下
        self.click_button_by_label("連絡先管理")

        # 136 連絡先管理画面: 表示
        self.screen_shot("連絡先管理画面_136")

        # 137 連絡先管理画面: 本人連絡先の「追加」ボタン押下
        self.click_by_id(idstr="BtnTsuika_Honnin")

        # 138 連絡先管理画面: 業務区分　？？電話番号公開範囲　？？優先順位「携帯電話番号」選択公開/非公開「公開」チェック自宅電話番号 「111-1111-1111」入力留守電「チェック」入力携帯電話番号 「222-2222-2222」入力FAX番号「333-3333-3333」入力勤務先名「勤務先入力テスト」入力勤務先電話番号「444-4444-4444」入力勤務先内線番号「123」入力勤務先FAX番号「555-5555-5555」入力   メールアドレス1「<EMAIL>」メールアドレス2「<EMAIL>」備考「備考テスト入力１２３４５６７８９０」種別「その他」選択連絡先「種別連絡先入力テスト１２３４５６７８９０」備考「種別備入力テスト１２３４５６７８９０」
        # TODO Item with ID: NG
        # self.entry_honnin_renraku(yusen_text="携帯電話番号", jitaku="111-1111-1111", rusuden=True, keitai="222-2222-2222", 
        #                         fax="333-3333-3333", kinmu_text="勤務先入力テスト", kinmu_tel="444-4444-4444", kinmu_fax="555-5555-5555", 
        #                         koukai=True, etc_list=["その他", "種別連絡先入力テスト１２３４５６７８９０", "種別備入力テスト１２３４５６７８９０"])
        self.form_input_by_id(idstr="CmbYusenTEL", text="携帯電話番号")
        self.form_input_by_id(idstr="kokai0", value="1")
        self.form_input_by_id(idstr="TxtTelJitaku", value="111-1111-1111")
        self.form_input_by_id(idstr="ChkRusuden", value="1")
        self.form_input_by_id(idstr="TxtTelKeitai", value="222-2222-2222")
        self.form_input_by_id(idstr="TxtFaxJitaku", value="333-3333-3333")
        self.form_input_by_id(idstr="TxtMail", value="<EMAIL>")
        self.form_input_by_id(idstr="TxtMail2", value="<EMAIL>")
        self.form_input_by_id(idstr="TxtKinmuSaki", value="勤務先入力テスト")
        self.form_input_by_id(idstr="TxtTelKinmu", value="444-4444-4444")
        self.form_input_by_id(idstr="TxtFaxKinmu", value="555-5555-5555")
        self.form_input_by_id(idstr="SelectHanyo1", text="その他")
        time.sleep(1)
        self.form_input_by_id(idstr="TxtHanyoRenraku1", value="種別連絡先入力テスト１２３４５６７８９０")
        self.form_input_by_id(idstr="TxtHanyoBiko1", value="種別備入力テスト１２３４５６７８９０")

        
        self.screen_shot("連絡先管理画面_138")

        # 139 連絡先管理画面: 「登録」ボタン押下
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        # 140 連絡先管理画面: 表示
        self.screen_shot("連絡先管理画面_140")

        # 141 連絡先管理画面: 緊急連絡先入力の「追加」ボタン押下
        self.click_by_id(idstr="BtnTsuika_Kinkyu")

        # 142 個人検索画面: 「個人検索」ボタン押下
        self.click_button_by_label("個人検索")

        # 143 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=case_data.get("kinkyu_renraku_atena_code", ""))

        # 144 個人検索画面: 「検索」ボタン押下
        #self.kojin_kensaku_by_atena_code(atena_code=atena_code)
        self.click_by_id(idstr="Kensaku")

        # 145 連絡先管理画面: 続柄「その他」選択自宅電話番号 「111-1111-1111」入力留守電「チェック」入力携帯電話番号 「222-2222-2222」入力FAX番号「333-3333-3333」入力勤務先「勤務先入力テスト１２３４５６７８９０」入力勤務先電話番号「444-4444-4444」入力
        self.form_input_by_id(idstr="TxtKanaNM",value="ジリツ ハナコ")
        self.form_input_by_id(idstr="CmbTsudukigara", text="その他")
        self.form_input_by_id(idstr="TxtTelJitaku_Kinkyu", value="111-1111-1111")
        self.form_input_by_id(idstr="ChkRusuden_Kinkyu", value="1")
        self.form_input_by_id(idstr="TxtTelKeitai_Kinkyu", value="222-2222-2222")
        self.form_input_by_id(idstr="TxtFaxJitaku_Kinkyu", value="333-3333-3333")
        self.form_input_by_id(idstr="TxtKinmuSaki_Kinkyu", value="勤務先入力テスト１２３４５６７８９０")
        self.form_input_by_id(idstr="TxtTelKinmu_Kinkyu", value="444-4444-4444")

        # 146 連絡先管理画面: 「登録」ボタン押下
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.click_by_id("BtnTouroku_Kinkyu")
        self.click_by_id_and_ok("BtnTouroku_Kinkyu")
        # self.click_by_id(idstr="BtnTouroku_Kinkyu")
        # self.alert_ok()
        # time.sleep(5)
        self.assert_message_area("登録しました")

        # 147 連絡先管理画面: 表示
        self.screen_shot("連絡先管理画面_147")

        # 148 連絡先管理画面: 「戻る」ボタン押下
        self.return_click()

        # 149 自立支援医療(育成医療)資格管理: 「メモ情報」ボタン押下
        self.click_button_by_label("メモ情報")

        # 150 メモ情報画面: 表示
        self.screen_shot("メモ情報画面_150")

        # 151 メモ情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 152 メモ情報画面: 内容「メモ入力テスト１２３４５６７８９０」
        self.form_input_by_id(idstr="TxtNaiyo", value="メモ入力テスト１２３４５６７８９０")

        # 153 メモ情報画面: 「登録」ボタン押下
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        # 154 メモ情報画面: 表示
        self.screen_shot("メモ情報画面_154")

        # 155 メモ情報画面: 「戻る」ボタン押下
        self.return_click()

        # 156 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_156")

        # 157 自立支援医療(育成医療)資格管理: 「保険情報」ボタン押下
        self.click_button_by_label("保険情報")

        # 158 保険情報画面: 表示
        self.screen_shot("保険情報画面_158")

        # 159 保険情報画面: 「戻る」ボタン押下
        self.return_click()

        # 160 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_160")

        # 161 自立支援医療(育成医療)資格管理: 保護者情報の「住所管理」ボタン押下
        # self.click_button_by_label("住所管理")
        self.click_by_id(idstr="福祉世帯表示部_CmdJusho")

        # 162 住所管理画面: 表示
        self.screen_shot("住所管理画面_161")

        # 163 住所管理画面: 「送付先」ボタン押下
        self.click_button_by_label("送付先住所")

        # 164 住所管理画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # self.click_by_id(idstr="RadioJigyo")
        # 165 住所管理画面: 「住記情報索引」ボタン押下
        self.click_button_by_label("住記情報索引")
        self.alert_ok()
        self.form_input_by_id(idstr="TxtKanaShimei_Uji", value="ケンショウ")
        self.form_input_by_id(idstr="TxtKanaShimei_Na", value="モジ")
        self.form_input_by_id(idstr="TxtShimei_Uji", value="検証")
        self.form_input_by_id(idstr="TxtShimei_Na", value="文字")
        self.screen_shot("住所管理画面_165")

        # 166 住所管理画面: 「登録」ボタン押下
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_base_header("登録しました。")

        # 167 住所管理画面: 表示
        self.screen_shot("住所管理画面_167")

        # 168 住所管理画面: 「戻る」ボタン押下
        self.return_click()

        # 169 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_169")

        # 170 自立支援医療(育成医療)資格管理: 保護者情報の「連絡先管理」ボタン押下
        #self.click_button_by_label("連絡先管理")
        self.click_by_id(idstr="福祉世帯表示部_CmdRenrakusaki")

        # 171 連絡先管理画面: 表示
        self.screen_shot("連絡先管理画面_171")

        # 172 連絡先管理画面: 本人連絡先の「追加」ボタン押下
        self.click_by_id(idstr="BtnTsuika_Honnin")

        # 173 連絡先管理画面: 業務区分　？？電話番号公開範囲　？？優先順位「携帯電話番号」選択公開/非公開「公開」チェック自宅電話番号 「111-1111-1111」入力留守電「チェック」入力携帯電話番号 「222-2222-2222」入力FAX番号「333-3333-3333」入力勤務先名「勤務先入力テスト」入力勤務先電話番号「444-4444-4444」入力勤務先内線番号「123」入力勤務先FAX番号「555-5555-5555」入力   メールアドレス1「<EMAIL>」メールアドレス2「<EMAIL>」備考「備考テスト入力１２３４５６７８９０」種別「その他」選択連絡先「種別連絡先入力テスト１２３４５６７８９０」備考「種別備入力テスト１２３４５６７８９０」
        # TODO Item with ID: NG
        #self.entry_honnin_renraku(yusen_text="携帯電話番号", jitaku="111-1111-1111", rusuden=True, keitai="222-2222-2222", fax="333-3333-3333", kinmu_text="勤務先入力テスト", kinmu_tel="444-4444-4444", kinmu_fax="555-5555-5555", koukai=True, etc_list=["その他", "種別連絡先入力テスト１２３４５６７８９０", "種別備入力テスト１２３４５６７８９０"])
        self.form_input_by_id(idstr="CmbYusenTEL", text="携帯電話番号")
        self.form_input_by_id(idstr="kokai0", value="1")
        self.form_input_by_id(idstr="TxtTelJitaku", value="111-1111-1111")
        self.form_input_by_id(idstr="ChkRusuden", value="1")
        self.form_input_by_id(idstr="TxtTelKeitai", value="222-2222-2222")
        self.form_input_by_id(idstr="TxtFaxJitaku", value="333-3333-3333")
        self.form_input_by_id(idstr="TxtMail", value="<EMAIL>")
        self.form_input_by_id(idstr="TxtMail2", value="<EMAIL>")
        self.form_input_by_id(idstr="TxtKinmuSaki", value="勤務先入力テスト")
        self.form_input_by_id(idstr="TxtTelKinmu", value="444-4444-4444")
        self.form_input_by_id(idstr="TxtFaxKinmu", value="555-5555-5555")
        self.form_input_by_id(idstr="SelectHanyo1", text="その他")
        time.sleep(1)
        self.form_input_by_id(idstr="TxtHanyoRenraku1", value="種別連絡先入力テスト１２３４５６７８９０")
        self.form_input_by_id(idstr="TxtHanyoBiko1", value="種別備入力テスト１２３４５６７８９０")

        self.screen_shot("連絡先管理画面_173")
        

        # 174 連絡先管理画面: 「登録」ボタン押下
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")

        # 175 連絡先管理画面: 表示
        self.screen_shot("連絡先管理画面_175")

        # 176 連絡先管理画面: 緊急連絡先入力の「追加」ボタン押下
        self.click_by_id(idstr="BtnTsuika_Kinkyu")

        # 177 個人検索画面: 「個人検索」ボタン押下
        self.click_button_by_label("個人検索")

        # 178 個人検索画面: 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=case_data.get("hogosha_kinkyu_renraku_atena_code", ""))

        # 179 個人検索画面: 「検索」ボタン押下
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 180 連絡先管理画面: 続柄「その他」選択自宅電話番号 「111-1111-1111」入力留守電「チェック」入力携帯電話番号 「222-2222-2222」入力FAX番号「333-3333-3333」入力勤務先「勤務先入力テスト１２３４５６７８９０」入力勤務先電話番号「444-4444-4444」入力
        self.form_input_by_id(idstr="CmbTsudukigara", text="その他")
        self.form_input_by_id(idstr="TxtTelJitaku_Kinkyu", value="111-1111-1111")
        self.form_input_by_id(idstr="ChkRusuden_Kinkyu", value="1")
        self.form_input_by_id(idstr="TxtTelKeitai_Kinkyu", value="222-2222-2222")
        self.form_input_by_id(idstr="TxtFaxJitaku_Kinkyu", value="333-3333-3333")
        self.form_input_by_id(idstr="TxtKinmuSaki_Kinkyu", value="勤務先入力テスト１２３４５６７８９０")
        self.form_input_by_id(idstr="TxtTelKinmu_Kinkyu", value="444-4444-4444")

        # 181 連絡先管理画面: 「登録」ボタン押下
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        # self.click_by_id(idstr="BtnTouroku_Kinkyu")
        # self.alert_ok()
                
        self.click_by_id("BtnTouroku_Kinkyu")
        self.click_by_id_and_ok("BtnTouroku_Kinkyu")
        self.assert_message_area("登録しました")

        # 182 連絡先管理画面: 表示
        self.screen_shot("連絡先管理画面_182")

        # 183 連絡先管理画面: 「戻る」ボタン押下
        self.return_click()

        # 184 自立支援医療(育成医療)資格管理: 表示
        self.screen_shot("自立支援医療(育成医療)資格管理_184")

        # 185 自立支援医療(育成医療)資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        time.sleep(1)

        # 進達入力
        self.click_button_by_label("進達入力")
        time.sleep(1)
        # 進達年月日「20230701」入力
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=shintatsu_ymd)
        # 4 自立支援医療(育成医療)資格管理: 申請日「20230701」入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinsei_ymd)
        # 35 自立支援医療(育成医療)資格管理: 「保険世帯作成」ボタン押下
        self.click_button_by_label("支給認定基準世帯作成")
        # 51 住民税個人情報画面: 「入力」ボタン押下
        self.click_button_by_label("入力完了")
        self.alert_ok()
        self.click_button_by_label("登録")
        self.alert_ok()

        # 進達結果入力
        self.click_button_by_label("進達結果入力")
        time.sleep(1)
        # 進達年月日「20230701」入力
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=shintatsu_ymd)
        self.form_input_by_id(idstr="ShintatsuHantei1Cmb", text=shintatsu_kekka)
        # 4 自立支援医療(育成医療)資格管理: 申請日「20230701」入力
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinsei_ymd)
        # 35 自立支援医療(育成医療)資格管理: 「保険世帯作成」ボタン押下
        self.click_button_by_label("支給認定基準世帯作成")
        # 51 住民税個人情報画面: 「入力」ボタン押下
        self.click_button_by_label("入力完了")
        self.alert_ok()
        self.click_button_by_label("登録")
        self.alert_ok()
        

        # 186 自立支援医療(育成医療)資格管理: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(育成医療)資格管理_186")
        time.sleep(2)
        # 187 自立支援医療(育成医療)資格管理: 「資格履歴」ボタン押下
        self.click_button_by_label("資格履歴")

        # 188 資格履歴画面: 表示
        self.screen_shot("資格履歴画面_188")

        # 189 資格履歴画面: 「戻る」ボタン押下
        self.return_click()
