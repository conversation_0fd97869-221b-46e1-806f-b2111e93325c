import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG030_01090319(FukushiSiteTestCaseBase):
    """TestQAG030_01090319"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 受給者証、自己負担上限額管理票を一括で出力できることを確認する。
    def test_QAG030_01090319(self):
        """受給者証、自己負担上限額管理票一括作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害事業：自立支援医療(育成医療)処理区分：バッチ処理処理分類：月次処理
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="自立支援医療(育成)")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="バッチ処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="月次処理")
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 「受給者証一括出力」のNoボタン押下
        self.click_batch_job_button_by_label("受給者証一括出力")
        self.screen_shot("バッチ起動画面_5")

        # 6 バッチ起動画面: 決定年月日＜はじめ＞「20230701」を入力決定年月日＜おわり＞「20230701」を入力申請種別「1:新規」を入力発行年月日「20230701」を入力裏面出力有無「1」を入力出力順序「1」を選択
        params = [
            {"title": "決定年月日＜はじめ＞", "type": "text", "value": "20240701"},
            {"title": "決定年月日＜おわり＞", "type": "text", "value": "20240701"},
            {"title": "申請種別", "type": "text", "value": "2"},
            {"title": "発行年月日", "type": "text", "value": "20240701"},
            {"title": "出力順序", "type": "selected","value": "1"},
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_6")

        # 10 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 12 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_12")

        # 13 ジョブ実行履歴画面: 「検索」ボタン押下
        # time.sleep(60)
        self.wait_job_finished(120,20)
        self.click_button_by_label("検索(S)")
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 14 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_14")

        # 15 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 16 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_16")

        # 17 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 18 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_18")

        # 19 ジョブ帳票履歴画面: 「自立支援医療受給者証（表面）」のNoボタン押下

        # 35 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_35")
