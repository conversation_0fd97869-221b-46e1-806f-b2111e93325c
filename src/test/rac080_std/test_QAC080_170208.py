import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC080_170208(FukushiSiteTestCaseBase):
    """TestQAC080_170208"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # ・資格管理画面から、額改定請求届の出力が行えることを確認する。・施設受給者の額改定請求届の出力が行えることを確認する。
    def test_QAC080_170208(self):
        """額改定認定請求書の出力"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")

        # 3 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 4 個人検索画面: 表示
        self.screen_shot("個人検索画面_4")

        # 5 個人検索画面: 住民コード　入力

        # 6 個人検索画面: 「検索」ボタン押下
        self.screen_shot("個人検索画面_6")
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 7 受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_7")

        # 8 受給状況参照画面: 「児童手当」ボタン押下
        self.click_button_by_label("児童手当")

        # 9 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_9")

        # 10 児童手当資格管理: 「印刷」ボタン押下
        self.click_button_by_label("印刷")

        # 11 帳票印刷画面: 表示
        self.screen_shot("帳票印刷画面_11")

        # 12 帳票印刷画面: 額改定請求書を選択

        # 13 帳票印刷画面: 「印刷」ボタン押下
        self.print_online_reports(case_name="帳票印刷画面", report_name="額改定請求書届出力処理", hakkou_ymd="")

        # 14 帳票印刷画面: 表示
        # Assert: 「プレビューしました。」のメッセージのチェック
        self.screen_shot("帳票印刷画面_14")

        # 15 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下

        # 16 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_16")

        # 17 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 18 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

        # 19 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_19")

        # 20 児童手当資格管理: 「戻る」ボタン押下
        self.return_click()

        # 21 受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_21")

        # 22 受給状況参照画面: 「戻る」ボタン押下
        self.return_click()

        # 23 個人検索画面: 表示
        self.screen_shot("個人検索画面_23")

        # 24 個人検索画面: 「戻る」ボタン押下
        self.return_click()

        # 25 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_25")

        # 26 メインメニュー画面: 「手当施設検索」ボタン押下
        self.click_button_by_label("手当施設検索")

        # 27 手当施設入所者検索画面: 表示
        self.screen_shot("手当施設入所者検索画面_27")

        # 28 手当施設入所者検索画面: 事業：児童手当　を選択
        self.form_input_by_id(idstr="CmbJigyo", text="児童手当")

        # 29 手当施設入所者検索画面: 「確定」ボタンを押下
        self.click_button_by_label("確定")

        # 30 手当施設入所者検索画面: 表示
        self.screen_shot("手当施設入所者検索画面_30")

        # 31 手当施設入所者検索画面: 「施設検索」ボタンを押下
        self.click_button_by_label("施設検索")

        # 32 施設検索: 表示
        self.screen_shot("施設検索_32")

        # 33 施設検索: 施設名カナ：ｻﾄｵﾔ　を入力
        self.form_input_by_id(idstr="kanaMeisho", value=case_data.get("kanaMeisho", ""))

        # 34 施設検索: 表示
        self.screen_shot("施設検索_34")

        # 35 施設検索: Noボタン押下
        # Test No = 1 TODO
        self.click_button_by_label("1")

        # 36 手当施設入所者検索画面: 表示
        self.screen_shot("手当施設入所者検索画面_36")

        # 37 手当施設入所者検索画面: 「医療機関検索」ボタン押下
        self.click_button_by_label("医療機関検索")

        # 38 医療機関検索: 表示
        self.screen_shot("医療機関検索_38")

        # 39 医療機関検索: 医療機関名カナ：　を入力
        self.form_input_by_id(idstr="TxtKanaMeisho", value=case_data.get("TxtKanaMeisho", ""))

        # 40 医療機関検索: 表示
        self.screen_shot("医療機関検索_40")

        # 41 医療機関検索: Noボタン押下
        # Test No = 1 TODO
        self.click_button_by_label("1")

        # 42 手当施設入所者検索画面: 表示
        self.screen_shot("手当施設入所者検索画面_42")

        # 43 手当施設入所者検索画面: 申請種別：認定請求　を入力
        self.form_input_by_id(idstr="CmbShinseiShubetu", text=case_data.get("CmbShinseiShubetu", ""))

        # 44 手当施設入所者検索画面: 「検索」ボタンを押下
        self.click_button_by_label("検索")

        # 45 手当施設入所者一覧画面: 表示
        self.screen_shot("手当施設入所者一覧画面_45")

        # 46 手当施設入所者一覧画面: 帳票名：施設用_額改定請求書届　を選択
        self.form_input_by_id(idstr="Cmb帳票名", text="施設用_額改定請求書届出力処理")

        # 47 手当施設入所者一覧画面: 額改定対象者がいれば、処理欄にチェックを入れる
        # Test: ChkUpdate = 1 TODO: このテストは、ChkUpdate = 1 に対してのみ実行される
        self.form_input_by_id(idstr="ChkUpdate_1", value="1")

        # 48 手当施設入所者一覧画面: 表示
        self.screen_shot("手当施設入所者一覧画面_48")

        # 49 手当施設入所者一覧画面: 「印刷」ボタンを押下
        self.print_online_reports(case_name="帳票印刷", report_name="施設用_消滅通知書", hakkou_ymd="")

        # 50 手当施設入所者一覧画面: 表示
        # Assert: 「プレビューしました。」のメッセージのチェック
        self.screen_shot("手当施設入所者一覧画面_50")

        # 51 手当施設入所者一覧画面: 「ファイルを開く(O)」ボタンを押下

        # 52 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_52")

        # 53 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 54 手当施設入所者一覧画面: 「戻る」ボタン押下
        self.return_click()
