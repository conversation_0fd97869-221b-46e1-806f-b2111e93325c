import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC080_170509(FukushiSiteTestCaseBase):
    """TestQAC080_170509"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # ・画面から、時効による消滅通知書の出力が行えることを確認する。
    def test_QAC080_170509(self):
        """支給事由消滅通知書の作成"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")

        # 3 メインメニュー画面: 「進捗管理」ボタン押下
        self.click_button_by_label("進捗管理")

        # 4 進捗管理対象者検索: 表示
        self.screen_shot("進捗管理対象者検索_4")

        # 5 進捗管理対象者検索: 業務：児童事業：児童手当　を選択
        self.form_input_by_id(idstr="Gyomu", text="児童")
        self.form_input_by_id(idstr="Jigyo", text="児童手当")

        # 6 進捗管理対象者検索: 「確定」ボタンを押下
        self.click_button_by_label("確定")
        self.screen_shot("進捗管理対象者検索_6")

        # 7 進捗管理対象者検索: 表示
        self.screen_shot("進捗管理対象者検索_7")

        # 8 進捗管理対象者検索: 進捗状況(上段）：空欄進捗状況(下段）：空欄申請年月日：空欄申請種別：消滅申請理由：空欄決定年月日：空欄決定結果：決定決定理由：空欄を入力
        self.form_input_by_id(idstr="CmbPreShinchoku", value=case_data.get("cmbPreShinchoku", ""))
        self.form_input_by_id(idstr="ShinChoku", value=case_data.get("shinChoku", ""))
        self.form_input_by_id(idstr="ShinseiKaishiYMD", value=case_data.get("shinsei_kaishiYMD", ""))
        self.form_input_by_id(idstr="ShinseiShubetsu", text=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id(idstr="ShiseiRiyu", text=case_data.get("shinsei_riyuu", ""))
        self.form_input_by_id(idstr="KeteiKaishiYMD", value=case_data.get("kettei_kaishiYMD", ""))
        self.form_input_by_id(idstr="KeteiKekka", text=case_data.get("kettei_kekka", ""))
        self.form_input_by_id(idstr="KeteiRiyu", text=case_data.get("kettei_riyuu", ""))

        # 9 進捗管理対象者検索: 「検索」ボタンを押下
        self.click_button_by_label("検索")
        self.screen_shot("進捗管理対象者検索_9")

        # 10 進捗管理対象者一覧: 表示
        self.screen_shot("進捗管理対象者一覧_10")

        # 11 進捗管理対象者一覧: Noボタン押下
        # Test No = 1 TODO
        self.click_button_by_label("1")

        # 12 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_12")

        # JyukyusyaKbn = self.find_element_by_id("JyukyusyaKbnCmb_wrFk_Disabled").text.strip()
        # if JyukyusyaKbn not in ["施設","里親","指定医療機関"]:
        # 13 児童手当資格管理: 「印刷」ボタン押下
        # self.click_button_by_label("印刷")

        # 14 帳票印刷画面: 表示
        # self.screen_shot("帳票印刷画面_14")

        # 15 帳票印刷画面: 支給事由消滅通知書を選択
        # self.form_input_by_id(idstr="insatsuChk_0", value="1")

        # 16 帳票印刷画面: 「印刷」ボタン押下
        # self.print_online_reports(case_name="帳票印刷画面", report_name="支給事由消滅通知書", hakkou_ymd="")

        # 17 帳票印刷画面: 表示
        # Assert: 「プレビューしました。」のメッセージのチェック
        # self.assert_message_area("プレビューしました。")
        # self.screen_shot("帳票印刷画面_17")

        # 18 帳票印刷画面: 「ファイルを開く(O)」ボタンを押下

        # 19 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_19")

        # 20 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 21 帳票印刷画面: 「戻る」ボタン押下
        # self.return_click()

        # 22 児童手当資格管理: 表示
        # self.screen_shot("児童手当資格管理_22")

        # 23 対象画面名: 操作内容
        # Assert: アサート内容
        # self.screen_shot("対象画面名_23")

        # else:
        self.do_login()
        # 24 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_24")

        # 25 メインメニュー画面: 「手当施設検索」ボタン押下
        self.click_button_by_label("手当施設検索")

        # 26 手当施設入所者検索画面: 表示
        self.screen_shot("手当施設入所者検索画面_26")

        # 27 手当施設入所者検索画面: 事業：児童手当　を選択
        self.form_input_by_id(idstr="Gyomu", text="児童")
        self.form_input_by_id(idstr="Jigyo", text="児童手当")

        # 28 手当施設入所者検索画面: 「確定」ボタンを押下
        self.screen_shot("進捗管理対象者検索_28")
        self.click_button_by_label("確定")

        # 29 手当施設入所者検索画面: 表示
        self.screen_shot("手当施設入所者検索画面_29")

        # JyukyusyaKbn = self.check_juukyuusha_kubun(jyukyusya_kbn = JyukyusyaKbn)

        # if JyukyusyaKbn == 1:

        # 30 手当施設入所者検索画面: 「施設検索」ボタンを押下
        self.click_button_by_label("施設検索")

        # 31 施設検索: 表示
        self.screen_shot("施設検索_31")

        # 32 施設検索: 施設名カナ：ｻﾄｵﾔ　を入力
        self.form_input_by_id(idstr="kanaMeisho", value=case_data.get("kanaMeisho", ""))

        # 33 施設検索: 表示
        self.screen_shot("施設検索_33")

        # 34 施設検索: Noボタン押下
        # Test No = 1 TODO
        self.click_button_by_label("1")

        # 35 手当施設入所者検索画面: 表示
        self.screen_shot("手当施設入所者検索画面_35")

        # elif JyukyusyaKbn == 2:

        # 36 手当施設入所者検索画面: 「指定医療機関」ボタン押下
        # self.click_button_by_label("医療機関検索")

        # 37 医療機関検索: 表示
        # self.screen_shot("医療機関検索_37")

        # 38 医療機関検索: 医療機関名カナ：　を入力
        # self.form_input_by_id(idstr="TxtKanaMeisho", value=case_data.get("iryou_kikanmei_kana", ""))

        # 39 医療機関検索: 表示
        # self.screen_shot("医療機関検索_39")

        # 40 医療機関検索: Noボタン押下
        # Test No = 1 TODO
        # self.click_button_by_label("1")

        # 41 手当施設入所者検索画面: 表示
        # self.screen_shot("手当施設入所者検索画面_41")

        # 42 手当施設入所者検索画面: 申請種別：消滅決定結果：決定　を入力
        self.form_input_by_id(idstr="CmbShinseiShubetu", text=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id(idstr="CmbKetteiKekka", text=case_data.get("kettei_kekka", ""))

        # 43 手当施設入所者検索画面: 「検索」ボタンを押下
        self.click_button_by_label("検索")

        # 44 手当施設入所者一覧画面: 表示
        # Assert: 1件以上検索されること
        self.screen_shot("手当施設入所者一覧画面_44")

        # 45 手当施設入所者一覧画面: 帳票名：施設用_支給事由消滅通知書　を選択
        self.form_input_by_id(idstr="Cmb帳票名", text="施設用_支給事由消滅通知書")

        # 46 手当施設入所者一覧画面: 消滅対象者になる該当者の処理欄をチェック
        # Test: ChkUpdate = 1 TODO: このテストは、ChkUpdate = 1 に対してのみ実行される
        self.form_input_by_id(idstr="ChkUpdate_1", value="1")

        # 47 手当施設入所者検索画面: 表示
        self.screen_shot("手当施設入所者検索画面_47")

        # 48 手当施設入所者一覧画面: 「印刷」ボタンを押下
        self.click_button_by_label("印刷")

        # 49 手当施設入所者一覧画面: 表示
        # Assert: 「プレビューしました。」のメッセージのチェック
        self.assert_message_area("プレビューしました。")
        self.screen_shot("手当施設入所者一覧画面_49")

        # 50 手当施設入所者一覧画面: 「ファイルを開く(O)」ボタンを押下

        # 51 帳票（PDF）: 表示
        # self.screen_shot("帳票（PDF）_51")

        # 52 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 53 手当施設入所者一覧画面: 「戻る」ボタン押下
        self.return_click()

        # 54 手当施設入所者検索画面: 表示
        self.screen_shot("手当施設入所者検索画面_54")

        # 55 手当施設入所者検索画面: 「戻る」ボタン押下
        self.return_click()
