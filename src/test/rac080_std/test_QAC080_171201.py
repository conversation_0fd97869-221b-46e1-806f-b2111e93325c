import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC080_171201(FukushiSiteTestCaseBase):
    """TestQAC080_171201"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # ・留学期間チェック一覧出力処理が出力できることを確認する。
    def test_QAC080_171201(self):
        """必要に応じて留学期間チェック"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 住民コード　入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 5 個人検索画面: 「検索」ボタン押下
        self.screen_shot("個人検索画面_5")

        # 6 該当者一覧画面: 表示

        # 7 該当者一覧画面: Noボタン押下
        self.click_button_by_label("1")

        # 8 受給状況参照画面: 表示
        self.screen_shot("受給状況参照画面_8")

        # 9 受給状況参照画面: 「児童手当」ボタン押下
        self.click_button_by_label("児童手当")

        # 10 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_10")

        # 11 児童手当資格管理: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 12 児童手当資格管理: 申請種別： 認定請求　　申請理由 ：転入　を入力
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", value=case_data.get("shinsei_shubetsu", ""))
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", value=case_data.get("shinsei_riyuu", ""))

        # 13 児童手当資格管理: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 14 児童手当資格管理: 申請年月日： 20230601　支給開始年月： 202307　を入力　受給者区分：指定なし被用区分： 被用または非被用者
        self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("shinsei_ymd", ""))
        self.form_input_by_id(idstr="TxtKaitei", value=case_data.get("kaitei", ""))
        self.form_input_by_id(idstr="JyukyusyaKbnCmb", text=case_data.get("jyukyusya_kbn", ""))
        self.form_input_by_id(idstr="RdoHiyo", value="1")

        # 15 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_15")

        # 16 児童手当資格管理: 「児童追加」ボタン押下
        self.click_button_by_label("児童追加")

        # 17 世帯員検索画面: 表示
        self.screen_shot("世帯員検索画面_17")

        # 18 世帯員検索画面: Noボタン押下
        self.click_button_by_label("1")

        # 19 支給対象児童入力: 表示
        self.screen_shot("支給対象児童入力_19")

        # 20 支給対象児童入力: 続柄：子要件開始年月：資格管理画面「支給開始年月」要件開始事由：資格管理画面「申請理由」または「その他」支給開始年月：資格管理画面「支給開始年月」支給開始事由：資格管理画面「申請理由」または「その他」児童との関係：父母留学開始年月日：20200901留学終了年月日：20230831　を入力
        # NG 「留学開始年月日」フィルドと「留学終了年月日」フィルドはなさそうです。

        # 21 支給対象児童入力: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 22 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_22")

        # 23 児童手当資格管理: 「福祉世帯情報」ボタン押下
        self.click_button_by_label("福祉世帯情報")

        # 24 福祉世帯情報画面: 表示
        self.screen_shot("福祉世帯情報画面_24")

        # 25 福祉世帯情報画面: 受給者との関係：対象児童を選択
        self.form_input_by_id(idstr="JukyuCmb_1", text=case_data.get("jukyu", ""))

        # 26 福祉世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 27 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_27")

        # 28 児童手当資格管理: 「口座情報」ボタン押下
        self.click_button_by_label("口座情報")

        # 29 口座情報画面: 表示
        self.screen_shot("口座情報画面_29")

        # 30 口座情報画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 31 口座情報画面: 「金融機関」ボタン押下
        self.click_button_by_label("金融機関")

        # 32 金融機関検索画面: 表示
        self.screen_shot("金融機関検索画面_32")

        # 33 金融機関検索画面: 金融機関名：'みずほ'支店名：'本店'　を入力
        # NG 「金融機関」ボタンがないので、この画面に進めないです

        # 34 金融機関検索画面: 「検索」ボタン押下
        # self.click_button_by_label("検索") #NG 「金融機関」ボタンがないので、この画面に進めないです

        # 35 金融機関検索画面: 表示
        self.screen_shot("金融機関検索画面_35")

        # 36 金融機関検索画面: Noボタン押下
        # self.click_button_by_label("1") #NG 「金融機関」ボタンがないので、この画面に進めないです

        # 37 口座情報画面: 表示
        self.screen_shot("口座情報画面_37")

        # 38 口座情報画面: 預金種別：普通口座番号：'1234567'　を入力
        # NG 「預金種別」フィルドはなさそうです。

        # 39 口座情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")

        # 40 口座情報画面: 表示
        # Assert: 「登録しました。」のメッセージのチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("口座情報画面_40")

        # 41 口座情報画面: 「戻る」ボタン押下
        self.return_click()

        # 42 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_42")

        # 43 児童手当資格管理: 「所得情報」ボタン押下
        self.click_button_by_label("所得情報")

        # 44 税世帯情報画面: 表示
        self.screen_shot("税世帯情報画面_44")

        # 45 税世帯情報画面: 令和5年度を選択して、「検索」ボタンを押下
        self.form_input_by_id(idstr="CmbNendo", text=case_data.get("nendo", ""))
        self.click_button_by_label("検索")

        # 46 税世帯情報画面: 表示
        self.screen_shot("税世帯情報画面_46")

        # 47 税世帯情報画面: Noボタン押下
        self.click_button_by_label("1")

        # 48 税個人情報画面: 表示
        self.screen_shot("税個人情報画面_48")

        # 49 税個人情報画面: 「入力」ボタン押下
        self.click_button_by_label("入力")

        # 50 税個人情報画面: 所得額、控除額、扶養人数、本人該当を入力
        # 所得
        self.form_input_by_id(idstr="Ctrl_2_0_1", value=case_data.get("Ctrl_2_0_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_3_1", value=case_data.get("Ctrl_2_3_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_6_1", value=case_data.get("Ctrl_2_6_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_9_1", value=case_data.get("Ctrl_2_9_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_12_1", value=case_data.get("Ctrl_2_12_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_15_1", value=case_data.get("Ctrl_2_15_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_16_1", value=case_data.get("Ctrl_2_16_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_1_1", value=case_data.get("Ctrl_2_1_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_4_1", value=case_data.get("Ctrl_2_4_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_7_1", value=case_data.get("Ctrl_2_7_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_10_1", value=case_data.get("Ctrl_2_10_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_13_1", value=case_data.get("Ctrl_2_13_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_2_1", value=case_data.get("Ctrl_2_2_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_5_1", value=case_data.get("Ctrl_2_5_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_8_1", value=case_data.get("Ctrl_2_8_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_11_1", value=case_data.get("Ctrl_2_11_1", ""))
        self.form_input_by_id(idstr="Ctrl_2_14_1", value=case_data.get("Ctrl_2_14_1", ""))

        # 住民税法所得控除
        self.form_input_by_id(idstr="Ctrl_3_0_1", value=case_data.get("Ctrl_3_0_1", ""))
        self.form_input_by_id(idstr="Ctrl_3_3_1", value=case_data.get("Ctrl_3_3_1", ""))
        self.form_input_by_id(idstr="Ctrl_3_6_1", value=case_data.get("Ctrl_3_6_1", ""))
        self.form_input_by_id(idstr="Ctrl_3_1_1", value=case_data.get("Ctrl_3_1_1", ""))
        self.form_input_by_id(idstr="Ctrl_3_4_1", value=case_data.get("Ctrl_3_4_1", ""))
        self.form_input_by_id(idstr="Ctrl_3_7_1", value=case_data.get("Ctrl_3_7_1", ""))
        self.form_input_by_id(idstr="Ctrl_3_2_1", value=case_data.get("Ctrl_3_2_1", ""))
        self.form_input_by_id(idstr="Ctrl_3_5_1", value=case_data.get("Ctrl_3_5_1", ""))
        self.form_input_by_id(idstr="Ctrl_3_8_1", value=case_data.get("Ctrl_3_8_1", ""))

        # 住民税法税額控除
        self.form_input_by_id(idstr="Ctrl_4_0_1", value=case_data.get("Ctrl_4_0_1", ""))
        self.form_input_by_id(idstr="Ctrl_4_3_1", value=case_data.get("Ctrl_4_3_1", ""))
        self.form_input_by_id(idstr="Ctrl_4_1_1", value=case_data.get("Ctrl_4_1_1", ""))
        self.form_input_by_id(idstr="Ctrl_4_4_1", value=case_data.get("Ctrl_4_4_1", ""))
        self.form_input_by_id(idstr="Ctrl_4_2_1", value=case_data.get("Ctrl_4_2_1", ""))

        # 所得税法所得控除
        self.form_input_by_id(idstr="Ctrl_5_0_1", value=case_data.get("Ctrl_5_0_1", ""))
        self.form_input_by_id(idstr="Ctrl_5_1_1", value=case_data.get("Ctrl_5_1_1", ""))
        self.form_input_by_id(idstr="Ctrl_5_2_1", value=case_data.get("Ctrl_5_2_1", ""))

        # 所得税法税額控除
        self.form_input_by_id(idstr="Ctrl_6_0_1", value=case_data.get("Ctrl_6_0_1", ""))
        self.form_input_by_id(idstr="Ctrl_6_3_1", value=case_data.get("Ctrl_6_3_1", ""))
        self.form_input_by_id(idstr="Ctrl_6_1_1", value=case_data.get("Ctrl_6_1_1", ""))
        self.form_input_by_id(idstr="Ctrl_6_4_1", value=case_data.get("Ctrl_6_4_1", ""))
        self.form_input_by_id(idstr="Ctrl_6_2_1", value=case_data.get("Ctrl_6_2_1", ""))
        self.form_input_by_id(idstr="Ctrl_6_5_1", value=case_data.get("Ctrl_6_5_1", ""))

        # 扶養人数
        self.form_input_by_id(idstr="Ctrl_7_0_1", value=case_data.get("Ctrl_7_0_1", ""))
        self.form_input_by_id(idstr="Ctrl_7_3_1", value=case_data.get("Ctrl_7_3_1", ""))
        self.form_input_by_id(idstr="Ctrl_7_6_1", value=case_data.get("Ctrl_7_6_1", ""))
        self.form_input_by_id(idstr="Ctrl_7_11_1", value=case_data.get("Ctrl_7_11_1", ""))
        self.form_input_by_id(idstr="Ctrl_7_1_1", value=case_data.get("Ctrl_7_1_1", ""))
        self.form_input_by_id(idstr="Ctrl_7_4_1", value=case_data.get("Ctrl_7_4_1", ""))
        self.form_input_by_id(idstr="Ctrl_7_7_4", text=case_data.get("Ctrl_7_7_4", ""))
        self.form_input_by_id(idstr="Ctrl_7_12_4", text=case_data.get("Ctrl_7_12_4", ""))
        self.form_input_by_id(idstr="Ctrl_7_2_1", value=case_data.get("Ctrl_7_2_1", ""))
        self.form_input_by_id(idstr="Ctrl_7_5_1", value=case_data.get("Ctrl_7_5_1", ""))
        self.form_input_by_id(idstr="Ctrl_7_8_6", value=case_data.get("Ctrl_7_8_6", ""))
        self.form_input_by_id(idstr="Ctrl_7_13_6", value=case_data.get("Ctrl_7_13_6", ""))

        # 本人該当
        self.form_input_by_id(idstr="Ctrl_9_0_4", text=case_data.get("Ctrl_9_0_4", ""))
        self.form_input_by_id(idstr="Ctrl_9_3_4", text=case_data.get("Ctrl_9_3_4", ""))
        self.form_input_by_id(idstr="Ctrl_9_1_4", text=case_data.get("Ctrl_9_1_4", ""))
        self.form_input_by_id(idstr="Ctrl_9_4_4", text=case_data.get("Ctrl_9_4_4", ""))
        self.form_input_by_id(idstr="Ctrl_9_2_4", text=case_data.get("Ctrl_9_2_4", ""))
        self.form_input_by_id(idstr="Ctrl_9_5_4", text=case_data.get("Ctrl_9_5_4", ""))

        # 51 税個人情報画面: 「登録」ボタン押下
        self.click_button_by_label("登録")

        # 52 税個人情報画面: 表示
        # Assert: 「登録しました。」のメッセージのチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("税個人情報画面_53")

        # 53 税個人情報画面: 「戻る」ボタン押下
        self.return_click()

        # 54 税世帯情報画面: 表示
        self.screen_shot("税世帯情報画面_54")

        # 55 税世帯情報画面: 「戻る」ボタン押下
        self.return_click()

        # 56 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_56")

        # 57 児童手当資格管理: 「所得判定詳細情報」ボタン押下
        self.click_button_by_label("所得判定詳細情報")

        # 58 所得判定詳細情報画面: 表示
        self.screen_shot("所得判定詳細情報画面_58")

        # 59 所得判定詳細情報画面: 「戻る」ボタン押下
        self.return_click()

        # 60 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_60")

        # 61 児童手当資格管理: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 62 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_62")

        # 63 児童手当資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")

        # 64 児童手当資格管理: 表示
        # Assert: 「登録しました。」のメッセージのチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("児童手当資格管理_64")

        # 65 児童手当資格管理: 「決定登録」ボタン押下
        self.click_button_by_label("決定登録")

        # 66 児童手当資格管理: 決定年月日： 20230701　　決定結果 ：決定　を入力
        # NG 左記フィルドはなさそうですため、IDコントロール取得が出来ません。

        # 67 児童手当資格管理: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 68 児童手当資格管理: 表示
        self.screen_shot("児童手当資格管理_68")

        # 69 児童手当資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")

        # 70 児童手当資格管理: 表示
        # Assert: 「登録しました。」のメッセージのチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("児童手当資格管理_70")

        # 71 児童手当資格管理: 「戻る」ボタン押下
        self.return_click()

        # 72 受給状況参照画面: 表示

        # 73 受給状況参照画面: 「戻る」ボタン押下
        self.return_click()

        # 74 対象画面名: 操作内容
        # Assert: アサート内容
        self.screen_shot("対象画面名_74")

        # 75 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_75")

        # 76 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 77 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_77")

        # 78 バッチ起動画面: 業務：児童事業：児童手当処理区分：月次処理分類：留学期間チェック
        self.form_input_by_id(idstr="GyomuSelect", text="児童")
        self.form_input_by_id(idstr="JigyoSelect", text="児童手当")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="留学期間チェック")

        # 79 バッチ起動画面: 「留学期間チェック一覧出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("留学期間チェック一覧出力処理")

        # 80 バッチ起動画面: 基準年月日「20230901」出力順「カナ氏名順」選択
        # NG 左記フィルドはなさそうですため、IDコントロール取得が出来ません。
        self.screen_shot("バッチ起動画面_80")

        # 81 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 82 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 83 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_83")

        # 84 ジョブ実行履歴画面: 「検索」ボタン押下
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 85 ジョブ実行履歴画面: 表示
        # Assert: 状態：正常終了　をチェック
        self.screen_shot("ジョブ実行履歴画面_85")

        # 86 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 87 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_88")

        # 88 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 89 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_89")

        # 90 ジョブ帳票履歴画面: 「留学期間チェックリスト」のNoボタン押下
        # self.click_button_by_label("留学期間チェックリスト")

        # 91 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下

        # 92 帳票（PDF）: 表示
        self.screen_shot("帳票（PDF）_92")

        # 93 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 94 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_94")
