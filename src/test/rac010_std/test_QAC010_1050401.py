import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAC010_1050401(FukushiSiteTestCaseBase):
    """TestQAC010_1050401"""

    def setUp(self):
        case_data = self.test_data["TestQAC010_1050401"]
        sql_params = {"TARGET_GYOUMU_CODE": "QAC010",
                      "DELETE_ATENA_CODE": case_data.get("atena_code", ""),
                      "TARGET_NENDO": case_data.get("nendoY", ""),
                      "TARGET_NENDO_2": case_data.get("nendoY_2", ""),
                      "TARGET_NENDO_3": case_data.get("nendoY_3", "")}
        self.exec_sqlfile("Test_QAC010_1050401.sql", params=sql_params)
        super().setUp()

    # 資格喪失届を提出した住民に対し、その他必要な情報の登録ができることを確認する。
    def test_QAC010_1050401(self):
        """届出情報入力"""

        case_data = self.test_data["TestQAC010_1050401"]
        atena_code = case_data.get("atena_code", "")
        shinseiShubetsu = case_data.get("shinseiShubetsu", "")
        shinseiRiyuu = case_data.get("shinseiRiyuu", "")
        shinseiYMD = case_data.get("shinseiYMD", "")
        seiyakuumu = case_data.get("seiyakuumu", "")
        shougai1 = case_data.get("shougai1", "")
        ninteiYMD1 = case_data.get("ninteiYMD1", "")
        hanyo1 = case_data.get("hanyo1", "")
        hanteiYMD = case_data.get("hanteiYMD", "")
        hRiyu = case_data.get("hRiyu", "")
        hNaiyou = case_data.get("hNaiyou", "")
        gaitouYMD = case_data.get("gaitouYMD", "")
        shintasuBtn = case_data.get("shintasuBtn", "")
        shintatsuYMD = case_data.get("shintatsuYMD", "")
        shintatsuHanteiYMD = case_data.get("shintatsuHanteiYMD", "")
        shintasuHantei = case_data.get("shintasuHantei", "")
        ketteiYMD = case_data.get("ketteiYMD", "")
        ketteiKekka = case_data.get("ketteiKekka", "")
        pShinseiShubetsuCmb = case_data.get("pShinseiShubetsuCmb", "")
        pShinseiRiyuuCmb = case_data.get("pShinseiRiyuuCmb", "")
        pTxtShinseiYMD = case_data.get("pTxtShinseiYMD", "")
        pTxtKaitei = case_data.get("pTxtKaitei", "")
        pShintatsuYMD = case_data.get("pShintatsuYMD", "")
        pShintatsuHanteiYMD = case_data.get("pShintatsuHanteiYMD", "")
        pShintasuHantei = case_data.get("pShintasuHantei", "")
        
        self.do_login()
        # 1 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_1")

        # 2 メインメニュー画面: 「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 3 個人検索画面: 表示
        self.screen_shot("個人検索画面_3")

        # 4 個人検索画面: 「住民コード」入力
        self.kojin_kensaku_by_atena_code(atena_code=atena_code)

        # 5 個人検索画面: 「検索」ボタン押下

        # 6 受給状況画面: 表示
        self.screen_shot("受給状況画面_6")

        # 7 受給状況画面: 「特別障害者手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC010")

        # 8 特別障害者手当受給者台帳画面: 表示
        self.screen_shot("特別障害者手当受給者台帳画面_8")

        # ＜対象者の条件＞
        # ・特別障害者手当の資格を持っている住民
        self.click_button_by_label("申請内容入力")
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=shinseiShubetsu)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=shinseiRiyuu)
        self.click_button_by_label("確定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value=shinseiYMD)
        self.form_input_by_id(idstr="SeiyakuumuChkBox", value=seiyakuumu)
        self.form_input_by_id(idstr="Shougai1Cmb", text=shougai1)
        self.form_input_by_id(idstr="TxtNinteiYMD1", value=ninteiYMD1)
        self.form_input_by_id(idstr="SelectHanyo1", text=hanyo1)
        self.click_button_by_label("障害程度審査情報1")
        self.form_input_by_id(idstr="TxtHanteiYMD", value=hanteiYMD)
        self.form_input_by_id(idstr="CmbHRiyu", text=hRiyu)
        self.form_input_by_id(idstr="CmbHNaiyou", text=hNaiyou)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("福祉世帯情報")
        self.fukushi_setai_entry_helper(gaitou_ymd=gaitouYMD, sonota_sakujo_flg=True)
        self.click_button_by_label("入力完了")
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        can_shintatsu_button = self.click_button_by_label(shintasuBtn)
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsuYMD", value=shintatsuYMD)
            self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value=shintatsuHanteiYMD)
            self.form_input_by_id(idstr="ShintasuHanteiCmb", text=shintasuHantei)
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました。")

        self.click_button_by_label("決定内容入力")
        self.form_input_by_id(idstr="TxtKetteiYMD", value=ketteiYMD)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=ketteiKekka)
        self.click_button_by_label("月額計算")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # 9 特別障害者手当受給者台帳画面: 「申請内容入力」ボタン押下
        self.click_button_by_label("申請内容入力")

        # 10 特別障害者手当受給者台帳画面: 申請種別「資格喪失」選択申請理由「死亡」選択
        self.form_input_by_id(idstr="ShinseiShubetsuCmb", text=pShinseiShubetsuCmb)
        self.form_input_by_id(idstr="ShinseiRiyuuCmb", text=pShinseiRiyuuCmb)
        self.screen_shot("特別障害者手当受給者台帳画面_10")

        # 11 特別障害者手当受給者台帳画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 12 特別障害者手当受給者台帳画面: 申請日「20230701」資格喪失日「20230630」
        self.form_input_by_id(idstr="TxtShinseiYMD", value=pTxtShinseiYMD)
        self.form_input_by_id(idstr="TxtKaitei", value=pTxtKaitei)
        self.screen_shot("特別障害者手当受給者台帳画面_12")

        # 13 特別障害者手当受給者台帳画面: 「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 14 特別障害者手当受給者台帳画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")

        # 15 特別障害者手当受給者台帳画面: 表示
        self.screen_shot("特別障害者手当受給者台帳画面_15")

        # 後続のシナリオのため進達入力がある場合登録しておく
        can_shintatsu_button = self.click_button_by_label(shintasuBtn)
        if (can_shintatsu_button):
            self.form_input_by_id(idstr="TxtShintatsuYMD", value=pShintatsuYMD)
            self.form_input_by_id(idstr="TxtShintatsuHanteiYMD", value=pShintatsuHanteiYMD)
            self.form_input_by_id(idstr="ShintasuHanteiCmb", text=pShintasuHantei)
            self.click_button_by_label("月額計算")
            self.click_button_by_label("登録")
            self.alert_ok()
            self.assert_message_area("登録しました。")