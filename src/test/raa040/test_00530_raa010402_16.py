import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01040216(FukushiSiteTestCaseBase):
    """TESTRAA01040216"""
    
    def test_case_raa010402_16(self):
        """test_case_raa010402_16"""
        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        # カード登録日
        card_touroku_ymd = case_data.get("card_touroku_ymd", "")
        # カード解除日
        card_kaijo_ymd = case_data.get("card_kaijo_ymd", "")
        # カード発行日
        card_hakko_ymd = case_data.get("card_hakko_ymd", "")
        # 手帳受領日
        ukewatashi_ymd = case_data.get("ukewatashi_ymd", "")
        # 通知発送日
        tuchi_hasso_ymd = case_data.get("tuchi_hasso_ymd", "")
        # 手帳引渡日
        techo_hikiwatashi_ymd = case_data.get("techo_hikiwatashi_ymd", "")
        # NHK受信料入力要否フラグ
        is_input_nhk =  case_data.get("is_input_nhk", False)
        # NHK受信料減免有無
        nhk_jushinryou_genmenumu = case_data.get("nhk_jushinryou_genmenumu", "")
        # NHK受信料減免お客様番号
        nhk_okyakusama_bangou = case_data.get("nhk_okyakusama_bangou", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA040")
        self.screen_shot("raa010402_16-08" )

        self.click_button_by_label("修正")
        
        self.form_input_by_id(idstr="TxtCardTourokuYMD", value = card_touroku_ymd)
        self.form_input_by_id(idstr="TxtCardKaijoYMD", value = card_kaijo_ymd)
        self.form_input_by_id(idstr="TxtCardHakkoYMD", value = card_hakko_ymd)
        self.form_input_by_id(idstr="TxtUkewatashiYMD", value = ukewatashi_ymd)
        self.form_input_by_id(idstr="TxtTuchiHassoYMD", value = tuchi_hasso_ymd)
        self.form_input_by_id(idstr="TxtTechoHikiwatashiYMD", value = techo_hikiwatashi_ymd)
        
        if is_input_nhk:
            self.form_input_by_id(idstr="NhkJushinryouGenmenUmuChkBox", value = nhk_jushinryou_genmenumu)
            self.form_input_by_id(idstr= "TxtNhkOkyakusamaBangou", value =  nhk_okyakusama_bangou)

        self.screen_shot("raa010402_16-10" )

        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("raa010402_16-12" )

        self.return_click()
        
        self.return_click()
        
        self.return_click()
        self.screen_shot("raa010402_16-18")

