import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QMAF030(FukushiSiteTestCaseBase):
    """Test_QMAF030"""

    def test_case_001(self):
        """test_case_001"""
        driver = None

        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.click_button_by_label("帳票印字項目メンテナンス")
        self.screen_shot("QMAF030-03_初期画面")

        self.form_input_by_id(idstr="CmbGyomu", text="障害")
        self.form_input_by_id(idstr="CmbJigyo", text="身体障害者手帳")
        self.click_button_by_label("確定")
        self.screen_shot("QMAF030-07_「確定」ボタン押下")
        
        self.form_input_by_id(idstr="CmbChohyo_J", text="AB020101:身体障害者手帳交付証明書")
        self.form_input_by_id(idstr="TxtKijyunbi_J", value="令和06年09月24日")
        self.form_input_by_id(idstr="ChkDelete_J", value="1")
        self.click_button_by_label("検索")
        self.screen_shot("QMAF030-12_「検索」ボタン押下")

        self.click_button_by_label("追加")
        self.click_by_id("Sel1")
        self.screen_shot("QMAF030-15_「追加」ボタン押下")
        
        self.form_input_by_id(idstr="TxtShutsuKomokuNameSub", value="追加項目（テスト用）")
        self.click_button_by_label("初期表示")
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.screen_shot("QMAF030-18_「初期表示」ボタン押下")

        self.form_input_by_id(idstr="TxtShutsuKomokuNameSub", value="追加項目（テスト用）")
        self.form_input_by_id(idstr="TxtYukoYmdStart", value="令和06年08月01日")
        self.form_input_by_id(idstr="TxtYukoYmdEnd", value="令和10年08月30日")
        self.form_input_by_id(idstr="DataGroupNameCmb", text="身体障害者手帳情報")
        self.form_input_by_id(idstr="TxtDataKomokuSearch", value="不足書類")
        self.click_button_by_label("データ項目")
        self.screen_shot("QMAF030-24_「データ項目」ボタン押下")
        
        self.form_input_by_id(idstr="DataKomokuCmb", text="不足書類_1")
        self.form_input_by_id(idstr="HenshuPatternCmb", text="02：カンマ編集")
        self.click_button_by_label("印字内容へ反映")
        self.screen_shot("QMAF030-28_「印字内容へ反映」ボタン押下")
        
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("QMAF030-30_登録処理")
        
        self.click_button_by_label("修正")
        self.screen_shot("QMAF030-32_「修正」ボタン押下")
        
        self.form_input_by_id(idstr="DataKomokuCmb", text="不足書類_2")
        self.click_button_by_label("印字内容へ反映")
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました。")
        self.screen_shot("QMAF030-36_更新処理")
        
        self.click_button_by_label("削除")
        self.alert_ok()
        self.assert_message_area("削除しました。")
        self.screen_shot("QMAF030-38_削除処理")
        
        self.return_click()
        self.screen_shot("QMAF030-40_「戻る」ボタン押下")

        self.click_button_by_label("検索条件クリア")
        self.screen_shot("QMAF030-42_「検索条件クリア」ボタン押下")
        
        self.click_button_by_label("初期表示")
        self.screen_shot("QMAF030-44_「初期表示」ボタン押下")
