import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys


class TestQAE040(FukushiSiteTestCaseBase):
    """TESTQAE040001"""

    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAE040-001～003.sql", params=atena_list)
        super().setUp()

    def test_case_qae040_001(self):
        """test_case_qae040_001"""

        driver = None
        test_data = self.common_test_data
        #ログイン
        self.do_login()

        #資格申請管理をクリック
        self.click_by_id("CmdProcess1_1") 
        self.screen_shot("QAE001-001-1-2", caption="QAE001-001-1-2")
        #住民コードを検索
        self.send_keys_by_name("Atena<PERSON>", test_data.get("qae002_atena_code01",""))
        self.screen_shot("QAE001-001-1-3", caption="QAE001-001-1-3")
        self.click_by_id("Kensa<PERSON>") 
        self.screen_shot("QAE001-001-1-5", caption="QAE001-001-1-5")

        #未熟児医療給付ボタン押下
        self.click_by_id("02:0000000040:QAE040")
        self.screen_shot("QAE001-001-1-7", caption="QAE001-001-1-7")

        #初期表示ボタン動確
        self.click_by_id("CmdShinsei")
        self.click_by_id("CmdShoki")
        self.screen_shot("QAE001-001-1-9", caption="QAE001-001-1-9")

        #処理取消ボタン動確
        self.click_by_id("CmdShinsei")
        self.screen_shot("QAE001-001-1-10", caption="QAE001-001-1-10")
        self.click_by_id("ShinseiShubetsuCmb")
        self.select_Option(self.driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"新規")
        self.click_by_id("CmdTorikeshi")
        self.screen_shot("QAE001-001-1-12", caption="QAE001-001-1-12")

        #福祉世帯情報入力
        self.click_by_id("btnCommon5")
        
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAE001//" + "QAE001-001-1-14-2" +".png")

        self.click_by_id("HoninCmb_2")
        self.select_Option(self.driver,self.find_element(By.ID,"HoninCmb_2"),"父")
        self.click_by_id("JukyuCmb_2")
        self.select_Option(self.driver,self.find_element(By.ID,"JukyuCmb_2"),"扶養義務者")
        self.click_by_id("GaitoYMDtxt_1")
        self.send_keys_by_name("GaitoYMDtxt_1", "")
        self.send_keys_by_name("GaitoYMDtxt_1", "R03.09.01")
        self.click_by_id("GaitoYMDtxt_2")
        self.send_keys_by_name("GaitoYMDtxt_2", "")
        self.send_keys_by_name("GaitoYMDtxt_2", "R03.09.01")

        self.click_by_id("span_Kakutei_BTN")
        self.screen_shot("QAE001-001-1-17", caption="QAE001-001-1-17")

        #確定ボタン動確
        self.click_by_id("ShinseiShubetsuCmb")
        self.select_Option(self.driver,self.find_element(By.ID,"ShinseiShubetsuCmb"),"新規")
        self.click_by_id("CmdKakutei")
        self.screen_shot("QAE001-001-1-19", caption="QAE001-001-1-19")

        #申請内容入力
        self.send_keys_by_name("TxtShinseiYMD", "")
        self.send_keys_by_name("TxtShinseiYMD", "20210929")
        self.click_by_id("TantoShokatsukuCmb")
        self.select_Option(self.driver,self.find_element(By.ID,"TantoShokatsukuCmb"),"第一区")
        self.click_by_id("TxtIryoKikanCode1")
        self.send_keys_by_name("TxtIryoKikanCode1", "")
        self.send_keys_by_name("TxtIryoKikanCode1", "0000000001")
        self.click_by_id("TxtNyuinKaishi")
        self.send_keys_by_name("TxtNyuinKaishi", "")
        self.send_keys_by_name("TxtNyuinKaishi", "20210901")
        self.click_by_id("TxtTsuinKaishi")
        self.send_keys_by_name("TxtTsuinKaishi", "")
        self.send_keys_by_name("TxtTsuinKaishi", "20210901")
        self.click_by_id("TxtNyuinShuryo")
        self.send_keys_by_name("TxtNyuinShuryo", "")
        self.send_keys_by_name("TxtNyuinShuryo", "20210914")
        self.click_by_id("TxtTsuinShuryo")
        self.send_keys_by_name("TxtTsuinShuryo", "")
        self.send_keys_by_name("TxtTsuinShuryo", "20210914")

        #医療機関/階層ボタン動確
        self.click_by_id("CmdIryoKikan1")
        self.screen_shot("QAE001-001-1-21", caption="QAE001-001-1-21")
        self.click_by_id("CmdKaisou")
        self.screen_shot("QAE001-001-1-22", caption="QAE001-001-1-22")

        #各種検索ボタン動確
        self.click_by_id("span_Gyosha_KensakuBtn")
        self.screen_shot("QAE001-001-1-24", caption="QAE001-001-1-24")
        self.click_by_id("GOBACK")
        self.screen_shot("QAE001-001-1-26", caption="QAE001-001-1-26")

        self.click_by_id("span_KensakuBtn_ServiceJigyosha")
        self.screen_shot("QAE001-001-1-28", caption="QAE001-001-1-28")
        self.click_by_id("GOBACK")
        self.screen_shot("QAE001-001-1-30", caption="QAE001-001-1-30")

        self.click_by_id("span_KensakuBtn")
        self.screen_shot("QAE001-001-1-32", caption="QAE001-001-1-32")
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAE001//" + "QAE001-001-1-32-2" +".png")
        self.click_by_id("span_Sel1")
        self.screen_shot("QAE001-001-1-34", caption="QAE001-001-1-34")

        #各種申請者関連ボタン動確
        self.click_by_id("申請者情報表示部1_CmdKanjiShimei")
        self.screen_shot("QAE001-001-1-35", caption="QAE001-001-1-35")

        self.click_by_id("申請者情報表示部1_CmdJusho")
        self.screen_shot("QAE001-001-1-36", caption="QAE001-001-1-36")

        self.click_by_id("申請者情報表示部1_CmdKatagaki_span")
        self.screen_shot("QAE001-001-1-37", caption="QAE001-001-1-37")

        self.click_by_id("申請者情報表示部1_CmdClearBtn_span")
        self.screen_shot("QAE001-001-1-38", caption="QAE001-001-1-38")

        #申請者情報を入力
        self.click_by_id("span_KensakuBtn")
        self.screen_shot("QAE001-001-1-40", caption="QAE001-001-1-40")
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAE001//" + "QAE001-001-1-40-2" +".png")

        self.click_by_id("Sel1")
        self.screen_shot("QAE001-001-1-42", caption="QAE001-001-1-42")

        self.click_by_id("申請者情報表示部1_CmdJyukyu")
        self.select_Option(self.driver,self.find_element(By.ID,"申請者情報表示部1_CmdJyukyu"),"父")
        self.click_by_id("申請者情報表示部1_KankeiInfo")
        self.send_keys_by_name("申請者情報表示部1$KankeiInfo", "")
        self.send_keys_by_name("申請者情報表示部1$KankeiInfo", "父")
        self.click_by_id("申請者情報表示部1_TelNum1")
        self.send_keys_by_name("申請者情報表示部1$TelNum1", "")
        self.send_keys_by_name("申請者情報表示部1$TelNum1", "000-0000-000")
        self.click_by_id("申請者情報表示部1_TelNum2")
        self.send_keys_by_name("申請者情報表示部1$TelNum2", "")
        self.send_keys_by_name("申請者情報表示部1$TelNum2", "111-1111-111")

        #登録ボタン動確
        #self.accept_next_alert = True
        self.driver.find_element(By.ID, "CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        #self.click_by_id("CmdTouroku")
        #self.assertEqual(self.driver.title, "更新します。よろしいですか？")
        #self.assertEqual(u"更新します。よろしいですか？", self.close_alert_and_get_its_text())
        self.screen_shot("QAE001-001-1-44", caption="QAE001-001-1-44")

        #決定内容入力/登録
        self.click_by_id("CmdKettei")
        self.screen_shot("QAE001-001-1-45", caption="QAE001-001-1-45")

        self.send_keys_by_name("TxtKetteiYMD", "")
        self.send_keys_by_name("TxtKetteiYMD", "20210929")

        self.click_by_id("KetteiKekkaCmb")
        self.select_Option(self.driver,self.find_element(By.ID,"KetteiKekkaCmb"),"承認")

        # self.driver.find_element(By.ID, "span_CmdTouroku").click()
        # self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        # self.driver.switch_to.alert.accept()
        #self.click_by_id("span_CmdTouroku")
        #self.assertEqual(u"更新します。よろしいですか？", self.close_alert_and_get_its_text())

        #self.click_by_id("span_CmdYukoKikan")
        self.click_by_id("TxtYukoKikanKaishi")
        self.send_keys_by_name("TxtYukoKikanKaishi", "")
        self.send_keys_by_name("TxtYukoKikanKaishi", "20210901")
        self.click_by_id("TxtYukoKikanShuryo")
        self.send_keys_by_name("TxtYukoKikanShuryo", "")
        self.send_keys_by_name("TxtYukoKikanShuryo", "20210901")
        self.screen_shot("QAE001-001-1-47", caption="QAE001-001-1-47")

        #self.click_by_id("span_CmdYukoKikanNyuin")
        self.click_by_id("TxtYukoKikanKaishiNyuin")
        self.send_keys_by_name("TxtYukoKikanKaishiNyuin", "")
        self.send_keys_by_name("TxtYukoKikanKaishiNyuin", "20210901")
        self.click_by_id("TxtYukoKikanShuryoNyuin")
        self.send_keys_by_name("TxtYukoKikanShuryoNyuin", "")
        self.send_keys_by_name("TxtYukoKikanShuryoNyuin", "20210901")
        self.screen_shot("QAE001-001-1-48", caption="QAE001-001-1-48")

        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        self.assertEqual(self.driver.switch_to.alert.text, "更新します。よろしいですか？")
        self.driver.switch_to.alert.accept()
        #self.click_by_id("span_CmdTouroku")
        #self.assertEqual(u"更新します。よろしいですか？", self.close_alert_and_get_its_text())
        self.screen_shot("QAE001-001-1-49", caption="QAE001-001-1-49")

        #印刷動確
        # self.click_by_id("CmdInsatsu")
        # self.screen_shot("QAE001-001-1-50", caption="QAE001-001-1-50")
        #WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.ID, "insatsuChk_1")))

        # self.click_by_id("insatsuChk_1")

        # self.driver.find_element(By.ID, "InsatsuBtn").click()
        # self.assertEqual(self.driver.switch_to.alert.text, "印刷します。よろしいですか？")
        # self.driver.switch_to.alert.accept()
        #self.click_by_id("InsatsuBtn")
        #self.assertEqual(u"印刷します。よろしいですか？", self.close_alert_and_get_its_text())
        # self.screen_shot("QAE001-001-1-52", caption="QAE001-001-1-52")
        # self.click_by_id("GOBACK")
        # self.screen_shot("QAE001-001-1-53", caption="QAE001-001-1-53")
        # self.click_by_id("GOBACK")
        # self.screen_shot("QAE001-001-1-54", caption="QAE001-001-1-54")
