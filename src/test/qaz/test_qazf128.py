import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAZF128(FukushiSiteTestCaseBase):
    """TESTQAZF128"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        super().setUp()
    
    def test_case_qazf128_001(self):
        """test_case_qazf128_001"""
        driver = None
        test_data = self.common_test_data
        
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("qazf128_atena_code"), gyoumu_code="QAZ040")
        self.save_screenshot_migrate(driver, "QAZF128-001-07", True)
        self.find_common_buttons()
        self.open_common_buttons_area()
        self.common_button_click(button_text="児童相談一覧")  
        self.save_screenshot_migrate(driver, "QAZF128-001-09", True)
        self.find_element(By.ID,"GOBACK").click()
        self.find_element(By.ID,"GOBACK").click()