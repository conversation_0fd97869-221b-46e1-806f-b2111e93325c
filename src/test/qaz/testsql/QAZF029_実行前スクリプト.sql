DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ相談内容 WHERE  宛名コード = '$$QAZF029_ATENACODE$$';

INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ相談内容 (相談番号, 枝番号, 自治体コード, 福祉事務所コード, 支所コード, 宛名コード, 受付日, 受付時間, 受付担当者, 受付方法コード, 受付場所コード, 区分1コード, 区分2コード, 区分3コード, 区分4コード, 区分5コード, 区分6コード, 参照権限, 所属部, 所属係, 業務権限種類, 関連業務, タイトル, 内容, 回答, 削除フラグ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム
) VALUES
('300', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('301', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('302', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('303', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('304', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('305', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('306', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('307', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('308', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('309', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('310', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('311', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('312', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('313', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('314', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('315', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('316', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('317', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('318', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('319', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('320', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 '),
('321', '99', '99101', '00000', '', '$$QAZF029_ATENACODE$$', '20240201', '0709', '9501', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000000', '0000000001', '', '', '0', '', 'タイトル1', '', '', '0', '9501', '9501', getdate(), getdate(), 'QAZF030 ');