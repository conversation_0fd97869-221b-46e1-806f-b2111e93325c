use WR$$JICHITAI_CODE$$QA

DELETE FROM QAR資格履歴 where 宛名コード = '$$QAZF040_ATENACODE$$' and  業務コード = 'QAR010'and 履歴番号 >=1926 and 履歴番号<=1932;
     
INSERT INTO QAR資格履歴([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[申請年月日],[申請種別],[申請理由],[申請内容入力日],[進達年月日1],[進達判定年月日1],[進達結果1],[進達内容入力日1],[進達年月日2],[進達判定年月日2],[進達結果2],[進達内容入力日2],[決定年月日],[決定結果],[決定理由],[決定内容入力日],[業務固有コード1],[業務固有コード2],[業務固有コード3],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QAR010',1926,0,'99101','12101','','102000000100002','20240113',3,0,'20240113','00000000','00000000',0,'00000000','00000000','00000000',0,'00000000','20240113',1,0,'20240113','01230015','','','0','0','INES','INES',getdate(),getdate(),'00000000')

INSERT INTO QAR資格履歴([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[申請年月日],[申請種別],[申請理由],[申請内容入力日],[進達年月日1],[進達判定年月日1],[進達結果1],[進達内容入力日1],[進達年月日2],[進達判定年月日2],[進達結果2],[進達内容入力日2],[決定年月日],[決定結果],[決定理由],[決定内容入力日],[業務固有コード1],[業務固有コード2],[業務固有コード3],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QAR010',1927,0,'99101','12101','','102000000100002','20240113',3,0,'20240113','00000000','00000000',0,'00000000','00000000','00000000',0,'00000000','20240113',1,0,'20240113','01230015','','','0','0','INES','INES',getdate(),getdate(),'00000000')

INSERT INTO QAR資格履歴([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[申請年月日],[申請種別],[申請理由],[申請内容入力日],[進達年月日1],[進達判定年月日1],[進達結果1],[進達内容入力日1],[進達年月日2],[進達判定年月日2],[進達結果2],[進達内容入力日2],[決定年月日],[決定結果],[決定理由],[決定内容入力日],[業務固有コード1],[業務固有コード2],[業務固有コード3],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QAR010',1928,0,'99101','12101','','102000000100002','20240113',3,0,'20240113','00000000','00000000',0,'00000000','00000000','00000000',0,'00000000','20240113',1,0,'20240113','01230015','','','0','0','INES','INES',getdate(),getdate(),'00000000')

INSERT INTO QAR資格履歴([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[申請年月日],[申請種別],[申請理由],[申請内容入力日],[進達年月日1],[進達判定年月日1],[進達結果1],[進達内容入力日1],[進達年月日2],[進達判定年月日2],[進達結果2],[進達内容入力日2],[決定年月日],[決定結果],[決定理由],[決定内容入力日],[業務固有コード1],[業務固有コード2],[業務固有コード3],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QAR010',1929,0,'99101','12101','','102000000100002','20240113',3,0,'20240113','00000000','00000000',0,'00000000','00000000','00000000',0,'00000000','20240113',1,0,'20240113','01230015','','','0','0','INES','INES',getdate(),getdate(),'00000000')

INSERT INTO QAR資格履歴([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[申請年月日],[申請種別],[申請理由],[申請内容入力日],[進達年月日1],[進達判定年月日1],[進達結果1],[進達内容入力日1],[進達年月日2],[進達判定年月日2],[進達結果2],[進達内容入力日2],[決定年月日],[決定結果],[決定理由],[決定内容入力日],[業務固有コード1],[業務固有コード2],[業務固有コード3],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QAR010',1930,0,'99101','12101','','102000000100002','20240113',3,0,'20240113','00000000','00000000',0,'00000000','00000000','00000000',0,'00000000','20240113',1,0,'20240113','01230015','','','0','0','INES','INES',getdate(),getdate(),'00000000')

INSERT INTO QAR資格履歴([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[申請年月日],[申請種別],[申請理由],[申請内容入力日],[進達年月日1],[進達判定年月日1],[進達結果1],[進達内容入力日1],[進達年月日2],[進達判定年月日2],[進達結果2],[進達内容入力日2],[決定年月日],[決定結果],[決定理由],[決定内容入力日],[業務固有コード1],[業務固有コード2],[業務固有コード3],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QAR010',1931,0,'99101','12101','','102000000100002','20240113',3,0,'20240113','00000000','00000000',0,'00000000','00000000','00000000',0,'00000000','20240113',1,0,'20240113','01230015','','','0','0','INES','INES',getdate(),getdate(),'00000000')

INSERT INTO QAR資格履歴([業務コード],[履歴番号],[履歴分類],[自治体コード],[福祉事務所コード],[支所コード],[宛名コード],[申請年月日],[申請種別],[申請理由],[申請内容入力日],[進達年月日1],[進達判定年月日1],[進達結果1],[進達内容入力日1],[進達年月日2],[進達判定年月日2],[進達結果2],[進達内容入力日2],[決定年月日],[決定結果],[決定理由],[決定内容入力日],[業務固有コード1],[業務固有コード2],[業務固有コード3],[職権フラグ],[削除フラグ],[データ作成担当者],[データ更新担当者],[データ作成日時],[データ更新日時],[データ更新プログラム])
VALUES('QAR010',1932,0,'99101','12101','','102000000100002','20240113',3,0,'20240113','00000000','00000000',0,'00000000','00000000','00000000',0,'00000000','20240113',1,0,'20240113','01230015','','','0','0','INES','INES',getdate(),getdate(),'00000000')



