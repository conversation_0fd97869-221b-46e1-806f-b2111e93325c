DELETE FROM WR$$JICHITAI_CODE$$QA..QAC手当資格内容      WHERE 業務コード = 'QAC080' AND 宛名コード = '$$QAZF282_ATENACODE$$';

INSERT INTO WR$$JICHITAI_CODE$$QA..QAC手当資格内容(業務コード, 履歴番号, 自治体コード, 福祉事務所コード, 支所コード, 宛名コード, 手当種別, 県費種別, 被用区分, 受給者区分, 支給区分, 所得判定対象者, 算定対象児童数, 算定対象児童数内訳1, 算定対象児童数内訳2, 算定対象児童数内訳3, 算定対象児童数内訳4, 算定対象児童数内訳5, 手当月額, 差引き額, 上乗せ額1, 上乗せ額2, 実支給月額, 手当月額内訳1, 手当月額内訳2, 手当月額内訳3, 手当月額内訳4, 手当月額内訳5, 開始_改定_終了, 当初支給開始日, 減額開始年月, 事由発生日, 証書記号, 証書返還チェック, 住登外区分, 世帯類型, 未支給_返還の別, 未支給請求者_債権者宛名コード, 未支給請求者の受給者との関係, 未支払手当支給決定結果, 進達時連絡項目, 災害特例該当, 削除フラグ, データ作成担当者, データ更新担当者, データ作成日時, データ更新日時, データ更新プログラム, 誓約有無, 備考, 受付日, 返付日, 再提出日, 再返付日, 再々提出日, 返戻日, 再返戻日, 再進達日, 再々進達日, 審査済日, 審査依頼要否, 審査依頼内容, 審査結果, 審査依頼日, 審査結果受理日, 判定理由, 決定日, 転入前住所地最終支給年月, 依頼日, 報告日, 回答日, 相手先, 旧住所地への移管通知日, 新住所地への移管通知日, 証書交付日, 再提出有無, 養育費の取決の有無, 省略理由, 当初支給開始年月日, 五年等経過年月翌月時点の児童数, 依頼年月日, 証書を失ったときの事情, 都道府県への提出年月日, 都道府県への再提出年月日, 起案番号, 処理済年月日, 処理区分, 理由テキスト, 旧証書記号, 旧証書番号, 証書返付年月日, 台帳送付依頼フラグ, 台帳受領済フラグ, 台帳送付依頼有無, 台帳送付済フラグ, 停止上限額, 障害有無, 転出元自治体宛名役職, 転出先自治体宛名, 申請方法, 父母指定_申請年月日, 父母指定_宛名コード, 未支給請求者_申請年月日, 未支給請求者_決定年月日, 未支給請求者_却下事由)
VALUES('QAC080', '4344', '99101', '99301', '', '$$QAZF282_ATENACODE$$', '0000000000', '0000000000', '0000000001', '0000000000', '0000000000', '0000000000', '1', '0', '0', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '20240201', '00000000', '000000', '20240104', '', '0', '0', '0000000000', '0', '000000000000000', '0000000000', '0000000000', '0000000000', '0', '0', '9501', '9501', getdate(), getdate(), 'RACS005 ', '0000000000', '', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '00000000', '0', '', '', '00000000', '00000000', '', '00000000', '000000', '00000000', '00000000', '00000000', '', '00000000', '00000000', '00000000', '0', '0', '0000000000', '00000000', '', '00000000', '', '00000000', '00000000', '', '00000000', '0000000000', '', '', '', '00000000', '0', '0', '0', '0', '0', '0', '', '', '0000000000', '00000000', '000000000000000', '00000000', '00000000', '0000000000');
