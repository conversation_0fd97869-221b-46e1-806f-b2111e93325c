import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QAZF262(FukushiSiteTestCaseBase):
    """Test_QAZF262"""

    def test_case_001(self):
        """test_case_001"""
        driver = None
        # test_data = self.common_test_data
        
        self.do_login()
        self.click_button_by_label("マスタメンテナンス")
        self.click_button_by_label("指定医療情報メンテナンス")
        self.screen_shot("QAZF262_1",caption="QAZF262_指定医療マスタ")
        
        self.driver.find_element(By.ID, "KijunYMD").click()
        self.driver.find_element(By.ID, "KijunYMD").send_keys("令和02年01月01日")
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '身体障害者手帳']").click()
        self.driver.find_element(By.ID, "KakuteiBtn").click()
        self.driver.find_element(By.ID, "span_TsuikaBtn").click()
        self.driver.find_element(By.ID, "TxtYukoKStrYMD").click()
        self.driver.find_element(By.ID, "TxtYukoKStrYMD").send_keys("令和02年01月01日")
        self.driver.find_element(By.ID, "span_CmdIryokikan").click()
        self.driver.find_element(By.ID, "CmbTodofuken").click()
        dropdown = self.driver.find_element(By.ID, "CmbTodofuken")
        dropdown.find_element(By.XPATH, "//option[. = '東京都']").click()
        self.driver.find_element(By.ID, "BtnKensaku").click()
        self.driver.find_element(By.ID, "span_Sel2").click()
        self.driver.find_element(By.ID, "ChkSyougai_1").click()
        self.driver.find_element(By.ID, "TxtMeisho").click()
        self.driver.find_element(By.ID, "TxtMeisho").send_keys("ア")
        self.driver.find_element(By.ID, "CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF262_2",caption="QAZF262_登録／復活ボタン押下_I01")
        
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "span_KensakuBtn").click()
        self.screen_shot("QAZF262_3",caption="QAZF262_検索ボタン押下")
        self.driver.find_element(By.ID, "span_Sel1").click()
        self.driver.find_element(By.ID, "CmdShuusei").click()
        self.driver.find_element(By.ID, "TxtBikou").click()
        self.driver.find_element(By.ID, "TxtBikou").send_keys("a")
        self.driver.find_element(By.ID, "span_CmdTouroku").click()
        assert self.driver.switch_to.alert.text == "更新します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF262_4",caption="QAZF262_登録／復活ボタン押下_U01")
        
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "span_Sel1").click()
        self.driver.find_element(By.ID, "CmdSakujo").click()
        assert self.driver.switch_to.alert.text == "削除します。よろしいですか？"
        self.driver.switch_to.alert.accept()
        self.screen_shot("QAZF262_5",caption="QAZF262_削除ボタン押下_D01")
        
        self.driver.find_element(By.ID, "GOBACK").click()
        self.driver.find_element(By.ID, "span_KaijoBtn").click()
        self.driver.find_element(By.ID, "KijunYMD").click()
        self.driver.find_element(By.ID, "KijunYMD").send_keys("令和02年01月01日")
        self.driver.find_element(By.ID, "CmbGyomu").click()
        dropdown = self.driver.find_element(By.ID, "CmbGyomu")
        dropdown.find_element(By.XPATH, "//option[. = '自立支援医療(更生)']").click()
        self.driver.find_element(By.ID, "KakuteiBtn").click()
        self.form_input_by_id(idstr="ChkDelZumi", value="1")
        self.driver.find_element(By.ID, "span_KensakuBtn").click()
        self.driver.find_element(By.ID, "span_Sel2").click()
        self.driver.find_element(By.ID, "CmdTsuika").click()
        self.screen_shot("QAZF262_6",caption="QAZF262_追加ボタン押下")
        
        self.driver.find_element(By.ID, "span_CmdShoki").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.screen_shot("QAZF262_7",caption="QAZF262_初期表示ボタン押下_D01")
        
        self.driver.find_element(By.ID, "span_CmdNextPage").click()
        self.screen_shot("QAZF262_8",caption="QAZF262_次頁ボタン押下")
        
        self.driver.find_element(By.ID, "span_CmdBackPage").click()
        self.screen_shot("QAZF262_9",caption="QAZF262_前頁ボタン押下")
        
        self.driver.find_element(By.ID, "CmbPage").click()
        dropdown = self.driver.find_element(By.ID, "CmbPage")
        dropdown.find_element(By.XPATH, "//option[. = '02']").click()
        self.driver.find_element(By.ID, "span_CmdJumpPage").click()
        self.screen_shot("QAZF262_10",caption="QAZF262_へ移動ボタン押下")
        
        self.driver.find_element(By.ID, "CmbPage").click()
        dropdown = self.driver.find_element(By.ID, "CmbPage")
        dropdown.find_element(By.XPATH, "//option[. = '01']").click()
        self.driver.find_element(By.ID, "span_CmdJumpPage").click()
        self.screen_shot("QAZF262_11",caption="QAZF262_へ移動ボタン押下")
