import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAZF116(FukushiSiteTestCaseBase):
    """TESTQAZF116"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        self.exec_sqlfile("QAZF116_実行前スクリプト.sql")
        super().setUp()

    def tearDown(self):
        self.exec_sqlfile("QAZF116_実行後スクリプト.sql")
        super().tearDown()

    def test_case_qazf116_001(self):
        """test_case_qazf116_001"""
        driver = None
        test_data = self.common_test_data
    
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=test_data.get("qazf116_atena_code"), gyoumu_code="QAS790")

        self.save_screenshot_migrate(driver, "QAZF116-001-07" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAZF116-001-08", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAZF116-001-09", True)