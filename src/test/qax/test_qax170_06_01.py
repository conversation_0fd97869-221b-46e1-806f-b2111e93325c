import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
from base.kyufu_case import KyufuSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select


class TestQAX1700601(FukushiSiteTestCaseBase, KyufuSiteTestCaseBase):
    """TestQAX170_06_01"""


    def test_case_QAX170_06_01(self):
        """test_case_QAX170_06_01"""

        driver = None

        # 宛名コード取得
        common_test_data = self.common_test_data
        QAX170_atena_code2 = common_test_data.get("case_common_QAX170_atena_code2")
        QAX170_atena_code3 = common_test_data.get("case_common_QAX170_atena_code3")

        # select文を実行して宛名コードから申請書番号を取得
        _db = self.get_db() 

        sql_text = """
        SELECT 申請書番号
        FROM WR$$JICHITAI_CODE$$QA.dbo.QAX170臨時福祉給付金世帯 
        WHERE 申請受給者住民コード = '$$ATENA_CODE$$' and 世代 = '2'
        """
        params = {"ATENA_CODE": QAX170_atena_code2}
        ret = _db.exec_sql_result(sql_text, params=params)

        # [(Decimal('9000000446'),)] のように配列として値が入ってくるので0番目だけを取り出す
        for rec in ret:
            shinsei_no = str(rec[0])
            break

        # パラメータ設定
        case_data = self.test_data["case06_01"]
        shiharaiyotei_date = case_data.get("shiharaiyotei_date")
        
        # ログイン
        self.do_login()

        # メインメニュー画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-1" , True)

        # 「バッチ検索」ボタン押下
        self.find_element(By.XPATH, "//button[normalize-space()='バッチ検索']").click()

        # バッチ検索画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-3" , True)
        
        # 「ジョブID」入力
        self.find_element(By.ID, "TxtJobID").send_keys("QAXJW050")
        
        # 「検索」ボタン押下
        self.find_element(By.ID, "CmdKensaku").click()
        
        # バッチ起動画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-6" , True)
        
        # 「支払予定日」入力
        self.find_element(By.ID, "item2").send_keys(shiharaiyotei_date)

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")

        # バッチ起動画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-9" , True)

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(120,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # ジョブ実行履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-12" , True)

        # ジョブ実行履歴画面
        # 「戻る」ボタン押下
        self.return_click()

        # バッチ起動画面
        # 「戻る」ボタン押下
        self.return_click()


        # 振込エラーリストを出力するためにデータをUPDATEする
        sql_text2 = """
        UPDATE WR$$JICHITAI_CODE$$QA.dbo.QAX170W臨時福祉給付金口座振込情報
        SET 口座番号='0000000'
        WHERE 宛名コード= '$$ATENA_CODE$$'
        """
        params = {"ATENA_CODE": QAX170_atena_code3}
        ret2 = _db.exec_sql(sql_text2, params=params)


        # メインメニュー画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-15" , True)

        # 「バッチ検索」ボタン押下
        self.find_element(By.XPATH, "//button[normalize-space()='バッチ検索']").click()

        # バッチ検索画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-17" , True)
        
        # 「ジョブID」入力
        self.find_element(By.ID, "TxtJobID").send_keys("QAXJW052")
        
        # 「検索」ボタン押下
        self.find_element(By.ID, "CmdKensaku").click()
        
        # バッチ起動画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-20" , True)
        
        # 「支払予定日」入力
        self.find_element(By.ID, "item4").send_keys(shiharaiyotei_date)

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")

        # バッチ起動画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-23" , True)

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(120,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # ジョブ実行履歴から「正常終了」のジョブ番号の取得(PDFダウンロードで使用する)
        exec_job_ID = self.get_job_execute_id("QAXJW052")

        # ジョブ実行履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-26" , True)

        # 「帳票履歴」ボタン押下
        self.click_report_log()

        # ジョブ帳票履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-28" , True)
        
        # 帳票履歴画面の「検索」ボタン押下
        self.find_element(By.ID, "SearchButton").click()

        # ジョブ帳票履歴画面表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-30" , True)

        # 今回の処理で作成したPDFのDL
        self.do_report_download_edge(exec_job_ID)

        # ジョブ帳票履歴画面
        # 「戻る」ボタン押下
        self.return_click()

        # バッチ検索画面
        # 「戻る」ボタン押下
        self.return_click()

        # メインメニュー画面 
        # 「非課税世帯給付金」ボタン押下
        self.find_element(By.XPATH, "//button[contains(@title,'非課税世帯給付金')]").click()

        # サブメニュー表示(エビデンス取得)
        self.save_screenshot_migrate(driver, "QAX170-06-01-46" , True)

        # 「申請台帳参照」ボタンを押下し、申請書発行画面で「申請書番号」を検索
        self.search_ShinseiNo("QAX170-06-01-48", shinsei_no, "QAX170-06-01-51")

        # ステータス表示が「支給済」であることを確認
        self.assert_shinsei_status("支給済")
