DROP TABLE IF EXISTS [WR99101FA].[dbo].[WK_QAX用_T宛名]
SELECT * INTO [WR99101FA].[dbo].[WK_QAX用_T宛名] FROM [WR99101FA].[dbo].[T宛名] 
WHERE 住民コード IN (
	'$$QAX_BASE_ATENACODE01$$',
	'$$QAX_BASE_ATENACODE02$$',
	'$$QAX_BASE_ATENACODE03$$',
	'$$QAX_BASE_ATENACODE04$$',
	'$$QAX_BASE_ATENACODE05$$',
	'$$QAX_BASE_ATENACODE06$$',
	'$$QAX_BASE_ATENACODE07$$',
	'$$QAX_BASE_ATENACODE08$$',
	'$$QAX_BASE_ATENACODE09$$',
	'$$QAX_BASE_ATENACODE10$$');
UPDATE [WR99101FA].[dbo].[WK_QAX用_T宛名] 
set 住民コード = '$$QAX170_ATENACODE01$$',
	世帯コード = '$$QAX170_ATENACODE01$$',
	住民となった事由 = '01',
	住民でなくなった日 = NULL,
	住民でなくなった届出日 = NULL,
	住民でなくなった事由 = NULL
WHERE 住民コード='$$QAX_BASE_ATENACODE01$$';
UPDATE [WR99101FA].[dbo].[WK_QAX用_T宛名] 
set 住民コード = '$$QAX170_ATENACODE02$$',
	世帯コード = '$$QAX170_ATENACODE02$$',
	住民となった日 = '2023-01-02 00:00:00.000'
WHERE 住民コード='$$QAX_BASE_ATENACODE02$$';
UPDATE [WR99101FA].[dbo].[WK_QAX用_T宛名] 
set 住民コード = '$$QAX170_ATENACODE03$$',
	世帯コード = '$$QAX170_ATENACODE03$$',
	名称 = '検証　桂太郎あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもらりるれろあいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもらりるれろ'
WHERE 住民コード='$$QAX_BASE_ATENACODE03$$';
UPDATE [WR99101FA].[dbo].[WK_QAX用_T宛名] 
set 住民コード = '$$QAX170_ATENACODE04$$',
	世帯コード = '$$QAX170_ATENACODE04$$',
	住民となった日 = '2023-01-02 00:00:00.000',
	名称 = '検証　桂太郎あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもらりるれろあいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもらりるれろ'
WHERE 住民コード='$$QAX_BASE_ATENACODE04$$';
UPDATE [WR99101FA].[dbo].[WK_QAX用_T宛名] 
set 住民コード = '$$QAX170_ATENACODE05$$',
	世帯コード = '$$QAX170_ATENACODE05$$',
	住民となった事由 = '01',
	住民でなくなった日 = NULL,
	住民でなくなった届出日 = NULL,
	住民でなくなった事由 = NULL
WHERE 住民コード='$$QAX_BASE_ATENACODE05$$';
UPDATE [WR99101FA].[dbo].[WK_QAX用_T宛名] 
set 住民コード = '$$QAX170_ATENACODE06$$',
	世帯コード = '$$QAX170_ATENACODE06$$',
	住民となった事由 = '01',
	住民でなくなった日 = NULL,
	住民でなくなった届出日 = NULL,
	住民でなくなった事由 = NULL
WHERE 住民コード='$$QAX_BASE_ATENACODE06$$';
UPDATE [WR99101FA].[dbo].[WK_QAX用_T宛名] 
set 住民コード = '$$QAX170_ATENACODE07$$',
	世帯コード = '$$QAX170_ATENACODE07$$',
	住民となった日 = '2023-01-02 00:00:00.000'
WHERE 住民コード='$$QAX_BASE_ATENACODE07$$';
UPDATE [WR99101FA].[dbo].[WK_QAX用_T宛名] 
set 住民コード = '$$QAX170_ATENACODE08$$',
	世帯コード = '$$QAX170_ATENACODE08$$',
	住民となった事由 = '01',
	住民でなくなった日 = NULL,
	住民でなくなった届出日 = NULL,
	住民でなくなった事由 = NULL,
	名称 = '検証　葵あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもらりるれろあいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもらりるれろ'
WHERE 住民コード='$$QAX_BASE_ATENACODE08$$';
UPDATE [WR99101FA].[dbo].[WK_QAX用_T宛名] 
set 住民コード = '$$QAX170_ATENACODE09$$',
	世帯コード = '$$QAX170_ATENACODE09$$',
	住民となった日 = '2023-01-02 00:00:00.000',
	名称 = '検証　早苗あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもらりるれろあいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもらりるれろ'
WHERE 住民コード='$$QAX_BASE_ATENACODE09$$';
UPDATE [WR99101FA].[dbo].[WK_QAX用_T宛名] 
set 住民コード = '$$QAX170_ATENACODE10$$',
	世帯コード = '$$QAX170_ATENACODE10$$'
WHERE 住民コード='$$QAX_BASE_ATENACODE10$$';
INSERT INTO [WR99101FA].[dbo].[T宛名] SELECT * FROM [WR99101FA].[dbo].[WK_QAX用_T宛名];