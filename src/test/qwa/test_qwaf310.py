import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class Test_QWAF310(FukushiSiteTestCaseBase):
    """Test_QWAF310"""
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        # self.exec_sqlfile("QAZF066_実行前スクリプト.sql", params=atena_list)
        super().setUp()

    def test_case_001(self):
        """test_case_001"""
        driver = None
        test_data = self.common_test_data
        
        self.do_login()
        self.click_button_by_label("白紙申請書出力")
        self.screen_shot("QWAF310_1",caption="QWAF310_白紙申請書出力画面")
        
        self.find_element(By.ID, "CmbJigyoID").click()
        dropdown = self.find_element(By.ID, "CmbJigyoID")
        dropdown.find_element(By.XPATH, "//option[. = '日常生活用具・住宅設備']").click()
        self.find_element(By.ID, "span_CmdKensaku").click()
        self.screen_shot("QWAF310_2",caption="QWAF310_検索ボタン押下")
        
        self.find_element(By.ID, "ChkChohyoNo_6").click()
        self.pdf_output_and_download_no_alert(button_id="CmdInsatsu", case_name="QWAF310_印刷ボタン押下")
        self.screen_shot("QWAF310_3",caption="QWAF310_印刷ボタン押下")
        
        self.find_element(By.ID, "CmdCheck").click()
        self.screen_shot("QWAF310_4",caption="QWAF310_出力対象のみボタン押下")
        