DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC過払月額] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC過払情報] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC公的年金計算結果] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC差止履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC債権計画] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC債権計画月別] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC債権情報] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC債権返納] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC支払履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC支払履歴_児童数内訳] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC資格履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC児童備考] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当支給要件児童] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当支給要件児童仮] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当資格内容] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当資格内容仮] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当障害審査情報] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC手当障害要件] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC住記行政欄データ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC障害者給付金] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC調整月額] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC適用除外] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC天引月別支払履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC天引月別支払履歴内訳] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC天引申請情報管理] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC天引申請情報児童] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC特記事項] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC汎用現況履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'

IF OBJECT_ID('[WR$$JICHITAI_CODE$$QA].dbo.[QAC被災状況]') IS NOT NULL
BEGIN
    DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC被災状況] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [本人宛名コード] = '$$DELETE_ATENA_CODE$$'
END

DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC不支給履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAC別居監護申立申請情報] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'

DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ現況履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ現況提出書類] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ提出書類内容] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ提出書類履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ所得税マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ受給状況] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZメモ情報] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ福祉世帯] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [本人宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QA.dbo.[QAZ帳票発行履歴] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'

DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ連絡先] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ緊急連絡先] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ送付先マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ居住地マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ届出住所マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ住所登録者マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [宛名コード] = '$$DELETE_ATENA_CODE$$'
DELETE FROM WR$$JICHITAI_CODE$$QZ.dbo.[QZ口座マスタ] WHERE [業務コード] = '$$TARGET_GYOUMU_CODE$$' AND [固有コード] = '$$DELETE_ATENA_CODE$$' AND [区分] = '0000000001'
