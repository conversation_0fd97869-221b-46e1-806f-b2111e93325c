from base.fukushi_case import FukushiSiteTestCaseBase



class TestQAC020_1050703(FukushiSiteTestCaseBase):
    """TestQAC020_1050703"""

    def setUp(self):
        super().setUp()

    # 差止情報が登録されている対象者の一覧が出力できることを確認する。
    def test_QAC020_1050703(self):
        """差止対象者の抽出"""

        case_data = self.test_data["TestQAC020_1050703"]
        output_kubun = case_data.get("output_kubun", "")
        gyomu_name = case_data.get("gyomu_name", "")
        jigyo_name = case_data.get("jigyo_name", "")
        shori_kubun_name = case_data.get("shori_kubun_name", "")
        shori_bunrui_name = case_data.get("shori_bunrui_name", "")
        batch_job_001 = case_data.get("batch_job_001")
        report_name = case_data.get("report_name")

        # 68 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_68")

        # 69 メインメニュー画面: メインメニューから「バッチ起動」ボタン押下
        self.batch_kidou_click()
        self.screen_shot("バッチ起動画面_69")

        # 70 バッチ起動画面: 業務：障害 事業：特別障害者手当 処理区分：月次処理 処理分類：確認処理
        self.form_input_by_id(idstr="GyomuSelect", text=gyomu_name)
        self.form_input_by_id(idstr="JigyoSelect", text=jigyo_name)
        self.form_input_by_id(idstr="ShoriKubunSelect", text=shori_kubun_name)
        self.form_input_by_id(idstr="ShoriBunruiSelect", text=shori_bunrui_name)
        self.screen_shot("バッチ起動画面_70")

        # 71 バッチ起動画面:「差止対象者一覧出力処理」のNoボタン押下
        self.click_batch_job_button_by_label(batch_job_001)

        # 72 バッチ起動画面: 出力区分「0」（又は空白）
        params = [
            {"title": "出力区分", "type": "text", "value": output_kubun}
        ]
        self.set_job_params(params)
        self.screen_shot("バッチ起動画面_72")

        # 73 バッチ起動画面:「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_73")

        # 74 バッチ起動画面:「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("ジョブ実行履歴画面_74")

        # 75 ジョブ実行履歴画面:「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_75")

        # 76 ジョブ実行履歴画面:「帳票履歴」ボタン押下
        self.click_report_log()
        self.screen_shot("ジョブ帳票履歴画面_76")

        # 77 ジョブ帳票履歴画面:「検索」ボタン押下
        # 78 ジョブ帳票履歴画面:「差止対象者一覧出力」のNoボタン押下
        # 79 ジョブ帳票履歴画面:「ファイルを開く」ボタン押下
        # 80 差止対象者一覧出力（PDF）: 差止対象者一覧を×ボタン押下でPDFを閉じる
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name=report_name)
        self.screen_shot("ジョブ帳票履歴画面_77")
