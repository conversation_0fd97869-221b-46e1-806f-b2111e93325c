from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050201(FukushiSiteTestCaseBase):
    """TestQAC020_1050201"""

    def setUp(self):
        super().setUp()

    # 支払が過足し、過払いとなっている対象者を抽出できることを確認する。
    def test_QAC020_1050201(self):
        """債権対象者検索"""

        case_data = self.test_data["TestQAC020_1050201"]

        # 1,2 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_1")

        # 3	メインメニュー画面: メインメニューから「支払調整」ボタン押下
        self.shiharai_chousei_click()

        # 4	支払調整履歴画面: 事業「障害児福祉手当」選択 絞り込み条件「未調整＋調整中」チェック
        self.form_input_by_id(idstr="CmbGyomu", text=case_data.get("cmb_gyomu", ""))
        self.form_input_by_id(idstr="RadioMC", value=case_data.get("radio_mc", ""))
        self.screen_shot("支払調整履歴画面_4")

        # 5	支払調整履歴画面:「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("支払調整履歴画面_5")

        # 6	支払調整履歴画面:「戻る」ボタン押下
        self.return_click()
        self.screen_shot("メインメニュー画面_6")

        # 7	メインメニュー画面:「債権管理」ボタン押下
        self.saiken_kanri_click()

        # 8	債権履歴画面: 事業「障害児福祉手当」選択 絞り込み条件「未返納＋返納中」チェック
        self.form_input_by_id(idstr="CmbGyomu", text=case_data.get("cmb_gyomu_1", ""))
        self.form_input_by_id(idstr="RadioMC", value=case_data.get("radio_mc_1", ""))
        self.screen_shot("債権履歴画面_8")

        # 9	債権履歴画面:「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("債権履歴画面_9")

        # 10 債権履歴画面:「戻る」ボタン押下
        self.return_click()
        self.screen_shot("メインメニュー画面_10")
