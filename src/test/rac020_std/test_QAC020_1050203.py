from selenium.webdriver.common.by import By

from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050203(FukushiSiteTestCaseBase):
    """TestQAC020_1050203"""

    def setUp(self):
        super().setUp()

    # 返納について計画情報を作成、返納登録ができることを確認する。
    def test_QAC020_1050203(self):
        """債権対象者検索"""

        case_data = self.test_data["TestQAC020_1050203"]
        nintei_code = case_data.get("nintei_code", "")

        # 23 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_23")

        # 24 メインメニュー画面: メインメニューから「債権管理」ボタン押下
        self.saiken_kanri_click()
        self.screen_shot("債権履歴画面_24")

        # 25 債権履歴画面: 事業「障害児福祉手当」選択 絞り込み条件「未返納」チェック
        self.form_input_by_id(idstr="CmbGyomu", text=case_data.get("cmb_gyomu", ""))
        self.form_input_by_id(idstr="RadioMC", value=case_data.get("radio_mc", ""))
        self.screen_shot("債権履歴画面_25")

        # 26 債権履歴画面:「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("債権履歴画面_26")

        # 27 債権履歴画面: 該当者一覧 「1」Noボタン押下
        nintei_col = '3'
        tb_Page = self.find_element_by_xpath('//*[@id="_wr_body_panel"]/table[5]/tbody/tr/td/b')
        maxPage = str(tb_Page.text).replace("／", "")
        is_found_nintei = False
        for page_index in range(int(maxPage)):
            tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
            table_idx = 0
            for elem in tr_elem:
                table_idx += 1
                td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + nintei_col + ")")
                if nintei_code == td_elem.text:
                    self.click_by_id("span_Sel" + str(table_idx))
                    is_found_nintei = True
                    break
            if is_found_nintei: break
            self.click_by_id("CmdNextPage")
        self.screen_shot("債権情報画面_27")

        # 28 債権情報画面:「修正」ボタン押下
        self.click_button_by_label("修正")

        # 29 債権情報画面:「計算」ボタン押下
        self.click_button_by_label("計算")

        # 30 債権情報画面:「計画作成」ボタン押下
        self.click_button_by_label("計画作成")
        self.screen_shot("返済計画作成画面_30")

        # 31 返済計画作成画面:「再計画」ボタン押下
        self.click_button_by_label("再計画")

        # 32 返済計画作成画面: 返納予定月額「1000」返納予定開始年月「202306」
        self.form_input_by_id(idstr="TxtYoteigaku", value=case_data.get("txt_yoteigaku", ""))
        self.form_input_by_id(idstr="TxtKaishi", value=case_data.get("txt_kaishi", ""))
        self.screen_shot("返済計画作成画面_32")

        # 33 返済計画作成画面:「計画作成」ボタン押下
        self.click_button_by_label("計画作成")

        # 34 返済計画作成画面:「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.screen_shot("債権情報画面_34")

        # 35 債権情報画面:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        self.screen_shot("債権履歴画面_35")

        # 36 債権履歴画面: 該当者一覧 「1」Noボタン押下
        nintei_col = '3'
        table_idx = 0
        tr_elem = self.find_elements_by_css_selector("#_wrFk_TitleTable_Div_1 > table > tbody > tr")
        for elem in tr_elem:
            table_idx += 1
            td_elem = elem.find_element(By.CSS_SELECTOR, "td:nth-child(" + nintei_col + ")")
            if nintei_code == td_elem.text:
                self.click_by_id("span_Sel" + str(table_idx))
                break
        self.screen_shot("債権情報画面_36")

        # 37 債権情報画面:「修正」ボタン押下
        self.click_button_by_label("修正")

        # 38 債権情報画面: 返納状況一覧 「N」Noボタン押下
        self.click_oldest_hennou()

        # 39 債権入金登録画面: 入金日「20230630」入金額「1000」「歳入」にチェック
        self.form_input_by_id(idstr="TxtNyukinbi", value=case_data.get("txt_nyukinbi", ""))
        self.form_input_by_id(idstr="TxtNyukingaku", value=case_data.get("txt_nyukingaku", ""))
        self.form_input_by_id(idstr="RdoSainyu", value=case_data.get("rdo_sainyu", ""))
        self.screen_shot("債権入金登録画面_39")

        # 40 債権入金登録画面:「入力完了」ボタン押下
        self.click_button_by_label("入力完了")

        # 41 債権情報画面:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        self.screen_shot("債権履歴画面_41")

        # 42 債権履歴画面:「戻る」ボタン押下
        self.return_click()
        self.screen_shot("メインメニュー画面_42")
