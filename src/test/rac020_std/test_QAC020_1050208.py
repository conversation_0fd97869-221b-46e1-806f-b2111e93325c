from base.fukushi_case import FukushiSiteTestCaseBase


class TestQAC020_1050208(FukushiSiteTestCaseBase):
    """TestQAC020_1050208"""

    def setUp(self):
        super().setUp()

    # 振込不能結果より振込不能情報を登録できることを確認する。
    def test_QAC020_1050208(self):
        """振込不能登録"""

        case_data = self.test_data["TestQAC020_1050208"]
        atena_code = case_data.get("atena_code", "")

        # 114 メインメニュー画面:「総合福祉メニュー」
        self.do_login()
        self.screen_shot("メインメニュー画面_114")

        # 115 メインメニュー画面: メインメニューから「申請資格管理」ボタン押下
        self.shinsei_shikaku_kanri_click()

        # 116 個人検索画面:「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)
        self.screen_shot("個人検索画面_116")

        # 117 個人検索画面:「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("個人検索画面_117")

        # 118 受給状況画面:「障害児福祉手当」ボタン押下
        self.click_jukyujoukyou_by_gyoumu_code(gyoumu_code="QAC020")
        self.screen_shot("障害児福祉手当資格管理画面_118")

        # 119 障害児福祉手当資格管理画面:「修正」ボタン押下
        self.click_button_by_label("修正")

        # 120 障害児福祉手当資格管理画面:「支払履歴」ボタン押下
        self.click_button_by_label("支払履歴")
        self.screen_shot("支払履歴画面_120")

        # 121 支払履歴画面: 支払履歴一覧「1」Noボタン押下
        self.click_button_by_label("1")
        self.screen_shot("支払実績登録画面_121")

        # 122 支払実績登録画面:「修正」ボタン押下
        self.click_button_by_label("修正")

        # 123 支払実績登録画面: 不能にチェック
        self.form_input_by_id(idstr="CbxImpossible", value=case_data.get("cbx_impossible", ""))
        self.screen_shot("支払実績登録画面_123")

        # 124 支払実績登録画面:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        self.screen_shot("支払実績登録画面_124")

        # 125 支払実績登録画面:「戻る」ボタン押下
        # 126 支払実績登録画面:「戻る」ボタン押下
        self.return_click()
        self.screen_shot("障害児福祉手当資格管理画面_126")

        # 127 障害児福祉手当資格管理画面:「月額計算」ボタン押下
        self.click_button_by_label("月額計算")

        # 128 障害児福祉手当資格管理画面:「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        self.assert_message_area("登録しました")
        self.screen_shot("障害児福祉手当資格管理画面_128")
