import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAB01110135(FukushiSiteTestCaseBase):
    """TESTRAB01110135"""

    # 補装具申請者に対して、決定登録ができることを確認する。支給券情報（支給番号）が自動配番されていることを確認する。
    def test_case_rab011101_35(self):
        """支給決定情報登録"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAB010")

        # 1 保健福祉総合システム > 福祉基本情報 > 履歴選択画面 > 「履歴選択一覧」の「資格管理」ボタン押下
        self.click_by_id("CmdButton1_1") 

        # 1 補装具費支給資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 2 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-35]_補装具費支給資格管理画面_2")

        # 3 補装具費支給資格管理画面: 決定日「〇〇」入力決定結果「決定」入力交付日「〇〇」入力
        self.form_input_by_id(idstr="TxtKetteiYMD", value=case_data.get("kettei_ymd", ""))
        self.form_input_by_id(idstr="KetteiKekkaCmb", text=case_data.get("KetteiKekkaCmb", ""))
        self.form_input_by_id(idstr="TxtKoufuYMD", value=case_data.get("kofu_ymd", ""))

        # 4 補装具費支給資格管理画面: 「指導記録」ボタン押下
        # self.click_button_by_label("指導記録")

        # 5 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-35]_補装具費支給資格管理画面_5")

        # 6 補装具費支給資格管理画面: 備考「備考欄入力テスト」入力
        self.form_input_by_id(idstr="TxtBikou", value="備考欄入力テスト")

        # 7 補装具費支給資格管理画面: 「支給券データ作成」ボタン押下
        self.click_button_by_label("支給券データ作成")

        # 8 補装具費支給資格管理画面: 表示
        self.screen_shot("[011101-35]_補装具費支給資格管理画面_8")

        # 9 補装具費支給資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 10 補装具費支給資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました。")
        self.screen_shot("[011101-35]_補装具費支給資格管理画面_10")
