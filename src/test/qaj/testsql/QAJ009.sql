DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN
-- シナリオ009-002
DELETE WR$$JICHITAI_CODE$$QA..QAJサービス利用計画実績管理            WHERE 宛名コード = '00099002' AND 業務コード = 'QAJ010'
DELETE WR$$JICHITAI_CODE$$QA..QAJサービス利用計画申請　　　　　　　　WHERE 宛名コード = '00099002' AND 業務コード = 'QAJ010'

-- シナリオ009-003
--DELETE WR$$JICHITAI_CODE$$QA..QAJ同行援助アセスメント票情報          WHERE 宛名コード = '00099002' AND 業務コード = 'QAJ010'

-- シナリオ009-004
DELETE WR$$JICHITAI_CODE$$QA..QAJ特例給付申請決定管理                WHERE 宛名コード = '00099002' AND 業務コード = 'QAJ010'
--DELETE WR$$JICHITAI_CODE$$QA..QAZ資格共通更新                        WHERE 宛名コード = '00099002' AND 業務コード = 'QAJ010'

-- シナリオ009-005
DELETE WR$$JICHITAI_CODE$$QA..QAJ高額申請管理                        WHERE 宛名コード = '00099010' AND 業務コード = 'QAJ010'

-- シナリオ009-010
DELETE WR$$JICHITAI_CODE$$QA..QAJ自立支援特定期間支給量管理          WHERE 宛名コード = '00090060' AND 業務コード = 'QAJ010'

-- シナリオ009-011
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連契約情報                      WHERE 宛名コード = '00090060' AND 業務コード = 'QAJ010'


IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END