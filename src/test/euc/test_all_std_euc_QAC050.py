import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
from util.helper import WebDriverHandleHelper

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestEUCAllQAC050STD001(FukushiSiteTestCaseBase):
    """標準化EUCのQAC050を全実行する"""
    
    def test_case_001(self):
        """標準化EUCのQAC050を全実行する"""
        gyoumu_code_prefix = "QAC050"
        span_results = []

        self.do_login()
        self.transit_euc_std()

        self.screen_shot("EUC初画面")

        # EUCのIFRAMEに制御を移す
        WebDriverHandleHelper.change_euc_driver(self.driver)
        
        # EUCの全名前一覧を取得
        name_list = WebDriverHandleHelper.get_euc_name_list(self.driver)
        error_count = 0
        fail_count = 0
        csv_zero_count = 0
        # EUC名でループ
        for i, rec in enumerate(name_list):
            if(not rec.startswith(gyoumu_code_prefix)):
                continue

            try:
                # EUCのIFRAMEに制御を移す
                WebDriverHandleHelper.change_euc_driver(self.driver)
                # EUCを開く
                WebDriverHandleHelper.open_euc_by_name(self.driver, rec)
                WebDriverHandleHelper.click_max_col_warn_dialog_if_visible(self.driver)
                # 検索ボタンクリック
                span_time_ms = WebDriverHandleHelper.euc_search_click(self.driver)
                span_results.append({"name":rec, "span": span_time_ms})
                
                self.logger.info(f"EUC-SEARCH-SPAN_MS: {rec} , {span_time_ms}")
                self.screen_shot(f"{rec}_RESULT")

                if(WebDriverHandleHelper.click_euc_no_result_alert_if_visible(self.driver)):
                    # 0件のアラートが出てる場合
                    fail_count += 1
                    self.logger.info(f"EUC-SEARCH-ZERO-RESULT: {rec}")
                else:
                    # CSV出力ボタンクリック
                    if(WebDriverHandleHelper.click_euc_csv_output_dialog_open_button(self.driver)):
                        csv_file_name = f"EUCALL_{rec}"
                        csv_outputed = WebDriverHandleHelper.click_euc_csv_output_button(self.driver,csv_file_name)
                        time.sleep(1)  # 少し待たないとCSVが出来てない
                        if(csv_outputed):
                            proc_tmp_ret = False
                            proc_tmp_ret = WebDriverHandleHelper.open_euc_side_menu_by_name(self.driver, "ファイル出力結果")
                            if(proc_tmp_ret):
                                proc_tmp_ret = WebDriverHandleHelper.click_euc_csv_file_list_button(self.driver)
                            if(proc_tmp_ret):
                                proc_tmp_ret = WebDriverHandleHelper.click_euc_no_file_list_result_alert_if_visible(self.driver)
                                if(proc_tmp_ret is False):
                                    WebDriverHandleHelper.down_load_euc_csv_file_by_file_name(self.driver, self, case_name=gyoumu_code_prefix, file_name=csv_file_name,is_after_delete=True)
                            WebDriverHandleHelper.click_euc_csv_file_list_dialog_close(self.driver)
                        else:
                            csv_zero_count += 1
                            self.logger.info(f"EUC-SEARCH-CSV-OUTPUT-FAIL: {rec}")
                            WebDriverHandleHelper.click_euc_csv_output_cancel_if_visible(self.driver)
                # EUCのタブを全部閉じる
                WebDriverHandleHelper.close_euc_all_tab(self.driver)
            except Exception as e:
                error_count += 1
                self.logger.info(f"EUC-SEARCH-ERROR: {rec}")
                self.logger.info(str(e))
                self.screen_shot(f"{rec}_RESULT_ERROR")

        is_over_span = False
        old_span_lines = WebDriverHandleHelper.read_lines_file_data_store(self, f"{gyoumu_code_prefix}.txt", "span_compare")
        old_span_lines = [line.split(",") for line in old_span_lines]
        check_result = []
        write_lines = []
        for item in span_results:
            current_name = item["name"]
            current_ms = item["span"]
            ok_ng = "OK"
            target_rec = [rec[1] for rec in old_span_lines if rec[0] == current_name]
            if(len(target_rec) > 0):
                old_ms = int(target_rec[0])
                if(current_ms - old_ms > 10000):
                    ok_ng = "NG"
                    is_over_span = True
                write_lines.append(f"{current_name},{current_ms},{old_ms},{ok_ng}")
            else:
                write_lines.append(f"{current_name},{current_ms},0,{ok_ng}")
        write_file_path = WebDriverHandleHelper.write_text_file_data_store(self,f"{gyoumu_code_prefix}.txt", "span_compare","\n".join(write_lines))
        self.driver.get("file:///" + write_file_path)
        self.screen_shot("検索経過時間一覧_名称_今回MS_前回MS_OKNG")

        self.assertEqual(fail_count, 0)
        self.assertEqual(csv_zero_count, 0)
        self.assertEqual(error_count, 0)
        self.assertFalse(is_over_span)

