import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01020038RAKJ2151(FukushiSiteTestCaseBase):
    """TESTQAK01020038RAKJ2151"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_20038_RAKJ2151.sql", params=atena_list)
        super().setUp()

    def test_case_qak010_20038_RAKJ2151(self):
        """test_case_qak010_20038_RAKJ2151"""
        driver = None
        case_data = self.test_data[self.__class__.__name__]
        tekiyou_nendo = case_data.get("適用年度", "")
        hosoku_ym = case_data.get("捕捉年月", "")
        fukichougaitouki = case_data.get("普徴該当期", "")
        shori_ym = case_data.get("処理対象年月", "")
        insatsu_tyouhyou_name = case_data.get("印刷帳票名", "")
        # ログイン
        self.do_login()
        self.batch_kidou_click()

        self.form_input_by_id(idstr="GyomuSelect", text="高齢")
        self.form_input_by_id(idstr="JigyoSelect", text="後期高齢者医療")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="賦課業務_異動賦課")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="期割計算関連処理")

        if not self.check_batch_job_exist(insatsu_tyouhyou_name):
            return

        #「特別徴収対象者資格突合_異動賦課」の行の数字ボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name)
        self.screen_shot("20038_RAKJ2151-06")
        params = [
            {"title": "適用年度", "type": "select", "value": tekiyou_nendo},
            {"title": "捕捉年月", "type": "text", "value": hosoku_ym},
            {"title": "普徴該当期", "type": "text", "value": fukichougaitouki},
            {"title": "処理対象年月", "type": "text", "value": shori_ym}
        ]
        self.set_job_params(params)
        self.screen_shot("20038_RAKJ2151-07")

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("20038_RAKJ2151-09")

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("20038_RAKJ2151-12")
        
        # 「帳票履歴」ボタン押下
        self.click_report_log()

        # 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime, case_name="ケース名")
        self.screen_shot("20038_RAKJ2151-15")

        # 「戻る」ボタン押下
        self.return_click()

