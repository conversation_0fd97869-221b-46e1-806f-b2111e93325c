import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01020002RAKJ7071(FukushiSiteTestCaseBase):
    """TESTQAK01020002RAKJ7071"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_20002_RAKJ7071.sql", params=atena_list)
        super().setUp()

    def test_case_qak010_20002_RAKJ7071(self):
        """test_case_qak010_20002_RAKJ7071"""
        driver = None
        case_data = self.test_data[self.__class__.__name__]
        insatsu_tyouhyou_name = case_data.get("印刷帳票名", "")
        # ログイン
        self.do_login()
        self.batch_kidou_click()

        self.form_input_by_id(idstr="GyomuSelect", text="高齢")
        self.form_input_by_id(idstr="JigyoSelect", text="後期高齢者医療")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="収納業務_収納消込")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="収納消込")

        if not self.check_batch_job_exist(insatsu_tyouhyou_name):
            self.find_element(By.ID, "NextPageButton").click()
            if not self.check_batch_job_exist(insatsu_tyouhyou_name):
                return

        #「コンビニ_MPN速報残抽出」の行の数字ボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name)
        self.screen_shot("20002_RAKJ7071-06")

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("20002_RAKJ7071-08")

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 処理が終わるまで待機する(20秒間隔でジョブの実行履歴の検索ボタンを最大30回クリック(最大10分待つ))
        self.wait_job_finished(30,20)
        #状態が「正常終了」であることを確認
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("20002_RAKJ7071-11")

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()

