DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE WR$$JICHITAI_CODE$$QA..QAJサービス事業者マスタ            WHERE サービス事業者コード = '$$JIGYOSHANO$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJサービス事業者提供マスタ            WHERE サービス事業者コード = '$$JIGYOSHANO$$'
DELETE WR$$JICHITAI_CODE$$QA..QAJ指定医マスタ            WHERE 医療機関コード = '0000000001' AND カナ氏名 = 'ｶﾅ5' AND 漢字氏名 = '漢字５'


IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END