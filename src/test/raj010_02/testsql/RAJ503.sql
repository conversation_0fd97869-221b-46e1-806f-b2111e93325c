DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE WR$$JICHITAI_CODE$$QA..QAJ高額_障害児施設宛名情報            　　WHERE 宛名コード = '$$RAJ503_ATENA_CODE_1$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ医療的ケア判定スコア情報  　　　　　　  WHERE 宛名コード = '$$RAJ503_ATENA_CODE_1$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ放課後等デイサービス基本報酬区分指標管理  WHERE 宛名コード = '$$RAJ503_ATENA_CODE_1$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJ国保連過誤申立書情報                    WHERE 宛名コード = '$$RAJ503_ATENA_CODE_2$$'　and 業務コード='QAJ030'
UPDATE WR$$JICHITAI_CODE$$QA..QAJ国保連審査結果一覧情報                    SET 削除フラグ = '1' WHERE 宛名コード = '$$RAJ503_ATENA_CODE_2$$'　and 業務コード='QAJ030' and 事業所番号 = '1310000001' and サービス提供年月 = '201804'
UPDATE WR$$JICHITAI_CODE$$QA..QAJ支給実績基本                       SET 過誤申立書情報作成有無 = 0 WHERE 宛名コード = '$$RAJ503_ATENA_CODE_2$$'　and 業務コード='QAJ030'
DELETE WR$$JICHITAI_CODE$$QA..QAJサービス事業者マスタ                    WHERE サービス事業者コード='1050000001'
DELETE WR$$JICHITAI_CODE$$QA..QAJサービス事業者提供マスタ                    WHERE サービス事業者コード='1050000001'


IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END