import datetime
import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01070302(FukushiSiteTestCaseBase):
    """TestQAJ010_01070302"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01070302"]
        super().setUp()
    
    # 過誤申立情報をCSV出力できることを確認する。過誤申立書情報作成対象者一覧表を出力できることを確認する。
    def test_QAJ010_01070302(self):
        """過誤申立情報出力_者_"""
        
        case_data = self.test_data["TestQAJ010_01070302"]
        atena_code = case_data.get("atena_code", "")
        today = datetime.date.today()

        self.do_login()
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ起動」ボタン押下
        # 2024.9.2 拠点統括修正 start
        #self.find_element(By.ID,"CmdProcess4_1").click()
        self.click_button_by_label("バッチ起動")
        # 2024.9.2 拠点統括修正 end
        
        # 4 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_4")
        
        # 5 バッチ起動画面: 業務「障害」選択
        self.find_element(By.ID,"GyomuSelect").send_keys("障害")
        
        # 6 バッチ起動画面: 事業「障害者総合支援」選択
        self.select_by_id("JigyoSelect", text="障害者総合支援")
        
        # 7 バッチ起動画面: 処理区分「国保連連携　支払業務」選択
        self.select_by_id("ShoriKubunSelect", text="国保連連携　支払業務")

        # 8 バッチ起動画面: 処理分類「過誤申立書情報出力」選択
        self.select_by_id("ShoriBunruiSelect", text="過誤申立書情報出力")
        
        # 9 バッチ起動画面: No「１」　過誤申立書情報出力処理 　ボタン押下
        self.find_element(By.ID,"Sel1").click()
        
        # 10 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_10")
        
        # 11 バッチ起動画面: 「処理対象年月」入力「申立年月日開始」入力「申立年月日終了」入力
        self.form_input_by_id(idstr="item5", value=case_data.get("syoritaisyoYM",""))
        self.find_element(By.ID,"item8").send_keys("")
        self.find_element(By.ID,"item8").send_keys(case_data.get("startYM",""))
        self.find_element(By.ID,"item9").send_keys("")
        self.find_element(By.ID,"item9").send_keys(case_data.get("endYM",""))

        # 12 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        # 13 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_13")
        
        # 14 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        
        # 15 ジョブ実行履歴画面: 「検索」ボタン押下
        self.find_element(By.ID,"SearchButton").click()
        
        # 16 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_16")
        
        # 17 ジョブ実行履歴画面: No.1 過誤申立書情報出力処理の状態が「正常終了」となり、作成データ欄に「ダウンロード」ボタンが表示されたら押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.find_element(By.ID,"DownLoad1").click()

        # 18 ダウンロード画面: No.「１」ボタン押下
        self.find_element(By.ID,"No1").click()
        
        # 19 ダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        #self.click_button_by_label("ファイルを開く(O)")
        
        # 20 ダウンロード画面: 表示
        #self.screen_shot("ダウンロード画面_20")
        
        # 21 ダウンロード画面: ×ボタン押下でCSVを閉じる
        
        # 22 ダウンロード画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click()
        
        # 23 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_23")
        
        # 24 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.find_element(By.ID,"ExecListButton").click()
        
        # 25 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_25")
        
        # 26 バッチ実行履歴画面: 「処理一覧」ボタン押下
        self.find_element(By.ID,"SearchButton").click()
        
        # 27 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_27")
        
        # 28 バッチ起動画面: No「２」　過誤申立書情報作成対象者一覧表出力処理  　ボタン押下
        self.find_element(By.ID,"Sel2").click()
        
        # 29 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_29")
        
        # 30 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()
        
        # 31 バッチ起動画面 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_31")

        # 32 バッチ起動画面 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 33 ジョブ実行履歴画面 No「2」過誤申立書情報作成対象者一覧表出力処理の状態が「正常終了」となったらエビデンス取得
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("ジョブ実行履歴画面_33")

        # 34 ジョブ実行履歴画面 「検索」ボタン押下 
        self.find_element(By.ID,"SearchButton").click()

        # 35 ジョブ実行履歴画面 表示
        self.screen_shot("ジョブ実行履歴画面_35")

        # 36 ジョブ実行履歴画面 「帳票履歴」ボタン押下
        self.click_report_log()

        # 37 ジョブ帳票履歴画面 表示
        self.screen_shot("ジョブ帳票履歴画面_37")

        # 38 ジョブ帳票履歴画面 「検索」ボタン押下
        self.find_element(By.ID,"SearchButton").click()

        # 39 ジョブ帳票履歴画面 表示
        self.screen_shot("ジョブ帳票履歴画面_39")

        # 40 ジョブ帳票履歴画面 過誤申立書情報作成対象者一覧表出力処理「No1」ボタン押下
        self.find_element(By.ID,"Sel1").click()

        # 41 ジョブ帳票履歴画面　表示
        self.screen_shot("ジョブ帳票履歴画面_41")

        # 42 帳票（PDF）: 「ファイルを開く(O)」ボタンを押下
        
        # 43 帳票（PDF）: 表示
        
        # 44 帳票（PDF）: ×ボタン押下でPDFを閉じる
        
        # 45 ジョブ帳票履歴画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click()
        
        # 46 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_46")
        
        
