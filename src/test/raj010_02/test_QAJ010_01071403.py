import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAJ010_01071403(FukushiSiteTestCaseBase):
    """TestQAJ010_01071403"""

    def setUp(self):
        case_data = self.test_data["TestQAJ010_01071403"]
        super().setUp()
    
    # 審査会報酬費支払後処理を出力できることを確認する。
    def test_QAJ010_01071403(self):
        """審査会報酬費支払後処理"""
        
        case_data = self.test_data["TestQAJ010_01071403"]
        # atena_code = case_data.get("atena_code", "")
        # hakkoTxt = case_data.get("hakkoTxt", "")
        furikomiTxt = case_data.get("furikomiTxt", "")

        self.do_login()
        
        # 2 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_2")
        
        # 3 メインメニュー画面: 「バッチ起動」ボタン押下
        self.click_button_by_label("バッチ起動")
        
        # 4 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_4")
        
        # 5 バッチ起動画面: 業務「障害」選択事業「障害者総合支援」選択処理区分「月次処理」選択処理分類「審査報酬費支払処理」選択
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="障害者総合支援")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="月次処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="審査報酬費支払処理")
        self.screen_shot("バッチ起動画面_5")
        
        # 6 バッチ起動画面: 「審査会報酬費支払後処理」No3ボタン押下
        self.click_button_by_label("3")
        
        # 7 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_7")

        # 8 バッチ起動画面: パラメータ入力
        params = [
            {"title":"振込年月日", "type": "text", "value": furikomiTxt}
        ]
        self.set_job_params(params)
        self.form_input_by_id(idstr="UQZGC402_PrintSelect", text="ファイルアウト")
        self.form_input_by_id(idstr="UQZGC402_chkPrinter", value="0")

        # 9 バッチ起動画面: 「処理開始」ボタン押下
        exec_datetime = self.exec_batch_job()

        # 10 バッチ起動画面: 表示
        # Assert: メッセージエリアに「ジョブを起動しました 」と表示されていることを確認する。
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("バッチ起動画面_10")
        
        # 11 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        
        # 12 ジョブ実行履歴画面: 「検索」ボタン押下
        self.find_element(By.ID,"SearchButton").click()
        
        # 13 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_13")
        
        # 14 ジョブ実行履歴画面: No.1 過誤申立書情報出力処理の状態が「正常終了」となり、作成データ欄に「ダウンロード」ボタンが表示されたら押下
        # self.find_element(By.ID,"DownLoad1").click()

        # 15 ダウンロード画面: No.「１」ボタン押下
        # self.find_element(By.ID,"No1").click()
        
        # 16 ダウンロード画面: 「ファイルを開く(O)」ボタンを押下
        
        # 17 ダウンロード画面: 表示
        
        # 18 ダウンロード画面: ×ボタン押下でCSVを閉じる
        
        # 19 ダウンロード画面: 「戻る」ボタン押下
        self.find_element(By.ID,"GOBACK").click()
        
        # 20 メインメニュー画面: 表示
        self.screen_shot("ジョブ実行履歴画面_20")
