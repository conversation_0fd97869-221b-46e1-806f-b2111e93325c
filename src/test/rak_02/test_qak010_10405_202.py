import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01010405202(FukushiSiteTestCaseBase):
    """TESTQAK01010405202"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10405_202.sql", params=atena_list)
        super().setUp() 
    
    def test_case_qak010_10405_202(self):
        """test_case_qak010_10405_202"""

        case_data = self.test_data[self.__class__.__name__]
        kagonou_hasseibi_start = case_data.get("過誤納発生日開始", "")
        kagonou_hasseibi_end = case_data.get("過誤納発生日終了", "")
        seikyuu_ymd1 = case_data.get("請求日１", "")
        shiharai_ymd1 = case_data.get("支払(予定)日１", "")
        kanpu_ymd1 = case_data.get("還付年月日１", "")
        shiharai_houhou = case_data.get("支払方法", "")
        seikyuu_ymd2 = case_data.get("請求日２", "")
        shiharai_ymd2 = case_data.get("支払(予定)日２", "")
        kanpu_ymd2 = case_data.get("還付年月日２", "")
        # ログイン
        self.do_login()

        # メインメニュー画面 「過誤納整理」ボタン押下
        self.click_button_by_label("過誤納整理")
        # サブメニュー画面 「還付一括登録」ボタン押下
        self.click_button_by_label("還付一括登録")
        # 還付一括登録検索画面 過誤納発生日開始
        # 還付一括登録検索画面 過誤納発生日終了
        self.form_input_by_id(idstr="RdoSyorizumi", value=kagonou_hasseibi_start)
        self.form_input_by_id(idstr="RdoMishori", value=kagonou_hasseibi_end)

        # 還付一括登録検索画面 「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("10405-202-08")

        # 還付一括登録明細画面 請求日
        # 還付一括登録明細画面 支払(予定)日
        # 還付一括登録明細画面 還付年月日
        self.form_input_by_id(idstr="TxtSeikyuuYMD", value=seikyuu_ymd1)
        self.form_input_by_id(idstr="TxtShiharaiYMD", value=shiharai_ymd1)
        self.form_input_by_id(idstr="TxtKanpuYMD", value=kanpu_ymd1)
        self.screen_shot("10405-202-10")

        # 還付充当入力画面 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 還付充当入力画面 支払方法
        # 還付充当入力画面 請求日
        # 還付充当入力画面 支払(予定)日
        # 還付充当入力画面 還付年月日
        self.form_input_by_id(idstr="ShiharaiHouhouCmb", text=shiharai_houhou)
        self.form_input_by_id(idstr="TxtSeikyuuYMD", value=seikyuu_ymd2)
        self.form_input_by_id(idstr="TxtShiharaiYMD", value=shiharai_ymd2)
        self.form_input_by_id(idstr="TxtKanpuYMD", value=kanpu_ymd2)
        
        # 還付充当入力画面 「全件選択」ボタン押下

        # 還付充当入力画面 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()
        # 還付充当入力画面 「登録しました」のメッセージチェック
        self.assert_message_area("登録しました。")
        self.screen_shot("10405-202-15")

        # 還付充当入力画面 「戻る」ボタン押下
        self.return_click()
        # 過誤納金一覧画面 「戻る」ボタン押下
        self.return_click()
        # 過誤納付書検索画面 「戻る」ボタン押下
        self.return_click()
        # サブメニュー画面 「戻る」ボタン押下
        self.return_click()