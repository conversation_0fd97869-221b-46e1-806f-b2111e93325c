import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK010104051303(FukushiSiteTestCaseBase):
    """TESTQAK010104051303"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10405_1303.sql", params=atena_list)
        super().setUp() 
    
    def test_case_qak010_10405_1303(self):
        """test_case_qak010_10405_1303"""

        case_data = self.test_data[self.__class__.__name__]
        rdomishori = case_data.get("未処理", "")
        rdokanpu = case_data.get("還付お知らせ", "")
        rdosyorizumi = case_data.get("処理済", "")
        rdohoryu = case_data.get("特徴保留分", "")
        rdotokutyou = case_data.get("特徴還付分", "")
        rdojiko = case_data.get("時効分", "")
        rdoall = case_data.get("全て", "")
        # ログイン
        self.do_login()

        # メインメニュー画面 「過誤納整理」ボタン押下
        self.click_button_by_label("過誤納整理")
        # サブメニュー画面 「還付充当入力」ボタン押下
        self.click_button_by_label("還付充当入力")
        self.screen_shot("10405-1303-05")
        # 過誤納付書検索画面 検索条件１チェック
        self.form_input_by_id(idstr="RdoMishori", value=rdomishori)
        self.form_input_by_id(idstr="RdoKanpu", value=rdokanpu)
        self.form_input_by_id(idstr="RdoSyorizumi", value=rdosyorizumi)
        self.form_input_by_id(idstr="RdoHoryu", value=rdohoryu)
        self.form_input_by_id(idstr="RdoTokutyou", value=rdotokutyou)
        self.form_input_by_id(idstr="RdoJiko", value=rdojiko)
        self.form_input_by_id(idstr="RdoAll", value=rdoall)

        # 過誤納付書検索画面 「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("10405-1303-08")

        # 過誤納金一覧画面 入力予定者の「No.」ボタン押下
        self.click_button_by_label("2") 
        self.screen_shot("10405-1303-10")

        # 還付充当入力画面 「戻る」ボタン押下
        self.return_click()
        # 過誤納金一覧画面 「戻る」ボタン押下
        self.return_click()
        # 過誤納付書検索画面 「戻る」ボタン押下
        self.return_click()
        # サブメニュー画面 「戻る」ボタン押下
        self.return_click()