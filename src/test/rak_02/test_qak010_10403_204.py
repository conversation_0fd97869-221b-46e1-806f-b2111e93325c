import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase
import datetime

from selenium import webdriver
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK01010403204(FukushiSiteTestCaseBase):
    """TESTQAK01010403204"""

    def setUp(self):
        test_data = self.test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("test_qak010_10403_204.sql", params=atena_list)
        super().setUp()

    def test_case_qak010_10403_204(self):
        """test_case_qak010_10403_204"""
        driver = None
        case_data = self.test_data[self.__class__.__name__]
        atena_code = case_data.get("住民コード", "")
        
        # ログイン
        self.do_login()
        
        # 「収納情報管理」ボタン押下
        self.click_button_by_label("収納情報管理")

        # 「住民コード」入力
        self.form_input_by_id(idstr="AtenaCD", value=atena_code)

        # 「検索」ボタン押下
        self.click_button_by_label("検索")
        self.screen_shot("10403-204-06")

        # 「普徴収納情報欄のNo」ボタン押下
        self.click_by_id("Sel21")
        self.screen_shot("10403-204-08")

        # 「戻る」ボタン押下
        self.return_click()

        # 「戻る」ボタン押下
        self.return_click()

        # 「戻る」ボタン押下
        self.return_click()