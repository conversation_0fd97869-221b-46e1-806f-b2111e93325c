--QAP009実施前に実行する
--データが既に存在する場合は登録不要

-- INSERT WRG$$JICHITAI_CODE$$QA.[dbo].[QZDA宛名コード変換対象選択設定] ([自治体C], [DB名], [テーブル名], [チェック有無], [備考], [削除F], [登録日時], [更新日時], [登録職員ID], [更新職員ID], [更新プログラム名]) 
-- VALUES (N'$$JICHITAI_CODE$$', N'QA', N'QPDA世帯員就労', N'1', N'', N'0', CAST(N'2021-09-21 14:56:05.180' AS DateTime), CAST(N'2021-09-21 14:56:05.180' AS DateTime), N'', N'', N'               ')

--変更後データが既に存在する場合は削除して変更前で再登録する
--変更後宛名コード：99930006236

--変更前宛名コード：99930006235
--

-- INSERT WRG$$JICHITAI_CODE$$QA.[dbo].[QPDA世帯員就労] ([自治体C], [業務C], [世帯台帳番号], [福祉世帯員_宛名C], [認定日], [就労項番], [区C], [支所C], [勤務先_種別C], [勤務先_名称], [勤務先_住所直接入力F], [勤務先_住所], [勤務先_方書], [勤務先_業務内容], [勤務開始日], [勤務終了日], [勤務形態C], [勤務形態詳細], [就労_月], [就労_火], [就労_水], [就労_木], [就労_金], [就労_土], [就労_日], [就労_不定期F], [就労_週日数], [就労_月日数], [就労_週時間], [就労_月時間], [就労実績], [通勤時間], [通勤区分C], [通勤経路], [育児時間], [育児短時間], [育児短時間_開始], [育児短時間_終了], [産休育休取得状況_開始], [産休育休取得状況_終了], [勤務先備考], [汎用項目1], [汎用項目2], [汎用項目3], [汎用項目4], [削除F], [登録日時], [更新日時], [登録職員ID], [更新職員ID], [更新プログラム名]) 
-- VALUES (N'$$JICHITAI_CODE$$', N'QAP010', N'99999', N'99930006361', N'20140828', 1, N'00000', N'', N'1', N'勤務先名称', N'1', N'勤務先住所', N'', N'', N'00000000', N'99999999', N'1', N'', CAST(2.00 AS Decimal(4, 2)), CAST(2.00 AS Decimal(4, 2)), CAST(2.00 AS Decimal(4, 2)), CAST(2.00 AS Decimal(4, 2)), CAST(2.00 AS Decimal(4, 2)), CAST(2.00 AS Decimal(4, 2)), CAST(2.00 AS Decimal(4, 2)), N'0', 7, 28, CAST(0.00 AS Decimal(6, 2)), CAST(0.00 AS Decimal(8, 2)), 5, CAST(0.00 AS Decimal(4, 2)), N'1', N'', CAST(0.00 AS Decimal(4, 2)), CAST(0.00 AS Decimal(4, 2)), N'00000000', N'00000000', N'00000000', N'00000000', N'', N'', N'', N'', N'', N'0', CAST(N'2014-08-28 13:07:31.170' AS DateTime), CAST(N'2014-09-20 21:33:21.143' AS DateTime), N'1100', N'1100', N'QAPF100500     ')
