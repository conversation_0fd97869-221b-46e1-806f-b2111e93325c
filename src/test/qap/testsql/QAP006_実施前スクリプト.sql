--QAP006実施前スクリプト
USE WRG$$JICHITAI_CODE$$QA

-- DECLARE @児童_宛名C varchar(15)

-- SET @児童_宛名C = '240002'

-- SELECT *
-- FROM              QPDC主食費等実績
-- WHERE             (児童_宛名C = '240002')

-- SELECT *
-- FROM              QPDC児童減免
-- WHERE             (児童_宛名C = '240002')

-- SELECT *
-- FROM              QPDC児童減免_特別保育
-- WHERE             (児童_宛名C = '240002')

-- SELECT *
-- FROM              QPDC特別保育実績
-- WHERE             (児童_宛名C = '240002')

-- SELECT *
-- FROM              QPDC特別保育申請
-- WHERE             (児童_宛名C = '240002')



DELETE
FROM              QPDC主食費等実績
WHERE             (児童_宛名C = '$$QAP010_ATENACODE03$$')

DELETE
FROM              QPDC児童減免
WHERE             (児童_宛名C = '$$QAP010_ATENACODE03$$')

DELETE
FROM              QPDC児童減免_特別保育
WHERE             (児童_宛名C = '$$QAP010_ATENACODE03$$')

DELETE
FROM              QPDC特別保育実績
WHERE             (児童_宛名C = '$$QAP010_ATENACODE03$$')

DELETE
FROM              QPDC特別保育申請
WHERE             (児童_宛名C = '$$QAP010_ATENACODE03$$')

