import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

# 転用元シナリオ：TestQAG020_01100205
class TestQAG020_01100603(FukushiSiteTestCaseBase):
    """TestQAG020_01100603"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 変更申請した住民に対し決定登録ができることを確認する。
    def test_QAG020_01100603(self):
        """認定結果の登録（決定）"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        kettei_ymd = case_data.get("kettei_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG020")
        # 資格管理画面まで遷移
        self.click_by_id(idstr="CmdButton1_1")

        # 1 自立支援医療(精神通院)資格管理画面: 「進達入力」ボタン押下
        self.click_button_by_label("進達入力")

        # 2 自立支援医療(精神通院)資格管理画面: 進達年月日「20230701」入力 判定予定日「20230701」入力 判定予定時間「15:00」入力 進達番号「12345」入力
        self.form_input_by_id(idstr="TxtShintatsu1YMD", value=case_data.get("shintatsu_ymd", ""))
        self.form_input_by_id(idstr="TxtHanteiYMD", value=case_data.get("hantei_ymd", ""))
        self.form_input_by_id(idstr="TxtHanteiYotei_Ji", value="0")
        self.form_input_by_id(idstr="TxtHanteiYotei_Fun", value="00")
        self.form_input_by_id(idstr="TxtShintatsuBango", value="12345")
        self.form_input_by_id(idstr="TxtUketsukeBango", value="")
        self.form_input_by_id(idstr="TxtShinseiYMD", value=case_data.get("shintatsu_ymd", ""))

        # 3 自立支援医療(精神通院)資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        
        self.alert_ok()

        # self.click_button_by_label("資格管理")
        # 1 自立支援医療(精神通院)資格管理画面: 「決定内容入力」ボタン押下
        self.click_button_by_label("決定内容入力")

        # 2 自立支援医療(精神通院)資格管理画面: 表示
        self.screen_shot("自立支援医療(精神通院)資格管理画面_2")

        # 3 自立支援医療(精神通院)資格管理画面: 決定日「○○」入力決定結果「決定」選択
        
        self.form_input_by_id(idstr="TxtKetteiYMD", value=kettei_ymd)
        time.sleep(1)
        self.form_input_by_id(idstr="KetteiKekkaCmb", text="決定")
        self.form_input_by_id(idstr="TxtShinseiYMD", value=kettei_ymd)
        # 4 自立支援医療(精神通院)資格管理画面: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 5 自立支援医療(精神通院)資格管理画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(精神通院)資格管理画面_5")
