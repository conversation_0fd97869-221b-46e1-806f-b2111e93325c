import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG020_01100507(FukushiSiteTestCaseBase):
    """TestQAG020_01100507"""
    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code")
        params = {"DELETE_ATENA_CODE": atena_code}
        self.exec_sqlfile("RAG020_01100507.sql", params=params)
        super().setUp()

    # 年金照会の照会結果確認書を作成できることを確認する。
    def test_QAG020_01100507(self):
        """年金照会_照会結果_"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        address_code = case_data.get("address_code", "")

        self.do_login()
        # 1 メインメニュー画面: 「バッチ起動」ボタン押下
        self.batch_kidou_click()

        # 2 バッチ起動画面: 表示
        self.screen_shot("バッチ起動画面_2")

        # 3 バッチ起動画面: 業務：障害事業：自立支援医療(精神通院)処理区分：年金照会抽出処理分類：年金照会抽出
        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="自立支援医療(精神)")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="年金照会抽出")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="年金照会抽出（0000052000）")
        self.screen_shot("バッチ起動画面_3")

        # 4 バッチ起動画面: 「受給年金情報照会結果登録処理」のNoボタン押下
        self.click_batch_job_button_by_label("受給年金情報照会結果登録処理")

        # 5 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 6 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 7 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_7")

        # 8 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 9 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_9")

        # 10 ジョブ実行履歴画面: 作成データ 「ダウンロード」ボタン押下
        # self.get_job_output_files(case_name="ジョブ実行履歴画面")

        # 11 ジョブ実行履歴画面: 表示
        # self.screen_shot("ジョブ実行履歴画面_11")

        # 12 ジョブ実行履歴画面: 「年金一括照会対象者一覧.csv」のNoボタン押下
        # self.click_batch_job_button_by_label("年金一括照会対象者一覧.csv")

        # 13 ジョブ実行履歴画面: 「ファイルを開く」ボタン押下
        # self.click_button_by_label("ファイルを開く")

        # 14 帳票（CSV）: 表示
        # self.screen_shot("帳票（CSV）_14")

        # 15 帳票（CSV）: ×ボタン押下でCSVを閉じる

        # 16 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_16")

        # 17 ジョブ実行履歴画面: 「処理一覧」ボタン押下
        self.click_job_list()

        # 18 バッチ起動画面: 「処理一覧」ボタン押下
        self.click_job_exec_log_search()

        # 19 バッチ起動画面: 「受給年金照会結果確認書出力処理」のNoボタン押下
        self.click_batch_job_button_by_label("受給年金照会結果確認書出力処理")

        # 20 バッチ起動画面: 照会者宛名コード 「○○」入力
        # 21 バッチ起動画面: 「処理開始」ボタン押下
        # Assert: メッセージエリアに「ジョブを起動しました」と表示されていることを確認する。
        exec_datetime = self.exec_batch_job()
        self.assert_message_base_header("ジョブを起動しました")

        # 22 バッチ起動画面: 「実行履歴」ボタン押下
        self.click_job_exec_log()

        # 23 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_23")

        # 24 ジョブ実行履歴画面: 「検索」ボタン押下
        self.wait_job_finished(120,20)
        self.assert_job_normal_end(exec_datetime=exec_datetime)

        # 25 ジョブ実行履歴画面: 表示
        self.screen_shot("ジョブ実行履歴画面_25")

        # 26 ジョブ実行履歴画面: 「帳票履歴」ボタン押下
        self.click_report_log()

        # 27 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_27")

        # 28 ジョブ帳票履歴画面: 「検索」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 29 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_29")

        # 30 ジョブ帳票履歴画面: 「受給年金照会結果確認書」のNoボタン押下
        self.click_batch_job_button_by_label("受給年金照会結果確認書")

        # 31 ジョブ帳票履歴画面: 「ファイルを開く」ボタン押下
        self.get_job_report_pdf(exec_datetime=exec_datetime)

        # 32 帳票（PDF）: 表示
        self.screen_shot("帳票（PDF）_32")

        # 33 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 34 ジョブ帳票履歴画面: 表示
        self.screen_shot("ジョブ帳票履歴画面_34")
