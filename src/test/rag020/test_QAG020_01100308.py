import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

# 転用元シナリオ：TestQAG020_01100108
class TestQAG020_01100308(FukushiSiteTestCaseBase):
    """TestQAG020_01100308"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 年金照会結果を元に非課税年金収入を更新し、所得判定を行えることを確認する。
    def test_QAG020_01100308(self):
        """所得判定の実施"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAG020")
        self.click_by_id(idstr="CmdButton1_1")

        # 1 自立支援医療(精神通院)資格管理: 「修正」ボタン押下
        self.click_button_by_label("修正")

        # 2 自立支援医療(精神通院)資格管理: 「支給認定基準世帯作成」ボタン押下
        self.click_button_by_label("支給認定基準世帯作成")

        # 3 保険世帯情報画面: 表示
        self.screen_shot("保険世帯情報画面_3")

        # 4 保険世帯情報画面: 1人目（本人）手当・障害年金等「100000」入力
        # NG no ID
        self.form_input_by_id(idstr="TxtTokubetsuShogaiTeate_1", value="100000")

        # 5 保険世帯情報画面: 「収入計算」ボタン押下
        self.click_button_by_label("収入計算")

        # 6 保険世帯情報画面: 表示
        self.screen_shot("保険世帯情報画面_6")

        # 7 保険世帯情報画面: 「入力完了」ボタン押下
        self.click_button_by_label("入力完了")
        self.alert_ok()

        # 8 自立支援医療(精神通院)資格管理: 表示
        self.screen_shot("自立支援医療(精神通院)資格管理_8")

        # 9 自立支援医療(精神通院)資格管理: 「世帯所得反映」ボタン押下
        self.click_button_by_label("世帯所得反映")

        # 10 自立支援医療(精神通院)資格管理: 表示
        self.screen_shot("自立支援医療(精神通院)資格管理_10")

        # 11 自立支援医療(精神通院)資格管理: 「所得区分」ボタン押下
        self.click_button_by_label("所得区分")

        # 12 自立支援医療(精神通院)資格管理: 表示
        self.screen_shot("自立支援医療(精神通院)資格管理_12")

        # 13 自立支援医療(精神通院)資格管理: 「登録」ボタン押下
        self.click_button_by_label("登録")
        self.alert_ok()

        # 14 自立支援医療(精神通院)資格管理: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("自立支援医療(精神通院)資格管理_14")
