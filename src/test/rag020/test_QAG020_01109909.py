import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG020_01109909(FukushiSiteTestCaseBase):
    """TestQAG020_01109909"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        hokensya_code = case_data.get("hokensya_code")
        params = {"DELETE_IRYOUHOKENSYA_CODE": hokensya_code}
        self.exec_sqlfile("RAG020_01109909.sql", params=params)
        super().setUp()

    # 保険者情報をマスタ管理できることを確認する。
    def test_QAG020_01109909(self):
        """保険者マスタ"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})

        self.do_login()
        # 1 メインメニュー画面: 「マスタメンテナンス」ボタン押下
        self.master_maintenance_click()

        # 2 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_2")

        # 3 サブメニュー画面: 「保険者マスタメンテナンス」ボタン押下
        self.click_button_by_label("保険者マスタメンテナンス")

        # 4 保険者検索画面: 表示
        self.screen_shot("保険者検索画面_4")

        # 5 保険者検索画面: 漢字名称「○○」入力
        self.form_input_by_id(idstr="TxtKanjiName", value=case_data.get("kanji_name", ""))

        # 6 保険者検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 7 保険者検索画面: 表示
        self.screen_shot("保険者検索画面_7")

        # 8 保険者検索画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 9 保険者検索画面: 表示
        self.screen_shot("保険者検索画面_9")

        # 10 保険者入力画面: 保険者番号「99999999」を入力 | 保険種別「国保」を選択
        # 漢字名称「テスト保険者名称」を入力カナ名称「ﾃｽﾄﾎｹﾝｼｬ」を入力
        # 郵便番号1「123」を入力郵便番号2「4567」を入力
        # 漢字住所「テスト医療機関住所」を入力漢字方書「テスト医療機関方書」を入力
        # 電話番号「1111111111」を入力FAX番号「2222222222」を入力適用開始日「20240101」を入力
        self.form_input_by_id(idstr="TxtHokenNo", value="99999999")
        self.form_input_by_id(idstr="CmbHokenType", text="国保")
        self.form_input_by_id(idstr="TxtKanjiName", value="テスト保険者名称")
        self.form_input_by_id(idstr="TxtKanaName", value="ﾃｽﾄﾎｹﾝｼｬ")
        self.get_post_code()
        self.form_input_by_id(idstr="TxtKanjiKatagaki", value="テスト医療機関方書")
        self.form_input_by_id(idstr="TxtTel", value="1111111111")
        self.form_input_by_id(idstr="TxtFax", value="2222222222")
        self.form_input_by_id(idstr="TxtTekiyouKaishiYMD", value=case_data.get("tekiyou_kaishi_ymd", ""))

        # 11 保険者入力画面: 「登録」ボタン押下
        self.click_button_by_label("登録／復活")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # self.alert_ok()

        # 12 保険者入力画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("保険者入力画面_12")

        # 13 保険者入力画面: 「戻る」ボタン押下
        self.return_click()

        # 14 保険者検索画面: 表示
        self.screen_shot("保険者検索画面_14")

        # 15 保険者検索画面: 「初期表示」ボタン押下
        self.click_button_by_label("初期表示")

        # 16 保険者検索画面: 所在地区分「指定なし」選択 | 漢字名称「テスト医療機関名称」入力
        self.form_input_by_id(idstr="Shozaichi_1", value="1")
        self.form_input_by_id(idstr="TxtKanjiName", value="テスト保険者名称")

        # 17 保険者検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 18 保険者検索画面: 表示
        self.screen_shot("保険者検索画面_18")

        # 19 保険者検索画面: No1ボタン押下
        self.click_by_id("Sel1")

        # 20 保険者検索画面: 「削除」ボタン押下
        # Assert: メッセージエリアに「削除しました 」と表示されていることを確認する。
        self.click_button_by_label("削除")
        self.alert_ok()
        self.assert_message_area("削除しました。")

        # 21 保険者検索画面: 「戻る」ボタン押下
        self.return_click()

        # 22 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_22")

        # 23 サブメニュー画面: 「戻る」ボタン押下
        self.return_click()

        # 24 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_24")
