import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAG020_01109910(FukushiSiteTestCaseBase):
    """TestQAG020_01109910"""

    def setUp(self):
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        super().setUp()

    # 病名情報をマスタ管理できることを確認する。
    def test_QAG020_01109910(self):
        """病名マスタ"""

        case_data = self.common_test_data.get(self.__class__.__name__, {})

        self.do_login()
        # 1 メインメニュー画面: 「マスタメンテナンス」ボタン押下
        self.master_maintenance_click()

        # 2 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_2")

        # 3 サブメニュー画面: 「病名マスタメンテナンス」ボタン押下
        self.click_button_by_label("病名マスタメンテナンス")

        # 4 病名検索画面: 表示
        self.screen_shot("病名検索画面_4")

        # 5 病名検索画面: 業務「障害」選択
        self.form_input_by_id(idstr="CmbGyomu", text="障害")

        # 6 病名検索画面: 事業「自立支援医療(精神通院)」選択
        self.form_input_by_id(idstr="CmbJigyo", text="自立支援医療(精神)")

        # 7 病名検索画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 8 病名検索画面: 表示
        self.screen_shot("病名検索画面_8")

        # 9 病名検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 10 病名検索画面: 表示
        self.screen_shot("病名検索画面_10")

        # 11 病名検索画面: 「追加」ボタン押下
        self.click_button_by_label("追加")

        # 12 病名入力画面: 表示
        self.screen_shot("病名入力画面_12")

        # 13 病名入力画面: 「新規追加」ボタン押下
        self.click_button_by_label("新規追加")

        # 14 病名入力画面: 表示
        self.screen_shot("病名入力画面_14")

        # 15 病名入力画面: 病名コード「自動配番する 」をチェック有効期間「20230401」～「20240331」を入力カナ名称「ﾃｽﾄﾋﾞｮｳﾒｲ」を入力略称「テスト病名略称」を入力正式名称「テスト病名正式名称」を入力対象年齢「0」歳以上「254」歳以下を入力表示順序「999」を入力ICD10コード「G99」を入力
        self.form_input_by_id(idstr="ChkAutoBan", value="1")
        self.form_input_by_id(idstr="TxtYukoKStrYMD", value=case_data.get("yuko_k_str_ymd", ""))
        # self.form_input_by_id(idstr="TxtYukoKEndYMD", value=case_data.get("yuko_k_end_ymd", ""))
        self.form_input_by_id(idstr="TxtKanaName", value="ﾃｽﾄﾋﾞｮｳﾒｲ")
        self.form_input_by_id(idstr="TxtRyakusho", value="テスト病名略称")
        self.form_input_by_id(idstr="TxtSMeisho", value="テスト病名正式名称")
        self.form_input_by_id(idstr="TxtTNenreiKagen", value="0")
        self.form_input_by_id(idstr="TxtTNenreiJyogen", value="254")
        self.form_input_by_id(idstr="TxtHJyunjyo", value="999")
        self.form_input_by_id(idstr="TxtICDTenCode", value="G99")

        # 16 病名入力画面: 「登録／復活」ボタン押下
        self.click_button_by_label("登録／復活")
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        # self.alert_ok()

        # 17 病名入力画面: 表示
        # Assert: メッセージエリアに「登録しました 」と表示されていることを確認する。
        self.assert_message_area("登録しました")
        self.screen_shot("病名入力画面_17")

        # 18 病名入力画面: 「戻る」ボタン押下
        self.return_click()

        # 19 病名検索画面: 表示
        self.screen_shot("病名検索画面_19")

        # 20 病名検索画面: 業務「障害」選択
        self.form_input_by_id(idstr="CmbGyomu", text="障害")

        # 21 病名検索画面: 事業「精神手帳」選択
        self.form_input_by_id(idstr="CmbJigyo", text="自立支援医療(精神)")

        # 22 病名検索画面: 「確定」ボタン押下
        self.click_button_by_label("確定")

        # 23 病名検索画面: 表示
        self.screen_shot("病名検索画面_23")

        # 24 病名検索画面: 漢字名称「テスト病名正式名称」入力
        self.form_input_by_id(idstr="TxtSeishikiMeisho", value="テスト病名正式名称")

        # 25 病名検索画面: 「検索」ボタン押下
        self.click_button_by_label("検索")

        # 26 病名検索画面: 表示
        self.screen_shot("病名検索画面_26")

        # 27 病名検索画面: No1ボタン押下
        self.click_by_id("Sel1")

        # 28 病名入力画面: 表示
        self.screen_shot("病名入力画面_28")

        # 29 病名入力画面: 「削除」ボタン押下
        self.click_button_by_label("削除")
        self.alert_ok()

        # 30 病名入力画面: 表示
        # Assert: メッセージエリアに「削除しました 」と表示されていることを確認する。
        self.assert_message_area("削除しました")
        self.screen_shot("病名入力画面_30")

        # 31 病名入力画面: 「戻る」ボタン押下
        self.return_click()

        # 32 病名検索画面: 表示
        self.screen_shot("病名検索画面_32")

        # 33 病名検索画面: 「戻る」ボタン押下
        self.return_click()

        # 34 サブメニュー画面: 表示
        self.screen_shot("サブメニュー画面_34")

        # 35 サブメニュー画面: 「戻る」ボタン押下
        self.return_click()

        # 36 メインメニュー画面: 表示
        self.screen_shot("メインメニュー画面_36")
