import time
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TestQAD010(FukushiSiteTestCaseBase):
    """TESTQAD010005"""

    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAD010-005.sql", params=atena_list)
        super().setUp()
    
    def test_case_qad010_005(self):
        """test_case_qad010_005"""

        driver = None
        test_data = self.common_test_data
        #ログイン
        self.do_login()

        #メインメニュー・助成実績処理ボタン押下
        self.find_element(By.ID,"CmdProcess8_2").send_keys(Keys.ENTER)
        self.save_screenshot_migrate(driver, "QAD010-005-5-2" , True)

        #助成実績一覧・初期表示ボタン動確
        self.find_element(By.ID,"TxtIryokikan").click()
        self.find_element(By.ID,"TxtIryokikan").send_keys("")
        self.find_element(By.ID,"TxtIryokikan").send_keys("0000000001")
        self.find_element(By.ID,"span_CmdIryokikan").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-4" , True)
        self.find_element(By.ID,"span_CmdDefault").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-5" , True)

        #助成実績一覧・追加ボタン押下
        self.find_element(By.ID,"CmbJigyo").send_keys("障害者医療")
        time.sleep(2)
        self.find_element(By.ID,"CmdAdd").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-8" , True)

        #助成実績詳細・助成内容入力
        self.find_element(By.ID,"TxtShinseiYMD").click()
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("")
        self.find_element(By.ID,"TxtShinseiYMD").send_keys("20210701")
        self.find_element(By.ID,"TxtSeikyuYM").click()
        self.find_element(By.ID,"TxtSeikyuYM").send_keys("")
        self.find_element(By.ID,"TxtSeikyuYM").send_keys("202107")
        self.find_element(By.ID,"TxtShinryoYM").click()
        self.find_element(By.ID,"TxtShinryoYM").send_keys("")
        self.find_element(By.ID,"TxtShinryoYM").send_keys("202107")
        self.find_element(By.ID,"TxtJukyushaNo").click()

        self.find_element(By.ID,"TxtJukyushaNo").send_keys("")
        self.find_element(By.ID,"TxtJukyushaNo").send_keys(test_data.get("case_common_qad010_jukyusha_code5",""))

        self.find_element(By.ID,"CmbNyuinGairai").click()
        self.select_Option(driver,self.find_element(By.ID,"CmbNyuinGairai"),"外来")

        self.find_element(By.ID,"CmdJukyushaNo").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-13" , True)

        self.find_element(By.ID,"TxtIryoKikanCd").click()
        self.find_element(By.ID,"TxtIryoKikanCd").send_keys("")
        self.find_element(By.ID,"TxtIryoKikanCd").send_keys("0000000001")
        self.find_element(By.ID,"CmdIryoKikan").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-15" , True)

        self.find_element(By.ID,"TxtHokenshaNo").click()
        self.find_element(By.ID,"TxtHokenshaNo").send_keys("")
        self.find_element(By.ID,"TxtHokenshaNo").send_keys("00000002")
        self.find_element(By.ID,"CmdHokensha").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-17" , True)

        self.find_element(By.ID,"CmdJukyushaNo").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-18" , True)

        #助成実績詳細・口座情報ボタン押下
        self.find_element(By.ID,"CmdKouzaJh").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-20" , True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-22" , True)

        #助成実績詳細・保険情報ボタン押下
        self.find_element(By.ID,"CmdHoken").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-24" , True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-26" , True)

        #助成実績詳細・助成内容入力
        self.find_element(By.ID,"CmbNyuinGairai").send_keys("外来")
        self.find_element(By.ID,"CmbShinryoSyubetsu").send_keys("医科")
        time.sleep(2)
        self.find_element(By.ID,"TxtShinryoNissu").click()
        self.find_element(By.ID,"TxtShinryoNissu").send_keys("")
        self.find_element(By.ID,"TxtShinryoNissu").send_keys("3")
        self.find_element(By.ID,"TxtSouIryohi").click()
        self.find_element(By.ID,"TxtSouIryohi").send_keys("")
        self.find_element(By.ID,"TxtSouIryohi").send_keys("2000")
        self.find_element(By.ID,"CmdKeisan").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-31" , True)

        #助成実績詳細・最新口座ボタン押下
        self.find_element(By.ID,"CmdNewAccount").click()
        self.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        self.save_screenshot_migrate(driver, "QAD010-005-5-32" , True)

        #助成実績詳細・クリアボタン押下
        self.find_element(By.ID,"CmdClrAccount").click()
        self.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        self.save_screenshot_migrate(driver, "QAD010-005-5-33" , True)

        #助成実績詳細・銀行検索ボタン押下
        self.find_element(By.ID,"CmdBankSearch").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-35" , True)
        self.find_element(By.ID,"GOBACK").click()
        self.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        self.save_screenshot_migrate(driver, "QAD010-005-5-37" , True)

        #助成実績詳細・最新口座ボタン押下
        self.find_element(By.ID,"CmdNewAccount").click()
        self.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        self.save_screenshot_migrate(driver, "QAD010-005-5-38" , True)

        #助成実績詳細・振込額ボタン押下
        self.find_element(By.ID,"CmdFrikomi").click()
        self.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        self.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        self.save_screenshot_migrate(driver, "QAD010-005-5-39" , True)

        #助成実績詳細・支払日入力
        self.find_element(By.ID,"TxtShiharaiYMD").click()
        self.find_element(By.ID,"TxtShiharaiYMD").send_keys("")
        self.find_element(By.ID,"TxtShiharaiYMD").send_keys("********")

        #助成実績詳細・登録ボタン押下
        self.accept_next_alert = True
        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAD010-005-5-41" , True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-43" , True)

        #助成実績一覧・検索ボタン押下
        self.find_element(By.ID,"TxtTaishoYm").click()
        self.find_element(By.ID,"TxtTaishoYm").send_keys("")
        self.find_element(By.ID,"TxtTaishoYm").send_keys("202107")
        self.find_element(By.ID,"TxtJukyushaNo").click()
        self.find_element(By.ID,"TxtJukyushaNo").send_keys("")
        self.find_element(By.ID,"TxtJukyushaNo").send_keys(test_data.get("case_common_qad010_jukyusha_code5",""))
        self.find_element(By.ID,"CmdSearch").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-45" , True)

        #助成実績一覧・登録ボタン押下
        self.find_element(By.ID,"CmbShoriJokyo1").send_keys("修正")
        self.find_element(By.ID,"TxtShiharaiYmd1").click()
        self.find_element(By.ID,"TxtShiharaiYmd1").send_keys("")
        self.find_element(By.ID,"TxtShiharaiYmd1").send_keys("R3.8.3")
        self.accept_next_alert = True
        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAD010-005-5-48" , True)

        #助成実績一覧・Noボタン押下
        self.find_element(By.ID,"Sel1").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-50" , True)

        #助成実績登録・支払い解除
        self.find_element(By.ID,"CmdSKaijyo").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-51" , True)

        #助成実績登録・印刷
        # self.find_element(By.ID,"CmbInsatsu").send_keys("償還申請書")
        # self.accept_next_alert = True
        # self.find_element(By.ID,"span_CmdInsatsu").click()
        # self.assertEqual(u"印刷します。よろしいですか？", self.alert_ok())
        # self.save_screenshot_migrate(driver, "QAD010-005-5-53" , True)

        #助成実績登録・追加
        self.find_element(By.ID,"CmdTsuikaMode").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-54" , True)
        self.find_element(By.ID,"TxtSeikyuYM").click()
        self.find_element(By.ID,"TxtSeikyuYM").send_keys("")
        self.find_element(By.ID,"TxtSeikyuYM").send_keys("202108")
        self.accept_next_alert = True
        self.find_element(By.ID,"span_CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAD010-005-5-56" , True)

        #助成実績登録・初期表示ボタン動確
        self.find_element(By.ID,"CmdShokiHyoji").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-57" , True)

        #助成実績登録・削除ボタン押下
        self.accept_next_alert = True
        self.find_element(By.ID,"CmdSakujyo").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAD010-005-5-58" , True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-59", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAD010-005-5-60", True)
