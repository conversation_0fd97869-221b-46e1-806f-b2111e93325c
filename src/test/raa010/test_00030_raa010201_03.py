import time
import datetime
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01020103(FukushiSiteTestCaseBase):
    """TESTRAA01020103"""
    
    def test_case_raa010201_03(self):
        """test_case_raa010201_03"""

        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        shintatsu_ymd = case_data.get("shintatsu_ymd", "")
        shinsei_ymd_str = case_data.get("shinsei_ymd_str", "")
        shinsei_ymd_end = case_data.get("shinsei_ymd_end", "")
        shintatsusaki = case_data.get("shintat<PERSON><PERSON>", "")
        shintatsu_ymd_str = case_data.get("shintatsu_ymd_str", "")
        shintatsu_ymd_end = case_data.get("shintatsu_ymd_end", "")
        bunsho_no = case_data.get("bunsho_no", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        # ログイン
        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA010")
        self.return_click()
        self.return_click()
        self.return_click()
        self.screen_shot("raa010201_03_6_メインメニュー画面")

        # 進達処理
        self.click_button_by_label("進達処理")
        self.form_input_by_id(idstr="Gyomu", text="障害")
        self.form_input_by_id(idstr="Jigyo", text="身体障害者手帳")
        self.screen_shot("raa010201_03_8_進達処理画面")

        # 「確定」ボタン押下
        self.click_button_by_label("確定")
        self.screen_shot("raa010201_03_10_「確定」ボタン押下")
        self.form_input_by_id(idstr="ShinseiKaishiYMD", value=shinsei_ymd_str)
        self.form_input_by_id(idstr="ShinseiOwariYMD", value=shinsei_ymd_end)
        self.click_button_by_label("検索")
        self.screen_shot("raa010201_03_12_「検索」ボタン押下")

        self.form_input_by_id(idstr="ShintatsuYMD1", value=shintatsu_ymd)
        self.form_input_by_id(idstr="CmbShintatsusaki1", text=shintatsusaki)
        self.form_input_by_id(idstr="ChkSentakuNo_1", value="1")
        self.click_button_by_label("進達情報")
        self.screen_shot("raa010201_03_16_「進達情報」ボタン押下")

        # 「登録」ボタン押下     
        self.pdf_output_and_download(button_id="TourokuBtn", case_name="ケース名")
        self.assert_message_area("登録しました。")
        self.screen_shot("raa010201_03_18_「登録」ボタン押下")
        
        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("raa010201_03_24_「戻る」ボタン押下")
        self.return_click()
        self.screen_shot("raa010201_03_26_「戻る」ボタン押下")

        # 「バッチ起動」ボタン押下
        self.batch_kidou_click()
        self.screen_shot("raa010201_03_28_「バッチ起動」ボタン押下")

        self.form_input_by_id(idstr="GyomuSelect", text="障害")
        self.form_input_by_id(idstr="JigyoSelect", text="身体障害者手帳")
        self.form_input_by_id(idstr="ShoriKubunSelect", text="随時処理")
        self.form_input_by_id(idstr="ShoriBunruiSelect", text="進達一覧作成")
        self.screen_shot("raa010201_03_33_バッチ起動画面" )

        # 「身体障害者手帳交付申請者一覧_進達_再出力処理」の行の数字ボタン押下
        self.click_batch_job_button_by_label(insatsu_tyouhyou_name)
        self.screen_shot("raa010201_03_35_「身体障害者手帳交付申請者一覧_進達_再出力処理」選択")
        params = [
            {"title": "進達年月日開始", "type": "text", "value": shintatsu_ymd_str},
            {"title": "進達年月日終了", "type": "text", "value": shintatsu_ymd_end},
            {"title": "文書番号", "type": "text", "value": bunsho_no}
        ]
        self.set_job_params(params)
        self.screen_shot("raa010201_03_36_パラメータ入力")

        # 「処理開始」ボタン押下 (実行した日時を保存しておく)
        exec_datetime = self.exec_batch_job()
        # 「処理開始」ボタン押下からジョブの起動まで3秒以上かかる場合があるので1分マイナス
        exec_datetime = exec_datetime - datetime.timedelta(minutes=1)

        # 基盤メッセージのアサート
        self.assert_message_base_header("ジョブを起動しました")
        self.screen_shot("raa010201_03_39_ジョブ起動")

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 「実行履歴」ボタン押下
        self.click_job_exec_log()
        self.screen_shot("raa010201_03_42_「実行履歴」ボタン押下" )

        # 「検索」ボタン押下
        self.assert_job_normal_end(exec_datetime=exec_datetime)
        self.screen_shot("raa010201_03_44_「検索」ボタン押下" )

        # 「戻る」ボタン押下
        self.return_click()
        self.screen_shot("raa010201_03_46_「戻る」ボタン押下" )
