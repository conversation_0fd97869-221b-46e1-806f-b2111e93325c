
DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ受給状況 WHERE 業務コード = 'QAA010' AND 宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAZ福祉世帯 WHERE 業務コード = 'QAA010' AND 本人宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA資格履歴 WHERE 業務コード = 'QAA010' AND 宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳資格内容 WHERE 業務コード = 'QAA010' AND 宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳障害認定 WHERE 業務コード = 'QAA010' AND 宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳障害区分 WHERE 業務コード = 'QAA010' AND 宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳障害原因_状況 WHERE 業務コード = 'QAA010' AND 宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳審査資格内容		 WHERE 業務コード = 'QAA010' AND 宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳審査障害認定		 WHERE 業務コード = 'QAA010' AND 宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳審査障害区分		 WHERE 業務コード = 'QAA010' AND 宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳審査障害原因_状況	 WHERE 業務コード = 'QAA010' AND 宛名コード = '$$ATENA_QAA010_TENSHUTSU$$'

DELETE FROM WR$$JICHITAI_CODE$$QA..QAA資格履歴 WHERE 業務コード = 'QAA010' AND 履歴番号 BETWEEN 2159 AND 2160
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳資格内容 WHERE 業務コード = 'QAA010' AND 履歴番号 BETWEEN 2159 AND 2160
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳障害認定 WHERE 業務コード = 'QAA010' AND 履歴番号 BETWEEN 2159 AND 2160
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳障害区分 WHERE 業務コード = 'QAA010' AND 履歴番号 BETWEEN 2159 AND 2160
DELETE FROM WR$$JICHITAI_CODE$$QA..QAA身障手帳障害原因_状況 WHERE 業務コード = 'QAA010' AND 履歴番号 BETWEEN 2159 AND 2160

--QAZ受給状況
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ受給状況 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,資格取得日,資格喪失日,申請年月日,申請種別,申請理由,進達年月日1,進達判定年月日1,進達結果1,進達年月日2,進達判定年月日2,進達結果2,決定年月日,決定結果,決定理由,進捗状況,業務固有コード1,業務固有コード2,業務固有コード3,複数申請フラグ,職権フラグ,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA010',2159,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA010_TENSHUTSU$$',N'20230801',N'99999999',N'20230801',2,99,N'20230801',N'00000000',0,N'00000000',N'00000000',0,N'20230801',1,0,N'0000600010',N'札幌市白12345',N'',N'',0,N'0',N'0',N'9501',N'9501','2024-05-08 16:29:25.7','2024-05-08 16:45:03.15',N'RAAF001 ');

--QAZ福祉世帯
INSERT INTO WR$$JICHITAI_CODE$$QA..QAZ福祉世帯 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,本人宛名コード,福祉世帯員宛名コード,該当日,非該当日,本人から見た続柄,受給者との関係,汎用項目,同居別居コード,旧姓併記有無,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA010',2159,0,N'$$JICHITAI_CODE$$',N'00000',N'',N'$$ATENA_QAA010_TENSHUTSU$$',N'$$ATENA_QAA010_TENSHUTSU$$',N'20230801',N'99999999',N'0000000001',N'0000000001',N'0000000000',N'0000000000',N'0',N'0',N'9501',N'9501','2024-05-08 16:29:25.653','2024-05-08 16:29:25.653',N'RAAF001 ');

--QAA資格履歴
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA資格履歴 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,申請年月日,申請種別,申請理由,申請理由2,申請理由3,申請理由テキスト,申請内容入力日,進達年月日1,進達判定年月日1,進達結果1,進達内容入力日1,進達先機関コード1,進達年月日2,進達判定年月日2,進達結果2,進達内容入力日2,進達先機関コード2,決定年月日,決定結果,決定理由,決定理由テキスト,決定内容入力日,業務固有コード1,業務固有コード2,業務固有コード3,職権フラグ,受付場所コード,担当場所コード,変更日,資格状態コード,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA010',2159,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA010_TENSHUTSU$$',N'20230801',2,99,0,0,N'',N'20240508',N'20230801',N'00000000',0,N'20240508',N'0000000000',N'00000000',N'00000000',0,N'00000000',N'0000000000',N'20230801',1,0,N'',N'20240508',N'札幌市白12345',N'',N'',N'0',N'0000000000',N'0000000000',N'00000000',N'0000000040',N'0',N'9501',N'9501','2024-05-08 16:29:25.637','2024-05-08 16:41:07.943',N'RAAS004 ');

--QAA身障手帳資格内容
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA身障手帳資格内容 (業務コード,履歴番号,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,手帳記号,手帳番号,種別,総合等級,代表障害コード,手帳障害名,事由発生日,交付年月日,再交付年月日,手帳受領日,返還年月日,本籍,旧手帳記号,旧手帳番号,手帳郵便番号1,手帳郵便番号2,手帳住所1,手帳住所2,手帳カナ氏名,手帳漢字氏名,進達区分,情報更新日,進達データ作成日,割引有無1,割引有無2,割引有無3,割引有無4,割引有無5,割引有無6,希望手帳様式コード,手帳交付方法コード,手帳交付場所コード,自立支援医療_更生医療の同時申請有無,再認定督促期限,宛先優先度コード,申請時の主たる障害部位コード,事務担当者,依頼日,報告日,回答日,決裁日,医師意見との相違有無,障害名_カード型,指導記録,原傷病名,高確法50条2該当有無,初回交付場所,カード登録日,カード解除日,カード発行日,通知発送日,手帳引渡日,NHK受信料減免有無,NHK受信料減免お客様番号,有料道路減免有無,備考,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA010',2159,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA010_TENSHUTSU$$',N'0000001104',N'12345',N'0000000002',N'0000000003',N'0000000006',N'網膜色素変性症（３級）',N'00000000',N'20230801',N'00000000',N'00000000',N'00000000',N'0000000000',N'0000000000',N'',N'000',N'0000',N'',N'',N'',N'',N'0',N'00000000',N'00000000',N'0',N'0',N'0',N'0',N'0',N'0',N'0000000001',N'0000000001',N'0000000000',N'0',N'00000000',N'0000000000',N'0         ',N'',N'00000000',N'00000000',N'00000000',N'00000000',N'0',N'',N'手帳番号：札幌市白　第12345号　障害種別：２種　総合等級：３級　申請事由：転入',N'',N'0',N'',N'00000000',N'00000000',N'00000000',N'00000000',N'00000000',N'0',N'',N'0',N'',N'0',N'9501',N'9501','2024-05-08 16:29:25.637','2024-05-08 16:41:07.973',N'RAAS004 ');

--QAA身障手帳障害認定
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA身障手帳障害認定 (業務コード,履歴番号,枝番,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,障害認定日,表示順序,障害名,裸眼_右_視力,裸眼_左_視力,矯正_右_視力,矯正_左_視力,全盲,右_聴力,左_聴力,右_語音明瞭度,右_語音明瞭度_dB,左_語音明瞭度,左_語音明瞭度_dB,障害原因,非表示フラグ,右_肢体不自由コード,左_肢体不自由コード,診断書作成医師,照会年月日,回答年月日,医療機関コード,医療機関名称,医師コード,審査内容,再認定理由,再認定時期,右_弁別区分コード,左_弁別区分コード,診療科目コード,診断日,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA010',2159,0,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA010_TENSHUTSU$$',N'20230801',0,N'網膜色素変性症',N'',N'',N'',N'',N'0',N'',N'',N'',N'',N'',N'',N'0000000000',N'0',N'',N'',N'',N'00000000',N'00000000',N'0000000000',N'',N'0000000000',N'',N'0000000000',N'0000000000',N'0000000000',N'0000000000',N'0000000000',N'00000000',N'0',N'9501',N'9501','2024-05-08 16:41:08.007','2024-05-08 16:41:08.007',N'RAAS004 ');

--QAA身障手帳障害区分
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA身障手帳障害区分 (業務コード,履歴番号,枝番,小枝番,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,表示順序,障害コード,障害等級,法別表,有期認定期限,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA010',2159,0,0,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA010_TENSHUTSU$$',0,N'0000000006',N'0000000003',N'0000000000',N'000000',N'0',N'9501',N'9501','2024-05-08 16:41:08.037','2024-05-08 16:41:08.037',N'RAAS004 ');

--QAA身障手帳障害原因_状況
INSERT INTO WR$$JICHITAI_CODE$$QA..QAA身障手帳障害原因_状況 (業務コード,履歴番号,枝番,小枝番,履歴分類,自治体コード,福祉事務所コード,支所コード,宛名コード,原因_状況区分,対応障害コード区分,原因_状況コード,修飾語コード,削除フラグ,データ作成担当者,データ更新担当者,データ作成日時,データ更新日時,データ更新プログラム) VALUES
	 (N'QAA010',2159,0,0,0,N'$$JICHITAI_CODE$$',N'99301',N'',N'$$ATENA_QAA010_TENSHUTSU$$',N'1',N'0000000000',N'0000000101',N'0000000000',N'0',N'9501',N'9501','2024-05-08 16:41:08.053','2024-05-08 16:41:08.053',N'RAAS004 ');
