import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQGK002002(FukushiSiteTestCaseBase):
    """TESTQGK002002"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        super().setUp()
    
    def test_case_qgk002_002(self):
        """test_case_qgk002_002"""
        driver = None
        test_data = self.common_test_data
        
        self.do_login()
        #資格管理ボタン押下

        self.find_element(By.ID,"CmdProcess37_2").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-2", True)
        self.find_element(By.ID,"CmdButton6").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-4", True)
        self.find_element(By.ID,u"CmbNinteiSyubetsu").send_keys("就学援助")

        self.find_element(By.ID,"span_BtnKakutei").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-7", True)
        self.find_element(By.ID,"BtnShoki").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-9", True)
        self.find_element(By.ID,u"CmbNinteiSyubetsu").send_keys("就学援助")

        self.find_element(By.ID,"span_BtnKakutei").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-12", True)
        self.find_element(By.ID,"span_BtnSchoolSearch").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-14", True)
        self.find_element(By.ID,"span_CmdKenSaku").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-16", True)
        self.find_element(By.ID,"sel_no1").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-18", True)
        self.find_element(By.ID,"BtnClear").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-20", True)
        self.find_element(By.ID,"BtnSearch").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-22", True)
        self.find_element(By.ID,"sel_no1").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-24", True)
        
        self.find_element(By.ID,"GOBACK").click()
        self.assertEqual(u"内容は変更されています。変更した内容を破棄しますか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QGK002-002-1-27", True)
        self.find_element(By.ID,"BtnAdd").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-29", True)
        self.find_element(By.ID,u"CmbEnjyohimokuBunrui").send_keys("学用品通学用品")

        self.find_element(By.ID,"ChkTsuujyo").click()
        self.find_element(By.ID,u"TxtEnjyohimoku").send_keys("援助費目テスト")

        self.find_element(By.ID,u"TxtEnjyohimokuRyakusho").send_keys("援助費目テスト略称")
    
        self.find_element(By.ID,u"CmbEnjyohimokuShiwake").send_keys("教科用図書購入費")

        self.find_element(By.ID,u"CmbShikyuTani").send_keys("児童・生徒単位")

        self.find_element(By.ID,u"CmbKihonShiharaiHouhou").send_keys("口座")

        self.find_element(By.ID,"TxtShiteiShiharai4").send_keys("4")
        self.find_element(By.ID,"TxtShiteiShiharai5").send_keys("5")
        self.find_element(By.ID,"TxtShiteiShiharai6").send_keys("6")
        self.find_element(By.ID,"TxtShiteiShiharai7").send_keys("7")
        self.find_element(By.ID,"TxtShiteiShiharai8").send_keys("8")
        self.find_element(By.ID,"TxtShiteiShiharai9").send_keys("9")
        self.find_element(By.ID,"TxtShiteiShiharai10").send_keys("10")
        self.find_element(By.ID,"TxtShiteiShiharai11").send_keys("11")
        self.find_element(By.ID,"TxtShiteiShiharai12").send_keys("12")
        self.find_element(By.ID,"TxtShiteiShiharai1").send_keys("1")
        self.find_element(By.ID,"TxtShiteiShiharai2").send_keys("2")
        self.find_element(By.ID,"TxtShiteiShiharai3").send_keys("3")
        self.save_screenshot_migrate(driver, "QGK002-002-1-37", True)
        self.find_element(By.ID,"span_BtnTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QGK002-002-1-40", True)
        self.find_element(By.ID,"BtnSchoolSearch").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-42", True)
        self.find_element(By.ID,"CmdKenSaku").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-44", True)
        self.find_element(By.ID,"sel_no1").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-46", True)
        self.find_element(By.ID,"BtnClear").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-48", True)
        self.find_element(By.ID,"BtnSearch").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-50", True)
        self.find_element(By.ID,"span_BtnAdd").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-52", True)
        self.find_element(By.ID,"TxtSchoolCode1").send_keys(Keys.CONTROL + "a")
        self.find_element(By.ID,"TxtSchoolCode1").send_keys(Keys.DELETE)
        self.save_screenshot_migrate(driver, "QGK002-002-1-54", True)
        self.find_element(By.ID,"span_BtnSchoolSearch1").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-56", True)
        self.find_element(By.ID,"CmdKenSaku").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-58", True)
        self.find_element(By.ID,"sel_no1").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-60", True)
        self.find_element(By.ID,"TxtKijyungaku4_1").send_keys("10000")
        self.find_element(By.ID,"TxtGendogaku4_1").send_keys("10000")
        self.save_screenshot_migrate(driver, "QGK002-002-1-62", True)
        self.find_element(By.ID,"span_BtnTeigaku1").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-64", True)
        self.find_element(By.ID,"span_BtnTeiritsu1").click()
        self.save_screenshot_migrate(driver, "QGK002-002-1-66", True)
        self.find_element(By.ID,"span_BtnTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QGK002-002-1-68", True)
        self.find_element(By.ID,"CmbShoriStatus1").send_keys("削除")
        self.find_element(By.ID,"span_BtnTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.find_element(By.ID,"BtnDelete").click()
        self.assertEqual(u"削除します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QGK002-002-1-71", True)