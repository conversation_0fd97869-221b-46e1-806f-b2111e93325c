import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTRAA01040522(FukushiSiteTestCaseBase):
    """TESTRAA01040522"""

    def test_case_raa010405_22(self):
        """test_case_raa010405_22"""

        driver = None
        case_data = self.common_test_data.get(self.__class__.__name__, {})
        atena_code = case_data.get("atena_code", "")
        insatsu_tyouhyou_name = case_data.get("insatsu_tyouhyou_name", "")
        hakkou_ymd = case_data.get("hakkou_ymd", "")

        self.goto_jukyu_joukyou_and_click_gyoumu(atena_code=atena_code, gyoumu_code="QAA040")
        # 印刷
        self.click_button_by_label("印刷")
        self.save_screenshot_migrate(driver, "raa010405_22-02", True)

        # 「障害者手帳交付決定通知書」の行の数字ボタン押下
        report_param_list = [
            {
                "report_name": insatsu_tyouhyou_name,
                "params": [
                    {"title": "発行年月日", "type": "text", "value": hakkou_ymd}
                ]
            }
        ]
        self.print_online_reports(case_name="ケース名", report_param_list=report_param_list)
        # 「ファイルを開く(O)」ボタンを押下 メッセージエリアに「プレビューを表示しました 」と表示されていることを確認する。
        self.assert_message_area("プレビューを表示しました")
        self.save_screenshot_migrate(driver, "raa010405_22-06", True)

        # 帳票（PDF）表示

        # 帳票（PDF）: ×ボタン押下でPDFを閉じる

        # 帳票印刷画面: 「戻る」ボタン押下
        self.return_click()
        self.save_screenshot_migrate(driver, "raa010405_22-09", True)
        # 受給状況参照画面
        self.return_click()
        self.save_screenshot_migrate(driver, "raa010405_22-11", True)
        # 個人検索画面
        self.return_click()
        self.save_screenshot_migrate(driver, "raa010405_22-13", True)
        # メインメニュー画面
        self.return_click()
        self.save_screenshot_migrate(driver, "raa010405_22-15", True)
