import time
import unittest
from base.fukushi_case import FukushiSiteTestCaseBase

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import Select

class TESTQAK009001(FukushiSiteTestCaseBase):
    """TESTQAK009001"""
    
    # 各テストメソッドの実行前に実行したいもの
    def setUp(self):
        test_data = self.common_test_data
        atena_list = test_data.get("sql_params", {})
        self.exec_sqlfile("QAK009.sql", params=atena_list)
        super().setUp()
    
    def test_case_qak009_001(self):
        """test_case_qak009_001"""
        driver = None
        test_data = self.common_test_data
        self.do_login()
        self.find_element(By.ID,"CmdProcess16_5").click()
        self.save_screenshot_migrate(driver, "QAK009-001-02", True)
        
        # self.find_element(By.ID,"FukaNendoCbo").click()
        # self.select_Option(driver,self.find_element(By.ID,"FukaNendoCbo"),"令和04年")
        self.find_element(By.ID,"FukaNendoCbo").send_keys("令和04年")
        # time.sleep(2)
        
        # self.find_element(By.ID,"TyosyuHouhouCbo").click()
        # self.select_Option(driver,self.find_element(By.ID,"TyosyuHouhouCbo"),"普通徴収")
        self.find_element(By.ID,"TyosyuHouhouCbo").send_keys("普通徴収")
        # time.sleep(2)
        
        # self.find_element(By.ID,"GenKaKubunCbo").click()
        # self.select_Option(driver,self.find_element(By.ID,"GenKaKubunCbo"),"現年")
        self.find_element(By.ID,"GenKaKubunCbo").send_keys("現年")
        # time.sleep(2)
        self.find_element(By.ID,"CmdKensaku").click()
        self.save_screenshot_migrate(driver, "QAK009-001-06", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK009//" + "QAK009-001-06-2" +".png")
        self.find_element(By.ID,"CmdKousin").click()
        self.save_screenshot_migrate(driver, "QAK009-001-07", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK009//" + "QAK009-001-07-2" +".png")
        self.find_element(By.ID,"span_Rireki1").click()
        self.find_element(By.ID,"TxtNoukigen").click()
        self.find_element(By.ID,"TxtNoukigen").send_keys("")
        # self.find_element(By.ID,"TxtNoukigen").send_keys("R04.09.13")
        # 小さい順番から並ぶ制限がある為
        self.find_element(By.ID,"TxtNoukigen").send_keys("H20.09.10")
        self.find_element(By.ID,"span_CmdKanryou").click()
        self.save_screenshot_migrate(driver, "QAK009-001-10", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK009//" + "QAK009-001-10-2" +".png")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK009-001-11", True)
        self.find_element(By.ID,"CmdKousin").click()
        self.save_screenshot_migrate(driver, "QAK009-001-13", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK009//" + "QAK009-001-13-2" +".png")
        self.find_element(By.ID,"span_CmdCancel").click()
        self.save_screenshot_migrate(driver, "QAK009-001-14", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK009//" + "QAK009-001-14-2" +".png")
        self.find_element(By.ID,"span_CmdShoki").click()
        self.save_screenshot_migrate(driver, "QAK009-001-15", True)
        
        self.find_element(By.ID,"FukaNendoCbo").click()
        self.select_Option(driver,self.find_element(By.ID,"FukaNendoCbo"),"令和05年")
        self.find_element(By.ID,"FukaNendoCbo").send_keys("令和05年")
        # time.sleep(2)
        
        # self.find_element(By.ID,"TyosyuHouhouCbo").click()
        # self.select_Option(driver,self.find_element(By.ID,"TyosyuHouhouCbo"),"普通徴収")
        self.find_element(By.ID,"TyosyuHouhouCbo").send_keys("普通徴収")
        # time.sleep(2)
        
        # self.find_element(By.ID,"GenKaKubunCbo").click()
        # self.select_Option(driver,self.find_element(By.ID,"GenKaKubunCbo"),"現年")
        self.find_element(By.ID,"GenKaKubunCbo").send_keys("現年")
        # time.sleep(2)
        self.find_element(By.ID,"CmdKensaku").click()
        self.save_screenshot_migrate(driver, "QAK009-001-15(2)", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK009//" + "QAK009-001-15(2)-2" +".png")
        self.find_element(By.ID,"CmdKousin").click()
        self.save_screenshot_migrate(driver, "QAK009-001-16", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK009//" + "QAK009-001-16-2" +".png")
        self.find_element(By.ID,"Rireki1").click()
        self.find_element(By.ID,"TxtNoukigen").click()
        self.find_element(By.ID,"TxtNoukigen").send_keys("")
        # self.find_element(By.ID,"TxtNoukigen").send_keys("R05.04.25")
        self.find_element(By.ID,"TxtNoukigen").send_keys("R05.04.25")
        self.find_element(By.ID,"span_CmdKanryou").click()
        self.save_screenshot_migrate(driver, "QAK009-001-19", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK009//" + "QAK009-001-19-2" +".png")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK009-001-20", True)
        
        self.find_element(By.ID,"span_CmdCopy").click()
        self.assertEqual(u"前年度のデータをコピーします。入力されたデータは破棄されますがよろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK009-001-23", True)
        # driver.find_element_by_tag_name("body").send_keys(Keys.PAGE_DOWN)
        # driver.save_screenshot("..//evidence//" + "QAK009//" + "QAK009-001-23-2" +".png")
        
        self.find_element(By.ID,"CmdTouroku").click()
        self.assertEqual(u"更新します。よろしいですか？", self.alert_ok())
        self.save_screenshot_migrate(driver, "QAK009-001-26", True)

        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAK009-001-27", True)
        self.find_element(By.ID,"GOBACK").click()
        self.save_screenshot_migrate(driver, "QAK009-001-28", True)
    
        