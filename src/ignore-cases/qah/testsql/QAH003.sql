DECLARE @削除実行フラグ INT
SET @削除実行フラグ = 1 -- 削除するとき 1 に書き換える

BEGIN TRAN

DELETE FROM WR$$JICHITAI_CODE$$QA..QAH通知履歴情報				where 宛名コード = '00080003' and 通知履歴番号 = '1'

IF @削除実行フラグ <> 1
BEGIN
	ROLLBACK
END
ELSE
BEGIN
	COMMIT
END


insert into WR$$JICHITAI_CODE$$QA..QAH通知履歴情報 values('$$JICHITAI_CODE$$','QAH101','01','1','QAH100','00000','','','','','2021','00080003','QAHJ9002','1','QAHB9002','20210401','00000000','00000000','','','','0','0','9501','9501',getdate(),getdate(),'QAHG_CUD')
