import argparse
import os
import unittest
import logging
import datetime
from base.runner import WebRingsSiteTestRunner
from report.html_report import HtmlReportHandler
from report.xlsx_report import XlsxReportHandler
from util.config import CommonSettings

root_dir = os.path.abspath(os.path.join(os.path.abspath(os.path.dirname(__file__)), ".."))
src_dir = os.path.join(root_dir, "src")
log_dir = os.path.join(root_dir, "logs")
EXEC_LOG_FILE_NAME = "exectest.log"


class CliOptionParser:
    @classmethod
    def get_args(cls):
        parser = argparse.ArgumentParser(description="画面テスト実行コマンドです。オプション無しの場合、Edgeを起動後にsrc/test配下のテストコードを全て実行し、HTMLとXLSXで結果を作成します。")
        parser.add_argument("--headless",action="store_true",help="ブラウザを起動しない場合に指定。")
        parser.add_argument("--html",action="store_true",help="htmlの結果だけにしたい場合に指定。")
        parser.add_argument("--xlsx",action="store_true",help="xlsxの結果だけにしたい場合に指定。")
        parser.add_argument("-u","--update",action="store_true",help="DOMスナップショットを更新する場合に指定。")
        parser.add_argument("-l","--legacy",action="store_true",help="IEモードで動作させる場合。ヘッドレスと同時に指定はできません。")
        parser.add_argument("-d","--dir",help="実行対象テストのディレクトリを指定する場合。未指定の場合はtest")
        parser.add_argument("-p","--pattern",help="実行対象テストのファイルを指定する場合。未指定の場合はtest_*.py")
        ret = parser.parse_args()
        # if ret.dir is None:
        #     ret.dir = "test"
        # if ret.pattern is None:
        #     ret.pattern = "test_*.py"

        # オプションのチェック(未実装)
        cls.__check(ret)

        return ret

    @classmethod
    def __check(cls, args):
        # エラーは例外出す ValueErrorあたりで
        if(args.legacy and args.headless):
            raise ValueError("IEモードで動作させる場合、ヘッドレスモードには出来ません。")

class TestEntryPoint:
    def __init__(self, test_args):
        self.test_args = test_args
        self.runner = WebRingsSiteTestRunner()
        
    
    def run_test(self):
        self.start_log()
        tests = self.__test_discover(self.test_args.dir, self.test_args.pattern)
        result = self.runner.run(tests)
        
        html_report = HtmlReportHandler("WebRingsFukushi")
        html_report.make_report(result)

        xlsx_report = XlsxReportHandler()
        xlsx_report.make_report(result)
        self.end_log()


    def __test_discover(self, start_dir="test", pattern="test_*.py"):
        start_dir = "test/qap010_std"
        pattern = "test_QAP010_28020101*.py"
        test_suite = unittest.TestSuite()
        all_test_suite = unittest.defaultTestLoader.discover(start_dir=start_dir, pattern=pattern, top_level_dir=src_dir)
        
        for ts in all_test_suite:  # TODO(nozawa): ソートしておいた方がいいかも
            test_suite.addTest(ts)
        return test_suite
    
    def start_log(self):
        date_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open(os.path.join(log_dir, EXEC_LOG_FILE_NAME), mode="w", encoding="utf-8") as f:
            f.write(f"TEST START {date_str}" + "\n")
    
    def end_log(self):
        date_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open(os.path.join(log_dir, EXEC_LOG_FILE_NAME), mode="a", encoding="utf-8") as f:
            f.write(f"TEST END {date_str}" + "\n")

if __name__ == "__main__":
    
    test_args = CliOptionParser.get_args()
    settings = CommonSettings.setup(test_args)
    
    if test_args.dir is None:
        test_args.dir = settings.default_test_target_dir
    if test_args.pattern is None:
        test_args.pattern = "test_*.py"

    proxy_url = settings.settings_json.get("proxy","")
    if proxy_url != "":
        os.environ["http_proxy"] = proxy_url
        os.environ["https_proxy"] = proxy_url
        os.environ["no_proxy"] = "127.0.0.1,localhost"

    main = TestEntryPoint(test_args)
    main.run_test()


    