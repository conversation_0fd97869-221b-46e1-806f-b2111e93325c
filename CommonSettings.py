import json
import os

class CommonSettings:
    def __init__(self):
        settings_file_path = os.path.abspath(os.path.join(os.path.abspath(os.path.dirname(__file__)), "settings.json"))
        with open(settings_file_path) as f:
            self.settings_json = json.load(f)
        
        self.base_url = self.settings_json["base_url"]
        self.login_url = self.settings_json["login_url"]
        self.user_id = self.settings_json["user_id"]
        self.password = self.settings_json["password"]
        self.user_id_element = self.settings_json["user_id_element"]
        self.password_element = self.settings_json["password_element"]
        self.login_element = self.settings_json["login_element"]
        self.fukushi_btn_element = self.settings_json["fukushi_btn_element"]
        self.drive = self.settings_json["drive"]
        self.path = self.settings_json["path"]

        self.before_log_path = self.settings_json["before_log_path"]
        self.new_log_path = self.settings_json["new_log_path"]

# if __name__ == "__main__":
#     x = CommonSettings()
#     print(x.base_url)